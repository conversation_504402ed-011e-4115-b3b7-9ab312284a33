// automatically generated by the FlatBuffers compiler, do not modify

/* eslint-disable @typescript-eslint/no-unused-vars, @typescript-eslint/no-explicit-any, @typescript-eslint/no-non-null-assertion */

import * as flatbuffers from 'flatbuffers';

import { TensorDataType } from '../../onnxruntime/fbs/tensor-data-type.js';

export class Tensor {
  bb: flatbuffers.ByteBuffer | null = null;
  bb_pos = 0;
  __init(i: number, bb: flatbuffers.ByteBuffer): Tensor {
    this.bb_pos = i;
    this.bb = bb;
    return this;
  }

  static getRootAsTensor(bb: flatbuffers.ByteBuffer, obj?: Tensor): Tensor {
    return (obj || new Tensor()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
  }

  static getSizePrefixedRootAsTensor(bb: flatbuffers.ByteBuffer, obj?: Tensor): Tensor {
    bb.setPosition(bb.position() + flatbuffers.SIZE_PREFIX_LENGTH);
    return (obj || new Tensor()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
  }

  name(): string | null;
  name(optionalEncoding: flatbuffers.Encoding): string | Uint8Array | null;
  name(optionalEncoding?: any): string | Uint8Array | null {
    const offset = this.bb!.__offset(this.bb_pos, 4);
    return offset ? this.bb!.__string(this.bb_pos + offset, optionalEncoding) : null;
  }

  docString(): string | null;
  docString(optionalEncoding: flatbuffers.Encoding): string | Uint8Array | null;
  docString(optionalEncoding?: any): string | Uint8Array | null {
    const offset = this.bb!.__offset(this.bb_pos, 6);
    return offset ? this.bb!.__string(this.bb_pos + offset, optionalEncoding) : null;
  }

  dims(index: number): bigint | null {
    const offset = this.bb!.__offset(this.bb_pos, 8);
    return offset ? this.bb!.readInt64(this.bb!.__vector(this.bb_pos + offset) + index * 8) : BigInt(0);
  }

  dimsLength(): number {
    const offset = this.bb!.__offset(this.bb_pos, 8);
    return offset ? this.bb!.__vector_len(this.bb_pos + offset) : 0;
  }

  dataType(): TensorDataType {
    const offset = this.bb!.__offset(this.bb_pos, 10);
    return offset ? this.bb!.readInt32(this.bb_pos + offset) : TensorDataType.UNDEFINED;
  }

  rawData(index: number): number | null {
    const offset = this.bb!.__offset(this.bb_pos, 12);
    return offset ? this.bb!.readUint8(this.bb!.__vector(this.bb_pos + offset) + index) : 0;
  }

  rawDataLength(): number {
    const offset = this.bb!.__offset(this.bb_pos, 12);
    return offset ? this.bb!.__vector_len(this.bb_pos + offset) : 0;
  }

  rawDataArray(): Uint8Array | null {
    const offset = this.bb!.__offset(this.bb_pos, 12);
    return offset
      ? new Uint8Array(
          this.bb!.bytes().buffer,
          this.bb!.bytes().byteOffset + this.bb!.__vector(this.bb_pos + offset),
          this.bb!.__vector_len(this.bb_pos + offset),
        )
      : null;
  }

  stringData(index: number): string;
  stringData(index: number, optionalEncoding: flatbuffers.Encoding): string | Uint8Array;
  stringData(index: number, optionalEncoding?: any): string | Uint8Array | null {
    const offset = this.bb!.__offset(this.bb_pos, 14);
    return offset ? this.bb!.__string(this.bb!.__vector(this.bb_pos + offset) + index * 4, optionalEncoding) : null;
  }

  stringDataLength(): number {
    const offset = this.bb!.__offset(this.bb_pos, 14);
    return offset ? this.bb!.__vector_len(this.bb_pos + offset) : 0;
  }

  externalDataOffset(): bigint {
    const offset = this.bb!.__offset(this.bb_pos, 16);
    return offset ? this.bb!.readInt64(this.bb_pos + offset) : BigInt('-1');
  }

  static startTensor(builder: flatbuffers.Builder) {
    builder.startObject(7);
  }

  static addName(builder: flatbuffers.Builder, nameOffset: flatbuffers.Offset) {
    builder.addFieldOffset(0, nameOffset, 0);
  }

  static addDocString(builder: flatbuffers.Builder, docStringOffset: flatbuffers.Offset) {
    builder.addFieldOffset(1, docStringOffset, 0);
  }

  static addDims(builder: flatbuffers.Builder, dimsOffset: flatbuffers.Offset) {
    builder.addFieldOffset(2, dimsOffset, 0);
  }

  static createDimsVector(builder: flatbuffers.Builder, data: bigint[]): flatbuffers.Offset {
    builder.startVector(8, data.length, 8);
    for (let i = data.length - 1; i >= 0; i--) {
      builder.addInt64(data[i]!);
    }
    return builder.endVector();
  }

  static startDimsVector(builder: flatbuffers.Builder, numElems: number) {
    builder.startVector(8, numElems, 8);
  }

  static addDataType(builder: flatbuffers.Builder, dataType: TensorDataType) {
    builder.addFieldInt32(3, dataType, TensorDataType.UNDEFINED);
  }

  static addRawData(builder: flatbuffers.Builder, rawDataOffset: flatbuffers.Offset) {
    builder.addFieldOffset(4, rawDataOffset, 0);
  }

  static createRawDataVector(builder: flatbuffers.Builder, data: number[] | Uint8Array): flatbuffers.Offset {
    builder.startVector(1, data.length, 1);
    for (let i = data.length - 1; i >= 0; i--) {
      builder.addInt8(data[i]!);
    }
    return builder.endVector();
  }

  static startRawDataVector(builder: flatbuffers.Builder, numElems: number) {
    builder.startVector(1, numElems, 1);
  }

  static addStringData(builder: flatbuffers.Builder, stringDataOffset: flatbuffers.Offset) {
    builder.addFieldOffset(5, stringDataOffset, 0);
  }

  static createStringDataVector(builder: flatbuffers.Builder, data: flatbuffers.Offset[]): flatbuffers.Offset {
    builder.startVector(4, data.length, 4);
    for (let i = data.length - 1; i >= 0; i--) {
      builder.addOffset(data[i]!);
    }
    return builder.endVector();
  }

  static startStringDataVector(builder: flatbuffers.Builder, numElems: number) {
    builder.startVector(4, numElems, 4);
  }

  static addExternalDataOffset(builder: flatbuffers.Builder, externalDataOffset: bigint) {
    builder.addFieldInt64(6, externalDataOffset, BigInt('-1'));
  }

  static endTensor(builder: flatbuffers.Builder): flatbuffers.Offset {
    const offset = builder.endObject();
    return offset;
  }

  static createTensor(
    builder: flatbuffers.Builder,
    nameOffset: flatbuffers.Offset,
    docStringOffset: flatbuffers.Offset,
    dimsOffset: flatbuffers.Offset,
    dataType: TensorDataType,
    rawDataOffset: flatbuffers.Offset,
    stringDataOffset: flatbuffers.Offset,
    externalDataOffset: bigint,
  ): flatbuffers.Offset {
    Tensor.startTensor(builder);
    Tensor.addName(builder, nameOffset);
    Tensor.addDocString(builder, docStringOffset);
    Tensor.addDims(builder, dimsOffset);
    Tensor.addDataType(builder, dataType);
    Tensor.addRawData(builder, rawDataOffset);
    Tensor.addStringData(builder, stringDataOffset);
    Tensor.addExternalDataOffset(builder, externalDataOffset);
    return Tensor.endTensor(builder);
  }
}
