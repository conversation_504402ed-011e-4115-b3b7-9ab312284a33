// automatically generated by the FlatBuffers compiler, do not modify

/* eslint-disable @typescript-eslint/no-unused-vars, @typescript-eslint/no-explicit-any, @typescript-eslint/no-non-null-assertion */

import { MapType } from '../../onnxruntime/fbs/map-type.js';
import { SequenceType } from '../../onnxruntime/fbs/sequence-type.js';
import { TensorTypeAndShape } from '../../onnxruntime/fbs/tensor-type-and-shape.js';

export enum TypeInfoValue {
  NONE = 0,
  tensor_type = 1,
  sequence_type = 2,
  map_type = 3,
}

export function unionToTypeInfoValue(
  type: TypeInfoValue,
  accessor: (obj: MapType | SequenceType | TensorTypeAndShape) => MapType | SequenceType | TensorTypeAndShape | null,
): MapType | SequenceType | TensorTypeAndShape | null {
  switch (TypeInfoValue[type]) {
    case 'NONE':
      return null;
    case 'tensor_type':
      return accessor(new TensorTypeAndShape())! as TensorTypeAndShape;
    case 'sequence_type':
      return accessor(new SequenceType())! as SequenceType;
    case 'map_type':
      return accessor(new MapType())! as MapType;
    default:
      return null;
  }
}

export function unionListToTypeInfoValue(
  type: TypeInfoValue,
  accessor: (
    index: number,
    obj: MapType | SequenceType | TensorTypeAndShape,
  ) => MapType | SequenceType | TensorTypeAndShape | null,
  index: number,
): MapType | SequenceType | TensorTypeAndShape | null {
  switch (TypeInfoValue[type]) {
    case 'NONE':
      return null;
    case 'tensor_type':
      return accessor(index, new TensorTypeAndShape())! as TensorTypeAndShape;
    case 'sequence_type':
      return accessor(index, new SequenceType())! as SequenceType;
    case 'map_type':
      return accessor(index, new MapType())! as MapType;
    default:
      return null;
  }
}
