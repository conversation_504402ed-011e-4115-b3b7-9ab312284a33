import React, { useState, useCallback } from 'react';
import { Wand2, Loader2, Download, RotateCcw } from 'lucide-react';
import { downloadImage, blobToImageUrl } from '../utils/imageUtils';

const ImageProcessor = ({ selectedImage, onReset }) => {
  const [isProcessing, setIsProcessing] = useState(false);
  const [processedImage, setProcessedImage] = useState(null);
  const [error, setError] = useState('');
  const [progress, setProgress] = useState(0);

  const processImage = useCallback(async () => {
    if (!selectedImage) return;

    setIsProcessing(true);
    setError('');
    setProgress(0);

    try {
      // Importar dinámicamente la biblioteca
      setProgress(10);
      const { removeBackground } = await import('@imgly/background-removal');

      setProgress(20);

      // Crear una imagen más pequeña si es muy grande
      let imageToProcess = selectedImage;
      if (selectedImage.size > 5 * 1024 * 1024) { // Si es mayor a 5MB
        setProgress(30);
        // Redimensionar imagen si es necesario
        const { resizeImage } = await import('../utils/imageUtils');
        imageToProcess = await resizeImage(selectedImage, 1920, 1080, 0.9);
      }

      setProgress(40);

      // Configuración optimizada
      const config = {
        model: 'isnet_fp16', // Modelo más eficiente
        output: {
          format: 'image/png',
          quality: 0.9,
          type: 'foreground'
        },
        device: 'cpu', // Usar CPU para mayor compatibilidad
        debug: false
      };

      setProgress(50);

      // Procesar imagen con IA
      const imageBlob = await removeBackground(imageToProcess, config);

      setProgress(90);

      if (!imageBlob || imageBlob.size === 0) {
        throw new Error('No se pudo procesar la imagen');
      }

      setProgress(100);
      setProcessedImage(imageBlob);

    } catch (err) {
      console.error('Error procesando imagen:', err);

      // Mensajes de error más específicos
      let errorMessage = 'Error al procesar la imagen. ';

      if (err.message.includes('network') || err.message.includes('fetch') || err.message.includes('Failed to fetch')) {
        errorMessage += 'Verifica tu conexión a internet. Los modelos de IA se descargan la primera vez.';
      } else if (err.message.includes('memory') || err.message.includes('size') || err.message.includes('out of memory')) {
        errorMessage += 'La imagen es demasiado grande. Intenta con una imagen más pequeña.';
      } else if (err.message.includes('format') || err.message.includes('decode')) {
        errorMessage += 'Formato de imagen no compatible. Usa JPG, PNG o WebP.';
      } else if (err.message.includes('SharedArrayBuffer')) {
        errorMessage += 'Tu navegador no soporta todas las características necesarias. Intenta con Chrome o Firefox.';
      } else {
        errorMessage += 'Inténtalo de nuevo. Si persiste el error, usa las herramientas básicas.';
      }

      setError(errorMessage);
    } finally {
      setIsProcessing(false);
      setTimeout(() => setProgress(0), 1000);
    }
  }, [selectedImage]);

  const handleDownload = useCallback(() => {
    if (processedImage) {
      const filename = `${selectedImage.name.split('.')[0]}-sin-fondo.png`;
      downloadImage(processedImage, filename);
    }
  }, [processedImage, selectedImage]);

  const handleReset = useCallback(() => {
    setProcessedImage(null);
    setError('');
    setProgress(0);
    onReset();
  }, [onReset]);

  if (!selectedImage) {
    return null;
  }

  return (
    <div className="space-y-6">
      {/* Botones de acción */}
      <div className="glass-effect rounded-xl p-6 animate-fadeInUp">
        <div className="flex flex-wrap gap-4 justify-center">
          {!processedImage && (
            <button
              onClick={processImage}
              disabled={isProcessing}
              className="btn-primary disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
            >
              {isProcessing ? (
                <>
                  <Loader2 className="w-5 h-5 animate-spin" />
                  <span>Procesando...</span>
                </>
              ) : (
                <>
                  <Wand2 className="w-5 h-5" />
                  <span>Eliminar fondo</span>
                </>
              )}
            </button>
          )}
          
          {processedImage && (
            <>
              <button
                onClick={handleDownload}
                className="btn-primary flex items-center space-x-2"
              >
                <Download className="w-5 h-5" />
                <span>Descargar</span>
              </button>
              
              <button
                onClick={handleReset}
                className="btn-secondary flex items-center space-x-2"
              >
                <RotateCcw className="w-5 h-5" />
                <span>Nueva imagen</span>
              </button>
            </>
          )}
        </div>
        
        {/* Barra de progreso */}
        {isProcessing && (
          <div className="mt-4">
            <div className="flex justify-between text-sm text-gray-600 mb-2">
              <span>
                {progress < 30 ? 'Descargando modelos de IA...' :
                 progress < 90 ? 'Procesando imagen...' :
                 'Finalizando...'}
              </span>
              <span>{progress}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="bg-gradient-to-r from-blue-500 to-indigo-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${progress}%` }}
              ></div>
            </div>
            {progress < 30 && (
              <p className="text-xs text-gray-500 mt-2">
                ℹ️ La primera vez puede tardar más mientras se descargan los modelos de IA
              </p>
            )}
          </div>
        )}
        
        {/* Error */}
        {error && (
          <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-red-700 text-sm">{error}</p>
          </div>
        )}
      </div>

      {/* Comparación de imágenes */}
      {processedImage && (
        <div className="glass-effect rounded-xl p-6 animate-fadeInUp">
          <h3 className="text-lg font-semibold text-gray-800 mb-4 text-center">
            Resultado
          </h3>
          
          <div className="grid md:grid-cols-2 gap-6">
            {/* Imagen original */}
            <div>
              <h4 className="text-sm font-medium text-gray-600 mb-2">Original</h4>
              <div className="relative">
                <img
                  src={URL.createObjectURL(selectedImage)}
                  alt="Imagen original"
                  className="w-full h-64 object-contain rounded-lg bg-gray-50 border"
                />
              </div>
            </div>
            
            {/* Imagen procesada */}
            <div>
              <h4 className="text-sm font-medium text-gray-600 mb-2">Sin fondo</h4>
              <div className="relative">
                <img
                  src={blobToImageUrl(processedImage)}
                  alt="Imagen sin fondo"
                  className="w-full h-64 object-contain rounded-lg border"
                  style={{
                    backgroundImage: `
                      linear-gradient(45deg, #f0f0f0 25%, transparent 25%),
                      linear-gradient(-45deg, #f0f0f0 25%, transparent 25%),
                      linear-gradient(45deg, transparent 75%, #f0f0f0 75%),
                      linear-gradient(-45deg, transparent 75%, #f0f0f0 75%)
                    `,
                    backgroundSize: '20px 20px',
                    backgroundPosition: '0 0, 0 10px, 10px -10px, -10px 0px'
                  }}
                />
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ImageProcessor;
