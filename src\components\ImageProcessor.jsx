import React, { useState, useCallback, useRef, useEffect } from 'react';
import { Wand2, Loader2, Download, <PERSON>otate<PERSON>c<PERSON>, <PERSON> } from 'lucide-react';
import { downloadImage, blobToImageUrl } from '../utils/imageUtils';

const ImageProcessor = ({ selectedImage, onReset }) => {
  const [isProcessing, setIsProcessing] = useState(false);
  const [processedImage, setProcessedImage] = useState(null);
  const [error, setError] = useState('');
  const [progress, setProgress] = useState(0);
  const [selfieSegmentation, setSelfieSegmentation] = useState(null);
  const canvasRef = useRef(null);
  const videoRef = useRef(null);

  // Inicializar MediaPipe
  useEffect(() => {
    const initializeMediaPipe = async () => {
      try {
        const { SelfieSegmentation } = await import('@mediapipe/selfie_segmentation');

        const selfieSegmentationInstance = new SelfieSegmentation({
          locateFile: (file) => {
            return `https://cdn.jsdelivr.net/npm/@mediapipe/selfie_segmentation/${file}`;
          }
        });

        selfieSegmentationInstance.setOptions({
          modelSelection: 1, // 0 para general, 1 para landscape
          selfieMode: false,
        });

        setSelfieSegmentation(selfieSegmentationInstance);
      } catch (error) {
        console.error('Error inicializando MediaPipe:', error);
      }
    };

    initializeMediaPipe();
  }, []);

  const processImage = useCallback(async () => {
    if (!selectedImage) return;

    setIsProcessing(true);
    setError('');
    setProgress(0);

    try {
      setProgress(10);

      // Crear canvas y contexto
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();

      setProgress(20);

      // Cargar imagen
      await new Promise((resolve, reject) => {
        img.onload = resolve;
        img.onerror = reject;
        img.src = URL.createObjectURL(selectedImage);
      });

      setProgress(30);

      // Configurar canvas
      canvas.width = img.width;
      canvas.height = img.height;
      ctx.drawImage(img, 0, 0);

      setProgress(40);

      if (selfieSegmentation) {
        // Usar MediaPipe para segmentación
        await processWithMediaPipe(canvas, img);
      } else {
        // Fallback a algoritmo básico mejorado
        await processWithAdvancedAlgorithm(canvas, img);
      }

      setProgress(90);

      // Convertir a blob
      canvas.toBlob((blob) => {
        if (blob) {
          setProcessedImage(blob);
          setProgress(100);
        } else {
          throw new Error('No se pudo generar la imagen procesada');
        }
      }, 'image/png', 0.9);

    } catch (err) {
      console.error('Error procesando imagen:', err);
      setError(`Error al procesar la imagen: ${err.message}. Intenta con los algoritmos avanzados.`);
    } finally {
      setIsProcessing(false);
      setTimeout(() => setProgress(0), 1000);
    }
  }, [selectedImage, selfieSegmentation]);

  const processWithMediaPipe = async (canvas, img) => {
    return new Promise((resolve, reject) => {
      const ctx = canvas.getContext('2d');

      selfieSegmentation.onResults((results) => {
        if (results.segmentationMask) {
          // Crear imagen con fondo transparente usando la máscara
          const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
          const maskCanvas = document.createElement('canvas');
          const maskCtx = maskCanvas.getContext('2d');

          maskCanvas.width = canvas.width;
          maskCanvas.height = canvas.height;
          maskCtx.drawImage(results.segmentationMask, 0, 0, canvas.width, canvas.height);

          const maskData = maskCtx.getImageData(0, 0, canvas.width, canvas.height);

          // Aplicar máscara
          for (let i = 0; i < imageData.data.length; i += 4) {
            const maskValue = maskData.data[i]; // Usar canal rojo como máscara
            imageData.data[i + 3] = maskValue; // Aplicar a canal alpha
          }

          ctx.putImageData(imageData, 0, 0);
          resolve();
        } else {
          reject(new Error('No se pudo generar la máscara de segmentación'));
        }
      });

      // Enviar imagen a MediaPipe
      selfieSegmentation.send({ image: img });
    });
  };

  const processWithAdvancedAlgorithm = async (canvas, img) => {
    const ctx = canvas.getContext('2d');
    const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
    const data = imageData.data;

    // Algoritmo mejorado de eliminación de fondo
    const { width, height } = canvas;

    // 1. Detectar colores de fondo más inteligentemente
    const backgroundColors = detectSmartBackgroundColors(data, width, height);

    // 2. Crear máscara inicial
    const mask = createSmartMask(data, width, height, backgroundColors);

    // 3. Aplicar máscara con suavizado
    for (let i = 0; i < data.length; i += 4) {
      const pixelIndex = Math.floor(i / 4);
      data[i + 3] = mask[pixelIndex]; // Aplicar transparencia
    }

    ctx.putImageData(imageData, 0, 0);
  };

  // Funciones auxiliares para algoritmos mejorados
  const detectSmartBackgroundColors = (data, width, height) => {
    const colors = [];
    const sampleSize = 20; // Muestrear cada 20 píxeles

    // Muestrear bordes de la imagen
    for (let x = 0; x < width; x += sampleSize) {
      for (let y = 0; y < height; y += sampleSize) {
        if (x < 50 || x > width - 50 || y < 50 || y > height - 50) {
          const idx = (y * width + x) * 4;
          if (idx < data.length) {
            colors.push([data[idx], data[idx + 1], data[idx + 2]]);
          }
        }
      }
    }

    // Agrupar colores similares
    const groupedColors = [];
    const tolerance = 40;

    for (const color of colors) {
      let found = false;
      for (const group of groupedColors) {
        if (colorDistance(color, group.color) < tolerance) {
          group.count++;
          found = true;
          break;
        }
      }
      if (!found) {
        groupedColors.push({ color, count: 1 });
      }
    }

    // Retornar los colores más comunes
    return groupedColors
      .sort((a, b) => b.count - a.count)
      .slice(0, 3)
      .map(g => g.color);
  };

  const createSmartMask = (data, width, height, backgroundColors) => {
    const mask = new Uint8Array(width * height);
    const tolerance = 35;

    for (let i = 0; i < width * height; i++) {
      const idx = i * 4;
      const r = data[idx];
      const g = data[idx + 1];
      const b = data[idx + 2];

      let isBackground = false;
      for (const bgColor of backgroundColors) {
        if (colorDistance([r, g, b], bgColor) < tolerance) {
          isBackground = true;
          break;
        }
      }

      // Aplicar suavizado en los bordes
      const x = i % width;
      const y = Math.floor(i / width);
      const edgeDistance = Math.min(x, y, width - x - 1, height - y - 1);

      if (isBackground) {
        mask[i] = Math.max(0, 255 - (edgeDistance * 10)); // Fade hacia los bordes
      } else {
        mask[i] = Math.min(255, 200 + (edgeDistance * 5)); // Más opaco hacia el centro
      }
    }

    return mask;
  };

  const colorDistance = (color1, color2) => {
    const [r1, g1, b1] = color1;
    const [r2, g2, b2] = color2;
    return Math.sqrt((r1 - r2) ** 2 + (g1 - g2) ** 2 + (b1 - b2) ** 2);
  };

  const handleDownload = useCallback(() => {
    if (processedImage) {
      const filename = `${selectedImage.name.split('.')[0]}-sin-fondo.png`;
      downloadImage(processedImage, filename);
    }
  }, [processedImage, selectedImage]);

  const handleReset = useCallback(() => {
    setProcessedImage(null);
    setError('');
    setProgress(0);
    onReset();
  }, [onReset]);

  if (!selectedImage) {
    return null;
  }

  return (
    <div className="space-y-6">
      {/* Botones de acción */}
      <div className="glass-effect rounded-xl p-6 animate-fadeInUp">
        <div className="flex flex-wrap gap-4 justify-center">
          {!processedImage && (
            <button
              onClick={processImage}
              disabled={isProcessing}
              className="btn-primary disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
            >
              {isProcessing ? (
                <>
                  <Loader2 className="w-5 h-5 animate-spin" />
                  <span>Procesando...</span>
                </>
              ) : (
                <>
                  <Brain className="w-5 h-5" />
                  <span>{selfieSegmentation ? 'IA MediaPipe' : 'Algoritmo Inteligente'}</span>
                </>
              )}
            </button>
          )}
          
          {processedImage && (
            <>
              <button
                onClick={handleDownload}
                className="btn-primary flex items-center space-x-2"
              >
                <Download className="w-5 h-5" />
                <span>Descargar</span>
              </button>
              
              <button
                onClick={handleReset}
                className="btn-secondary flex items-center space-x-2"
              >
                <RotateCcw className="w-5 h-5" />
                <span>Nueva imagen</span>
              </button>
            </>
          )}
        </div>
        
        {/* Barra de progreso */}
        {isProcessing && (
          <div className="mt-4">
            <div className="flex justify-between text-sm text-gray-600 mb-2">
              <span>
                {progress < 30 ? 'Descargando modelos de IA...' :
                 progress < 90 ? 'Procesando imagen...' :
                 'Finalizando...'}
              </span>
              <span>{progress}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="bg-gradient-to-r from-blue-500 to-indigo-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${progress}%` }}
              ></div>
            </div>
            {progress < 30 && (
              <p className="text-xs text-gray-500 mt-2">
                ℹ️ La primera vez puede tardar más mientras se descargan los modelos de IA
              </p>
            )}
          </div>
        )}
        
        {/* Error */}
        {error && (
          <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-red-700 text-sm">{error}</p>
          </div>
        )}
      </div>

      {/* Comparación de imágenes */}
      {processedImage && (
        <div className="glass-effect rounded-xl p-6 animate-fadeInUp">
          <h3 className="text-lg font-semibold text-gray-800 mb-4 text-center">
            Resultado
          </h3>
          
          <div className="grid md:grid-cols-2 gap-6">
            {/* Imagen original */}
            <div>
              <h4 className="text-sm font-medium text-gray-600 mb-2">Original</h4>
              <div className="relative">
                <img
                  src={URL.createObjectURL(selectedImage)}
                  alt="Imagen original"
                  className="w-full h-64 object-contain rounded-lg bg-gray-50 border"
                />
              </div>
            </div>
            
            {/* Imagen procesada */}
            <div>
              <h4 className="text-sm font-medium text-gray-600 mb-2">Sin fondo</h4>
              <div className="relative">
                <img
                  src={blobToImageUrl(processedImage)}
                  alt="Imagen sin fondo"
                  className="w-full h-64 object-contain rounded-lg border"
                  style={{
                    backgroundImage: `
                      linear-gradient(45deg, #f0f0f0 25%, transparent 25%),
                      linear-gradient(-45deg, #f0f0f0 25%, transparent 25%),
                      linear-gradient(45deg, transparent 75%, #f0f0f0 75%),
                      linear-gradient(-45deg, transparent 75%, #f0f0f0 75%)
                    `,
                    backgroundSize: '20px 20px',
                    backgroundPosition: '0 0, 0 10px, 10px -10px, -10px 0px'
                  }}
                />
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ImageProcessor;
