{"version": 3, "file": "session.js", "sourceRoot": "", "sources": ["session.ts"], "names": [], "mappings": ";AAAA,4DAA4D;AAC5D,kCAAkC;;;AAElC,uCAA+D;AAC/D,qDAAiD;AAEjD,6CAAwC;AACxC,mCAAgC;AAiBhC,MAAa,OAAO;IAClB,YAAY,SAAyB,EAAE;QACrC,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;QAC1B,IAAI,CAAC,WAAW,GAAG,MAAM,CAAC,WAAW,CAAC;QACtC,IAAI,CAAC,QAAQ,GAAG,qBAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QACjD,IAAI,CAAC,OAAO,GAAG,EAAE,QAAQ,EAAE,IAAI,CAAC,QAAQ,EAAE,eAAe,EAAE,EAAE,EAAE,cAAc,EAAE,EAAE,EAAE,CAAC;IACtF,CAAC;IAED,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,aAAa,EAAE,CAAC;IAC3C,CAAC;IACD,IAAI,WAAW;QACb,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,cAAc,EAAE,CAAC;IAC5C,CAAC;IAED,cAAc;QACZ,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;IACxB,CAAC;IAED,YAAY;QACV,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;IACvB,CAAC;IAKD,KAAK,CAAC,SAAS,CAAC,GAAsC,EAAE,UAAmB,EAAE,MAAe;QAC1F,MAAM,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,SAAS,EAAE,mBAAmB,EAAE,KAAK,IAAI,EAAE;YACnE,sCAAsC;YACtC,MAAM,OAAO,GAAG,MAAM,IAAA,wBAAc,EAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YACvD,IAAI,CAAC,cAAc,GAAG,OAAO,CAAC,oBAAoB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAEjE,IAAI,CAAC,MAAM,GAAG,IAAI,aAAK,EAAE,CAAC;YAC1B,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;gBAC3B,MAAM,WAAW,GAAG,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;gBACzC,IAAI,OAAO,OAAO,KAAK,WAAW,IAAI,OAAO,CAAC,QAAQ,IAAI,OAAO,CAAC,QAAQ,CAAC,IAAI,EAAE;oBAC/E,OAAO;oBACP,MAAM,EAAE,QAAQ,EAAE,GAAG,OAAO,CAAC,kBAAkB,CAAC,CAAC;oBACjD,MAAM,GAAG,GAAG,MAAM,QAAQ,CAAC,GAAG,CAAC,CAAC;oBAChC,IAAI,CAAC,UAAU,CAAC,GAAG,EAAE,WAAW,CAAC,CAAC;iBACnC;qBAAM;oBACL,UAAU;oBACV,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,CAAC,CAAC;oBAClC,MAAM,GAAG,GAAG,MAAM,QAAQ,CAAC,WAAW,EAAE,CAAC;oBACzC,IAAI,CAAC,UAAU,CAAC,IAAI,UAAU,CAAC,GAAG,CAAC,EAAE,WAAW,CAAC,CAAC;iBACnD;aACF;iBAAM,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE;gBACnC,8BAA8B;gBAC9B,MAAM,GAAG,GAAG,IAAI,UAAU,CAAC,GAAG,EAAE,UAAU,IAAI,CAAC,EAAE,MAAM,IAAI,GAAG,CAAC,UAAU,CAAC,CAAC;gBAC3E,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;aACtB;iBAAM;gBACL,6BAA6B;gBAC7B,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;aACtB;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,UAAU,CAAC,cAA0B,EAAE,WAAqB;QAClE,IAAI,IAAI,CAAC,YAAY,EAAE;YACrB,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;SACxC;QAED,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,SAAS,EAAE,oBAAoB,EAAE,GAAG,EAAE;YACxD,aAAa;YACb,MAAM,gBAAgB,GAAG,IAAI,CAAC,cAAc,CAAC,cAAc;gBACzD,CAAC,CAAE,IAAI,CAAC,cAAoC;gBAC5C,CAAC,CAAC,SAAS,CAAC;YACd,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,gBAAgB,EAAE,WAAW,CAAC,CAAC;YAEhE,mFAAmF;YACnF,IAAI,IAAI,CAAC,cAAc,CAAC,kBAAkB,EAAE;gBAC1C,IAAI,CAAC,cAAc,CAAC,kBAAkB,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;aAC3D;YACD,wCAAwC;YACxC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAEtC,uEAAuE;YACvE,IAAI,CAAC,cAAc,GAAG,IAAI,8BAAa,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;QACvF,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;IAC3B,CAAC;IAED,KAAK,CAAC,GAAG,CAAC,MAAsC;QAC9C,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;YACtB,MAAM,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC;SAChD;QAED,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,SAAS,EAAE,aAAa,EAAE,KAAK,IAAI,EAAE;YAC9D,MAAM,YAAY,GAAG,IAAI,CAAC,0BAA0B,CAAC,MAAM,CAAC,CAAC;YAE7D,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,IAAI,CAAC,cAAc,EAAE,YAAY,CAAC,CAAC;YAE3F,OAAO,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,0BAA0B,CAAC,MAAsC;QACvE,MAAM,eAAe,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,aAAa,EAAE,CAAC;QAE1D,mBAAmB;QACnB,mBAAmB;QACnB,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;YACzB,IAAI,MAAM,CAAC,MAAM,KAAK,eAAe,CAAC,MAAM,EAAE;gBAC5C,MAAM,IAAI,KAAK,CAAC,0CAA0C,eAAe,CAAC,MAAM,YAAY,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;aAC9G;SACF;QACD,uBAAuB;QACvB,8BAA8B;aACzB;YACH,IAAI,MAAM,CAAC,IAAI,KAAK,eAAe,CAAC,MAAM,EAAE;gBAC1C,MAAM,IAAI,KAAK,CAAC,sCAAsC,eAAe,CAAC,MAAM,YAAY,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC;aACxG;YAED,MAAM,YAAY,GAAG,IAAI,KAAK,CAAS,MAAM,CAAC,IAAI,CAAC,CAAC;YACpD,IAAI,iBAAiB,GAAG,CAAC,CAAC;YAC1B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,eAAe,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;gBAC/C,MAAM,MAAM,GAAG,MAAM,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC9C,IAAI,CAAC,MAAM,EAAE;oBACX,MAAM,IAAI,KAAK,CAAC,8BAA8B,IAAI,GAAG,CAAC,CAAC;iBACxD;gBACD,YAAY,CAAC,iBAAiB,EAAE,CAAC,GAAG,MAAM,CAAC;aAC5C;YAED,MAAM,GAAG,YAAY,CAAC;SACvB;QAED,6BAA6B;QAC7B,qEAAqE;QACrE,IACE,CAAC,IAAI,CAAC,OAAO,CAAC,eAAe;YAC7B,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,MAAM,KAAK,CAAC;YACzC,CAAC,IAAI,CAAC,OAAO,CAAC,cAAc;YAC5B,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,MAAM,KAAK,CAAC,EACxC;YACA,MAAM,iBAAiB,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,eAAe,EAAE,CAAC;YAC9D,MAAM,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC;YAElD,MAAM,cAAc,GAAG,IAAI,KAAK,CAAoB,iBAAiB,CAAC,MAAM,CAAC,CAAC;YAE9E,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,iBAAiB,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;gBACjD,MAAM,UAAU,GAAG,WAAW,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC;gBACrD,cAAc,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,IAAK,CAAC,KAAK,CAAC,IAAI,CAAC;gBAEhD,yCAAyC;gBACzC,qGAAqG;gBACrG,IAAI,CAAC,OAAO,CAAC,eAAgB,CAAC,IAAI,CAAC,UAAU,CAAC,IAAK,CAAC,UAAU,CAAC,CAAC;gBAChE,IAAI,CAAC,OAAO,CAAC,cAAe,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;aACnD;YAED,IAAI,CAAC,uBAAuB,CAAC,cAAc,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;SAC5D;QAED,kFAAkF;aAC7E;YACH,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;SAC1E;QAED,6BAA6B;QAC7B,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,OAAO,CAAC,eAAgB,EAAE,MAAM,CAAC,CAAC;QAErE,OAAO,MAAM,CAAC;IAChB,CAAC;IAEO,wBAAwB,CAAC,eAAkC,EAAE,WAAqB;QACxF,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC3C,MAAM,YAAY,GAAG,eAAe,CAAC,CAAC,CAAC,CAAC;YACxC,MAAM,UAAU,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;YACvC,IAAI,YAAY,KAAK,UAAU,EAAE;gBAC/B,MAAM,IAAI,KAAK,CAAC,gBAAgB,CAAC,kCAAkC,YAAY,aAAa,UAAU,EAAE,CAAC,CAAC;aAC3G;SACF;IACH,CAAC;IAEO,uBAAuB,CAC7B,cAAwC,EACxC,WAAqB,EACrB,gBAAyB;QAEzB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC3C,MAAM,YAAY,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC;YACvC,MAAM,UAAU,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;YACvC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,YAAY,EAAE,UAAU,EAAE,gBAAgB,CAAC,EAAE;gBACvE,MAAM,IAAI,KAAK,CACb,gBAAgB,CAAC,oCAAoC,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,eAAe,UAAU,CAAC,IAAI,CACvG,GAAG,CACJ,GAAG,CACL,CAAC;aACH;SACF;IACH,CAAC;IAEO,iBAAiB,CACvB,YAA+B,EAC/B,UAA6B,EAC7B,gBAAyB;QAEzB,IAAI,YAAY,CAAC,MAAM,KAAK,UAAU,CAAC,MAAM,EAAE;YAC7C,OAAO,KAAK,CAAC;SACd;QAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;YAC5C,IAAI,YAAY,CAAC,CAAC,CAAC,KAAK,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,gBAAgB,IAAI,YAAY,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE;gBACrF,mDAAmD;gBACnD,OAAO,KAAK,CAAC;aACd;SACF;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAEO,YAAY,CAAC,aAAuB;QAC1C,MAAM,gBAAgB,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,cAAc,EAAE,CAAC;QAC5D,IAAI,aAAa,CAAC,MAAM,KAAK,gBAAgB,CAAC,MAAM,EAAE;YACpD,MAAM,IAAI,KAAK,CAAC,qEAAqE,CAAC,CAAC;SACxF;QAED,MAAM,MAAM,GAAG,IAAI,GAAG,EAAkB,CAAC;QACzC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,gBAAgB,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;YAChD,MAAM,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC,CAAC,EAAE,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC;SACnD;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAEO,aAAa,CAAC,KAAY;QAChC,MAAM,KAAK,GAAG,KAAK,CAAC,QAAQ,EAAE,CAAC;QAC/B,IAAI,CAAC,IAAI,GAAG,IAAI,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;QAEpC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACrC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;SACjF;IACH,CAAC;CAaF;AArPD,0BAqPC"}