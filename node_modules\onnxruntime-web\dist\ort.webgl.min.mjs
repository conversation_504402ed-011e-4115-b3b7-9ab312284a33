/*!
 * ONNX Runtime Web v1.21.0-dev.20250206-d981b153d3
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */
var hd=Object.create;var _n=Object.defineProperty;var md=Object.getOwnPropertyDescriptor;var bd=Object.getOwnPropertyNames;var gd=Object.getPrototypeOf,yd=Object.prototype.hasOwnProperty;var y=(r,t)=>()=>(r&&(t=r(r=0)),t);var P=(r,t)=>()=>(t||r((t={exports:{}}).exports,t),t.exports),oa=(r,t)=>{for(var n in t)_n(r,n,{get:t[n],enumerable:!0})},ia=(r,t,n,e)=>{if(t&&typeof t=="object"||typeof t=="function")for(let o of bd(t))!yd.call(r,o)&&o!==n&&_n(r,o,{get:()=>t[o],enumerable:!(e=md(t,o))||e.enumerable});return r};var E=(r,t,n)=>(n=r!=null?hd(gd(r)):{},ia(t||!r||!r.__esModule?_n(n,"default",{value:r,enumerable:!0}):n,r)),Td=r=>ia(_n({},"__esModule",{value:!0}),r);var xn,Oe,vn,_d,aa,Er=y(()=>{"use strict";xn=new Map,Oe=[],vn=(r,t,n)=>{if(t&&typeof t.init=="function"&&typeof t.createInferenceSessionHandler=="function"){let e=xn.get(r);if(e===void 0)xn.set(r,{backend:t,priority:n});else{if(e.priority>n)return;if(e.priority===n&&e.backend!==t)throw new Error(`cannot register backend "${r}" using priority ${n}`)}if(n>=0){let o=Oe.indexOf(r);o!==-1&&Oe.splice(o,1);for(let i=0;i<Oe.length;i++)if(xn.get(Oe[i]).priority<=n){Oe.splice(i,0,r);return}Oe.push(r)}return}throw new TypeError("not a valid backend")},_d=async r=>{let t=xn.get(r);if(!t)return"backend not found.";if(t.initialized)return t.backend;if(t.aborted)return t.error;{let n=!!t.initPromise;try{return n||(t.initPromise=t.backend.init(r)),await t.initPromise,t.initialized=!0,t.backend}catch(e){return n||(t.error=`${e}`,t.aborted=!0),t.error}finally{delete t.initPromise}}},aa=async r=>{let t=r.executionProviders||[],n=t.map(u=>typeof u=="string"?u:u.name),e=n.length===0?Oe:n,o,i=[],a=new Set;for(let u of e){let l=await _d(u);typeof l=="string"?i.push({name:u,err:l}):(o||(o=l),o===l&&a.add(u))}if(!o)throw new Error(`no available backend found. ERR: ${i.map(u=>`[${u.name}] ${u.err}`).join(", ")}`);for(let{name:u,err:l}of i)n.includes(u)&&console.warn(`removing requested execution provider "${u}" from session options because it is not available: ${l}`);let s=t.filter(u=>a.has(typeof u=="string"?u:u.name));return[o,new Proxy(r,{get:(u,l)=>l==="executionProviders"?s:Reflect.get(u,l)})]}});var sa=y(()=>{"use strict";Er()});var ua,la=y(()=>{"use strict";ua="1.21.0-dev.20250206-d981b153d3"});var ca,ft,Dr=y(()=>{"use strict";la();ca="warning",ft={wasm:{},webgl:{},webgpu:{},versions:{common:ua},set logLevel(r){if(r!==void 0){if(typeof r!="string"||["verbose","info","warning","error","fatal"].indexOf(r)===-1)throw new Error(`Unsupported logging level: ${r}`);ca=r}},get logLevel(){return ca}};Object.defineProperty(ft,"logLevel",{enumerable:!0})});var K,fa=y(()=>{"use strict";Dr();K=ft});var da,pa,ha=y(()=>{"use strict";da=(r,t)=>{let n=typeof document<"u"?document.createElement("canvas"):new OffscreenCanvas(1,1);n.width=r.dims[3],n.height=r.dims[2];let e=n.getContext("2d");if(e!=null){let o,i;t?.tensorLayout!==void 0&&t.tensorLayout==="NHWC"?(o=r.dims[2],i=r.dims[3]):(o=r.dims[3],i=r.dims[2]);let a=t?.format!==void 0?t.format:"RGB",s=t?.norm,u,l;s===void 0||s.mean===void 0?u=[255,255,255,255]:typeof s.mean=="number"?u=[s.mean,s.mean,s.mean,s.mean]:(u=[s.mean[0],s.mean[1],s.mean[2],0],s.mean[3]!==void 0&&(u[3]=s.mean[3])),s===void 0||s.bias===void 0?l=[0,0,0,0]:typeof s.bias=="number"?l=[s.bias,s.bias,s.bias,s.bias]:(l=[s.bias[0],s.bias[1],s.bias[2],0],s.bias[3]!==void 0&&(l[3]=s.bias[3]));let c=i*o,d=0,p=c,m=c*2,b=-1;a==="RGBA"?(d=0,p=c,m=c*2,b=c*3):a==="RGB"?(d=0,p=c,m=c*2):a==="RBG"&&(d=0,m=c,p=c*2);for(let g=0;g<i;g++)for(let x=0;x<o;x++){let w=(r.data[d++]-l[0])*u[0],I=(r.data[p++]-l[1])*u[1],D=(r.data[m++]-l[2])*u[2],z=b===-1?255:(r.data[b++]-l[3])*u[3];e.fillStyle="rgba("+w+","+I+","+D+","+z+")",e.fillRect(x,g,1,1)}if("toDataURL"in n)return n.toDataURL();throw new Error("toDataURL is not supported")}else throw new Error("Can not access image data")},pa=(r,t)=>{let n=typeof document<"u"?document.createElement("canvas").getContext("2d"):new OffscreenCanvas(1,1).getContext("2d"),e;if(n!=null){let o,i,a;t?.tensorLayout!==void 0&&t.tensorLayout==="NHWC"?(o=r.dims[2],i=r.dims[1],a=r.dims[3]):(o=r.dims[3],i=r.dims[2],a=r.dims[1]);let s=t!==void 0&&t.format!==void 0?t.format:"RGB",u=t?.norm,l,c;u===void 0||u.mean===void 0?l=[255,255,255,255]:typeof u.mean=="number"?l=[u.mean,u.mean,u.mean,u.mean]:(l=[u.mean[0],u.mean[1],u.mean[2],255],u.mean[3]!==void 0&&(l[3]=u.mean[3])),u===void 0||u.bias===void 0?c=[0,0,0,0]:typeof u.bias=="number"?c=[u.bias,u.bias,u.bias,u.bias]:(c=[u.bias[0],u.bias[1],u.bias[2],0],u.bias[3]!==void 0&&(c[3]=u.bias[3]));let d=i*o;if(t!==void 0&&(t.format!==void 0&&a===4&&t.format!=="RGBA"||a===3&&t.format!=="RGB"&&t.format!=="BGR"))throw new Error("Tensor format doesn't match input tensor dims");let p=4,m=0,b=1,g=2,x=3,w=0,I=d,D=d*2,z=-1;s==="RGBA"?(w=0,I=d,D=d*2,z=d*3):s==="RGB"?(w=0,I=d,D=d*2):s==="RBG"&&(w=0,D=d,I=d*2),e=n.createImageData(o,i);for(let F=0;F<i*o;m+=p,b+=p,g+=p,x+=p,F++)e.data[m]=(r.data[w++]-c[0])*l[0],e.data[b]=(r.data[I++]-c[1])*l[1],e.data[g]=(r.data[D++]-c[2])*l[2],e.data[x]=z===-1?255:(r.data[z++]-c[3])*l[3]}else throw new Error("Can not access image data");return e}});var Lr,ma,ba,ga,ya,Ta,_a=y(()=>{"use strict";wn();Lr=(r,t)=>{if(r===void 0)throw new Error("Image buffer must be defined");if(t.height===void 0||t.width===void 0)throw new Error("Image height and width must be defined");if(t.tensorLayout==="NHWC")throw new Error("NHWC Tensor layout is not supported yet");let{height:n,width:e}=t,o=t.norm??{mean:255,bias:0},i,a;typeof o.mean=="number"?i=[o.mean,o.mean,o.mean,o.mean]:i=[o.mean[0],o.mean[1],o.mean[2],o.mean[3]??255],typeof o.bias=="number"?a=[o.bias,o.bias,o.bias,o.bias]:a=[o.bias[0],o.bias[1],o.bias[2],o.bias[3]??0];let s=t.format!==void 0?t.format:"RGBA",u=t.tensorFormat!==void 0&&t.tensorFormat!==void 0?t.tensorFormat:"RGB",l=n*e,c=u==="RGBA"?new Float32Array(l*4):new Float32Array(l*3),d=4,p=0,m=1,b=2,g=3,x=0,w=l,I=l*2,D=-1;s==="RGB"&&(d=3,p=0,m=1,b=2,g=-1),u==="RGBA"?D=l*3:u==="RBG"?(x=0,I=l,w=l*2):u==="BGR"&&(I=0,w=l,x=l*2);for(let F=0;F<l;F++,p+=d,b+=d,m+=d,g+=d)c[x++]=(r[p]+a[0])/i[0],c[w++]=(r[m]+a[1])/i[1],c[I++]=(r[b]+a[2])/i[2],D!==-1&&g!==-1&&(c[D++]=(r[g]+a[3])/i[3]);return u==="RGBA"?new et("float32",c,[1,4,n,e]):new et("float32",c,[1,3,n,e])},ma=async(r,t)=>{let n=typeof HTMLImageElement<"u"&&r instanceof HTMLImageElement,e=typeof ImageData<"u"&&r instanceof ImageData,o=typeof ImageBitmap<"u"&&r instanceof ImageBitmap,i=typeof r=="string",a,s=t??{},u=()=>{if(typeof document<"u")return document.createElement("canvas");if(typeof OffscreenCanvas<"u")return new OffscreenCanvas(1,1);throw new Error("Canvas is not supported")},l=c=>typeof HTMLCanvasElement<"u"&&c instanceof HTMLCanvasElement||c instanceof OffscreenCanvas?c.getContext("2d"):null;if(n){let c=u();c.width=r.width,c.height=r.height;let d=l(c);if(d!=null){let p=r.height,m=r.width;if(t!==void 0&&t.resizedHeight!==void 0&&t.resizedWidth!==void 0&&(p=t.resizedHeight,m=t.resizedWidth),t!==void 0){if(s=t,t.tensorFormat!==void 0)throw new Error("Image input config format must be RGBA for HTMLImageElement");s.tensorFormat="RGBA",s.height=p,s.width=m}else s.tensorFormat="RGBA",s.height=p,s.width=m;d.drawImage(r,0,0),a=d.getImageData(0,0,m,p).data}else throw new Error("Can not access image data")}else if(e){let c,d;if(t!==void 0&&t.resizedWidth!==void 0&&t.resizedHeight!==void 0?(c=t.resizedHeight,d=t.resizedWidth):(c=r.height,d=r.width),t!==void 0&&(s=t),s.format="RGBA",s.height=c,s.width=d,t!==void 0){let p=u();p.width=d,p.height=c;let m=l(p);if(m!=null)m.putImageData(r,0,0),a=m.getImageData(0,0,d,c).data;else throw new Error("Can not access image data")}else a=r.data}else if(o){if(t===void 0)throw new Error("Please provide image config with format for Imagebitmap");let c=u();c.width=r.width,c.height=r.height;let d=l(c);if(d!=null){let p=r.height,m=r.width;return d.drawImage(r,0,0,m,p),a=d.getImageData(0,0,m,p).data,s.height=p,s.width=m,Lr(a,s)}else throw new Error("Can not access image data")}else{if(i)return new Promise((c,d)=>{let p=u(),m=l(p);if(!r||!m)return d();let b=new Image;b.crossOrigin="Anonymous",b.src=r,b.onload=()=>{p.width=b.width,p.height=b.height,m.drawImage(b,0,0,p.width,p.height);let g=m.getImageData(0,0,p.width,p.height);s.height=p.height,s.width=p.width,c(Lr(g.data,s))}});throw new Error("Input data provided is not supported - aborted tensor creation")}if(a!==void 0)return Lr(a,s);throw new Error("Input data provided is not supported - aborted tensor creation")},ba=(r,t)=>{let{width:n,height:e,download:o,dispose:i}=t,a=[1,e,n,4];return new et({location:"texture",type:"float32",texture:r,dims:a,download:o,dispose:i})},ga=(r,t)=>{let{dataType:n,dims:e,download:o,dispose:i}=t;return new et({location:"gpu-buffer",type:n??"float32",gpuBuffer:r,dims:e,download:o,dispose:i})},ya=(r,t)=>{let{dataType:n,dims:e,download:o,dispose:i}=t;return new et({location:"ml-tensor",type:n??"float32",mlTensor:r,dims:e,download:o,dispose:i})},Ta=(r,t,n)=>new et({location:"cpu-pinned",type:r,data:t,dims:n??[t.length]})});var Pe,Ye,xa,va,wa=y(()=>{"use strict";Pe=new Map([["float32",Float32Array],["uint8",Uint8Array],["int8",Int8Array],["uint16",Uint16Array],["int16",Int16Array],["int32",Int32Array],["bool",Uint8Array],["float64",Float64Array],["uint32",Uint32Array],["int4",Uint8Array],["uint4",Uint8Array]]),Ye=new Map([[Float32Array,"float32"],[Uint8Array,"uint8"],[Int8Array,"int8"],[Uint16Array,"uint16"],[Int16Array,"int16"],[Int32Array,"int32"],[Float64Array,"float64"],[Uint32Array,"uint32"]]),xa=!1,va=()=>{if(!xa){xa=!0;let r=typeof BigInt64Array<"u"&&BigInt64Array.from,t=typeof BigUint64Array<"u"&&BigUint64Array.from,n=typeof Float16Array<"u"&&Float16Array.from;r&&(Pe.set("int64",BigInt64Array),Ye.set(BigInt64Array,"int64")),t&&(Pe.set("uint64",BigUint64Array),Ye.set(BigUint64Array,"uint64")),n?(Pe.set("float16",Float16Array),Ye.set(Float16Array,"float16")):Pe.set("float16",Uint16Array)}}});var Ia,Oa,Pa=y(()=>{"use strict";wn();Ia=r=>{let t=1;for(let n=0;n<r.length;n++){let e=r[n];if(typeof e!="number"||!Number.isSafeInteger(e))throw new TypeError(`dims[${n}] must be an integer, got: ${e}`);if(e<0)throw new RangeError(`dims[${n}] must be a non-negative integer, got: ${e}`);t*=e}return t},Oa=(r,t)=>{switch(r.location){case"cpu":return new et(r.type,r.data,t);case"cpu-pinned":return new et({location:"cpu-pinned",data:r.data,type:r.type,dims:t});case"texture":return new et({location:"texture",texture:r.texture,type:r.type,dims:t});case"gpu-buffer":return new et({location:"gpu-buffer",gpuBuffer:r.gpuBuffer,type:r.type,dims:t});case"ml-tensor":return new et({location:"ml-tensor",mlTensor:r.mlTensor,type:r.type,dims:t});default:throw new Error(`tensorReshape: tensor location ${r.location} is not supported`)}}});var et,wn=y(()=>{"use strict";ha();_a();wa();Pa();et=class{constructor(t,n,e){va();let o,i;if(typeof t=="object"&&"location"in t)switch(this.dataLocation=t.location,o=t.type,i=t.dims,t.location){case"cpu-pinned":{let s=Pe.get(o);if(!s)throw new TypeError(`unsupported type "${o}" to create tensor from pinned buffer`);if(!(t.data instanceof s))throw new TypeError(`buffer should be of type ${s.name}`);this.cpuData=t.data;break}case"texture":{if(o!=="float32")throw new TypeError(`unsupported type "${o}" to create tensor from texture`);this.gpuTextureData=t.texture,this.downloader=t.download,this.disposer=t.dispose;break}case"gpu-buffer":{if(o!=="float32"&&o!=="float16"&&o!=="int32"&&o!=="int64"&&o!=="uint32"&&o!=="uint8"&&o!=="bool"&&o!=="uint4"&&o!=="int4")throw new TypeError(`unsupported type "${o}" to create tensor from gpu buffer`);this.gpuBufferData=t.gpuBuffer,this.downloader=t.download,this.disposer=t.dispose;break}case"ml-tensor":{if(o!=="float32"&&o!=="float16"&&o!=="int32"&&o!=="int64"&&o!=="uint32"&&o!=="uint64"&&o!=="int8"&&o!=="uint8"&&o!=="bool"&&o!=="uint4"&&o!=="int4")throw new TypeError(`unsupported type "${o}" to create tensor from MLTensor`);this.mlTensorData=t.mlTensor,this.downloader=t.download,this.disposer=t.dispose;break}default:throw new Error(`Tensor constructor: unsupported location '${this.dataLocation}'`)}else{let s,u;if(typeof t=="string")if(o=t,u=e,t==="string"){if(!Array.isArray(n))throw new TypeError("A string tensor's data must be a string array.");s=n}else{let l=Pe.get(t);if(l===void 0)throw new TypeError(`Unsupported tensor type: ${t}.`);if(Array.isArray(n)){if(t==="float16"&&l===Uint16Array||t==="uint4"||t==="int4")throw new TypeError(`Creating a ${t} tensor from number array is not supported. Please use ${l.name} as data.`);t==="uint64"||t==="int64"?s=l.from(n,BigInt):s=l.from(n)}else if(n instanceof l)s=n;else if(n instanceof Uint8ClampedArray)if(t==="uint8")s=Uint8Array.from(n);else throw new TypeError("A Uint8ClampedArray tensor's data must be type of uint8");else throw new TypeError(`A ${o} tensor's data must be type of ${l}`)}else if(u=n,Array.isArray(t)){if(t.length===0)throw new TypeError("Tensor type cannot be inferred from an empty array.");let l=typeof t[0];if(l==="string")o="string",s=t;else if(l==="boolean")o="bool",s=Uint8Array.from(t);else throw new TypeError(`Invalid element type of data array: ${l}.`)}else if(t instanceof Uint8ClampedArray)o="uint8",s=Uint8Array.from(t);else{let l=Ye.get(t.constructor);if(l===void 0)throw new TypeError(`Unsupported type for tensor data: ${t.constructor}.`);o=l,s=t}if(u===void 0)u=[s.length];else if(!Array.isArray(u))throw new TypeError("A tensor's dims must be a number array");i=u,this.cpuData=s,this.dataLocation="cpu"}let a=Ia(i);if(this.cpuData&&a!==this.cpuData.length&&!((o==="uint4"||o==="int4")&&Math.ceil(a/2)===this.cpuData.length))throw new Error(`Tensor's size(${a}) does not match data length(${this.cpuData.length}).`);this.type=o,this.dims=i,this.size=a}static async fromImage(t,n){return ma(t,n)}static fromTexture(t,n){return ba(t,n)}static fromGpuBuffer(t,n){return ga(t,n)}static fromMLTensor(t,n){return ya(t,n)}static fromPinnedBuffer(t,n,e){return Ta(t,n,e)}toDataURL(t){return da(this,t)}toImageData(t){return pa(this,t)}get data(){if(this.ensureValid(),!this.cpuData)throw new Error("The data is not on CPU. Use `getData()` to download GPU data to CPU, or use `texture` or `gpuBuffer` property to access the GPU data directly.");return this.cpuData}get location(){return this.dataLocation}get texture(){if(this.ensureValid(),!this.gpuTextureData)throw new Error("The data is not stored as a WebGL texture.");return this.gpuTextureData}get gpuBuffer(){if(this.ensureValid(),!this.gpuBufferData)throw new Error("The data is not stored as a WebGPU buffer.");return this.gpuBufferData}get mlTensor(){if(this.ensureValid(),!this.mlTensorData)throw new Error("The data is not stored as a WebNN MLTensor.");return this.mlTensorData}async getData(t){switch(this.ensureValid(),this.dataLocation){case"cpu":case"cpu-pinned":return this.data;case"texture":case"gpu-buffer":case"ml-tensor":{if(!this.downloader)throw new Error("The current tensor is not created with a specified data downloader.");if(this.isDownloading)throw new Error("The current tensor is being downloaded.");try{this.isDownloading=!0;let n=await this.downloader();return this.downloader=void 0,this.dataLocation="cpu",this.cpuData=n,t&&this.disposer&&(this.disposer(),this.disposer=void 0),n}finally{this.isDownloading=!1}}default:throw new Error(`cannot get data from location: ${this.dataLocation}`)}}dispose(){if(this.isDownloading)throw new Error("The current tensor is being downloaded.");this.disposer&&(this.disposer(),this.disposer=void 0),this.cpuData=void 0,this.gpuTextureData=void 0,this.gpuBufferData=void 0,this.mlTensorData=void 0,this.downloader=void 0,this.isDownloading=void 0,this.dataLocation="none"}ensureValid(){if(this.dataLocation==="none")throw new Error("The tensor is disposed.")}reshape(t){if(this.ensureValid(),this.downloader||this.disposer)throw new Error("Cannot reshape a tensor that owns GPU resource.");return Oa(this,t)}}});var de,$r=y(()=>{"use strict";wn();de=et});var Sa,Aa,In,On,Nr=y(()=>{"use strict";Dr();Sa=(r,t)=>{(typeof ft.trace>"u"?!ft.wasm.trace:!ft.trace)||console.timeStamp(`${r}::ORT::${t}`)},Aa=(r,t)=>{let n=new Error().stack?.split(/\r\n|\r|\n/g)||[],e=!1;for(let o=0;o<n.length;o++){if(e&&!n[o].includes("TRACE_FUNC")){let i=`FUNC_${r}::${n[o].trim().split(" ")[1]}`;t&&(i+=`::${t}`),Sa("CPU",i);return}n[o].includes("TRACE_FUNC")&&(e=!0)}},In=r=>{(typeof ft.trace>"u"?!ft.wasm.trace:!ft.trace)||Aa("BEGIN",r)},On=r=>{(typeof ft.trace>"u"?!ft.wasm.trace:!ft.trace)||Aa("END",r)}});var Pn,Ea=y(()=>{"use strict";Er();$r();Nr();Pn=class r{constructor(t){this.handler=t}async run(t,n,e){In();let o={},i={};if(typeof t!="object"||t===null||t instanceof de||Array.isArray(t))throw new TypeError("'feeds' must be an object that use input names as keys and OnnxValue as corresponding values.");let a=!0;if(typeof n=="object"){if(n===null)throw new TypeError("Unexpected argument[1]: cannot be null.");if(n instanceof de)throw new TypeError("'fetches' cannot be a Tensor");if(Array.isArray(n)){if(n.length===0)throw new TypeError("'fetches' cannot be an empty array.");a=!1;for(let l of n){if(typeof l!="string")throw new TypeError("'fetches' must be a string array or an object.");if(this.outputNames.indexOf(l)===-1)throw new RangeError(`'fetches' contains invalid output name: ${l}.`);o[l]=null}if(typeof e=="object"&&e!==null)i=e;else if(typeof e<"u")throw new TypeError("'options' must be an object.")}else{let l=!1,c=Object.getOwnPropertyNames(n);for(let d of this.outputNames)if(c.indexOf(d)!==-1){let p=n[d];(p===null||p instanceof de)&&(l=!0,a=!1,o[d]=p)}if(l){if(typeof e=="object"&&e!==null)i=e;else if(typeof e<"u")throw new TypeError("'options' must be an object.")}else i=n}}else if(typeof n<"u")throw new TypeError("Unexpected argument[1]: must be 'fetches' or 'options'.");for(let l of this.inputNames)if(typeof t[l]>"u")throw new Error(`input '${l}' is missing in 'feeds'.`);if(a)for(let l of this.outputNames)o[l]=null;let s=await this.handler.run(t,o,i),u={};for(let l in s)if(Object.hasOwnProperty.call(s,l)){let c=s[l];c instanceof de?u[l]=c:u[l]=new de(c.type,c.data,c.dims)}return On(),u}async release(){return this.handler.dispose()}static async create(t,n,e,o){In();let i,a={};if(typeof t=="string"){if(i=t,typeof n=="object"&&n!==null)a=n;else if(typeof n<"u")throw new TypeError("'options' must be an object.")}else if(t instanceof Uint8Array){if(i=t,typeof n=="object"&&n!==null)a=n;else if(typeof n<"u")throw new TypeError("'options' must be an object.")}else if(t instanceof ArrayBuffer||typeof SharedArrayBuffer<"u"&&t instanceof SharedArrayBuffer){let c=t,d=0,p=t.byteLength;if(typeof n=="object"&&n!==null)a=n;else if(typeof n=="number"){if(d=n,!Number.isSafeInteger(d))throw new RangeError("'byteOffset' must be an integer.");if(d<0||d>=c.byteLength)throw new RangeError(`'byteOffset' is out of range [0, ${c.byteLength}).`);if(p=t.byteLength-d,typeof e=="number"){if(p=e,!Number.isSafeInteger(p))throw new RangeError("'byteLength' must be an integer.");if(p<=0||d+p>c.byteLength)throw new RangeError(`'byteLength' is out of range (0, ${c.byteLength-d}].`);if(typeof o=="object"&&o!==null)a=o;else if(typeof o<"u")throw new TypeError("'options' must be an object.")}else if(typeof e<"u")throw new TypeError("'byteLength' must be a number.")}else if(typeof n<"u")throw new TypeError("'options' must be an object.");i=new Uint8Array(c,d,p)}else throw new TypeError("Unexpected argument[0]: must be 'path' or 'buffer'.");let[s,u]=await aa(a),l=await s.createInferenceSessionHandler(i,u);return On(),new r(l)}startProfiling(){this.handler.startProfiling()}endProfiling(){this.handler.endProfiling()}get inputNames(){return this.handler.inputNames}get outputNames(){return this.handler.outputNames}}});var xd,Da=y(()=>{"use strict";Ea();xd=Pn});var La=y(()=>{"use strict"});var $a=y(()=>{"use strict"});var Na=y(()=>{"use strict"});var Fa=y(()=>{"use strict"});var Fr={};oa(Fr,{InferenceSession:()=>xd,TRACE:()=>Sa,TRACE_FUNC_BEGIN:()=>In,TRACE_FUNC_END:()=>On,Tensor:()=>de,env:()=>K,registerBackend:()=>vn});var me=y(()=>{"use strict";sa();fa();Da();$r();La();$a();Nr();Na();Fa()});function be(r,t,n,e){if(t===void 0)return wd(r);if(n===void 0)Sn(r,t,1);else if(typeof n=="number"&&e===void 0)Sn(r,t,n);else if(typeof n=="string"&&e===void 0)Sn(r,n,1,t);else if(typeof n=="string"&&typeof e=="number")Sn(r,n,e,t);else throw new TypeError("input is valid")}function wd(r){return{verbose:be.verbose.bind(null,r),info:be.info.bind(null,r),warning:be.warning.bind(null,r),error:be.error.bind(null,r),fatal:be.fatal.bind(null,r)}}function Sn(r,t,n,e){let o=Qe[e||""]||Qe[""];Ra[r]<Ra[o.minimalSeverity]||(o.logDateTime&&(t=`${new Date().toISOString()}|${t}`),o.logSourceLocation,vd[o.provider].log(r,t,e))}var Cr,Rr,Ra,vd,Ga,Qe,B,En,Dn,Ln,An,pt=y(()=>{"use strict";Cr=class{log(t,n,e){}},Rr=class{log(t,n,e){console.log(`${this.color(t)} ${e?"\x1B[35m"+e+"\x1B[0m ":""}${n}`)}color(t){switch(t){case"verbose":return"\x1B[34;40mv\x1B[0m";case"info":return"\x1B[32mi\x1B[0m";case"warning":return"\x1B[30;43mw\x1B[0m";case"error":return"\x1B[31;40me\x1B[0m";case"fatal":return"\x1B[101mf\x1B[0m";default:throw new Error(`unsupported severity: ${t}`)}}},Ra={verbose:1e3,info:2e3,warning:4e3,error:5e3,fatal:6e3},vd={none:new Cr,console:new Rr},Ga={provider:"console",minimalSeverity:"warning",logDateTime:!0,logSourceLocation:!1},Qe={"":Ga};(u=>{function r(l,c){u("verbose",l,c)}u.verbose=r;function t(l,c){u("info",l,c)}u.info=t;function n(l,c){u("warning",l,c)}u.warning=n;function e(l,c){u("error",l,c)}u.error=e;function o(l,c){u("fatal",l,c)}u.fatal=o;function i(l){Qe={},a("",l||{})}u.reset=i;function a(l,c){if(l==="*")i(c);else{let d=Qe[l]||Ga;Qe[l]={provider:c.provider||d.provider,minimalSeverity:c.minimalSeverity||d.minimalSeverity,logDateTime:c.logDateTime===void 0?d.logDateTime:c.logDateTime,logSourceLocation:c.logSourceLocation===void 0?d.logSourceLocation:c.logSourceLocation}}}u.set=a;function s(l){let c={};l.logLevel&&(c.minimalSeverity=l.logLevel),a("",c)}u.setWithEnv=s})(be||={});B=be,En=class{constructor(t,n,e,o,i,a){this.category=t;this.name=n;this.startTime=e;this.endCallback=o;this.timer=i;this.ctx=a}async end(){return this.endCallback(this)}async checkTimer(){if(this.ctx===void 0||this.timer===void 0)throw new Error("No webgl timer found");return this.ctx.endTimer(),this.ctx.waitForQueryAndGetTime(this.timer)}},Dn=class{constructor(t,n,e,o){this.category=t;this.name=n;this.startTime=e;this.endTime=o}},Ln=class{constructor(t,n,e){this._started=!1;this._flushPointer=0;this._started=!1,this._maxNumberEvents=t===void 0?1e4:t,this._flushBatchSize=n===void 0?10:n,this._flushIntervalInMilliseconds=e===void 0?5e3:e}static create(t){return t===void 0?new this:new this(t.maxNumberEvents,t.flushBatchSize,t.flushIntervalInMilliseconds)}start(){this._started=!0,this._timingEvents=[],this._flushTime=An(),this._flushPointer=0}stop(){for(this._started=!1;this._flushPointer<this._timingEvents.length;this._flushPointer++)this.logOneEvent(this._timingEvents[this._flushPointer])}event(t,n,e,o){let i=this._started?this.begin(t,n,o):void 0,a=!1,s=e();if(s&&typeof s.then=="function")return a=!0,new Promise((u,l)=>{s.then(async c=>{i&&await i.end(),u(c)},async c=>{i&&await i.end(),l(c)})});if(!a&&i){let u=i.end();if(u&&typeof u.then=="function")return new Promise((l,c)=>{u.then(()=>{l(s)},d=>{c(d)})})}return s}begin(t,n,e){if(!this._started)throw new Error("profiler is not started yet");if(e===void 0){let o=An();return this.flush(o),new En(t,n,o,i=>this.endSync(i))}else{let o=e.beginTimer();return new En(t,n,0,async i=>this.end(i),o,e)}}async end(t){let n=await t.checkTimer();this._timingEvents.length<this._maxNumberEvents&&(this._timingEvents.push(new Dn(t.category,t.name,t.startTime,n)),this.flush(n))}endSync(t){let n=An();this._timingEvents.length<this._maxNumberEvents&&(this._timingEvents.push(new Dn(t.category,t.name,t.startTime,n)),this.flush(n))}logOneEvent(t){B.verbose(`Profiler.${t.category}`,`${(t.endTime-t.startTime).toFixed(2)}ms on event '${t.name}' at ${t.endTime.toFixed(2)}`)}flush(t){if(this._timingEvents.length-this._flushPointer>=this._flushBatchSize||t-this._flushTime>=this._flushIntervalInMilliseconds){for(let n=this._flushPointer;this._flushPointer<n+this._flushBatchSize&&this._flushPointer<this._timingEvents.length;this._flushPointer++)this.logOneEvent(this._timingEvents[this._flushPointer]);this._flushTime=An()}}get started(){return this._started}},An=typeof performance<"u"&&performance.now?()=>performance.now():Date.now});function ka(r,t,n){for(let e of n){let o=e[0],i=e[1],a=e[2],s=e[3],u=e[4];if(r.opType===o){for(let l of t)if((l.domain===i||l.domain==="ai.onnx"&&i==="")&&Id(l.version,a))return{opImpl:s,opInit:u}}}throw new TypeError(`cannot resolve operator '${r.opType}' with opsets: ${t.map(e=>`${e.domain||"ai.onnx"} v${e.version}`).join(", ")}`)}function Id(r,t){if(t.endsWith("+")){let n=Number.parseInt(t.substring(0,t.length-1),10);return!isNaN(n)&&n<=r}else if(t.split("-").length===2){let n=t.split("-"),e=Number.parseInt(n[0],10),o=Number.parseInt(n[1],10);return!isNaN(e)&&!isNaN(o)&&e<=r&&r<=o}else return Number.parseInt(t,10)===r}var Ma=y(()=>{"use strict"});var Va=P(Gr=>{"use strict";Gr.__esModule=!0;var Od=function(){function r(t){if(!t)throw new TypeError("Invalid argument; `value` has no value.");this.value=r.EMPTY,t&&r.isGuid(t)&&(this.value=t)}return r.isGuid=function(t){var n=t.toString();return t&&(t instanceof r||r.validator.test(n))},r.create=function(){return new r([r.gen(2),r.gen(1),r.gen(1),r.gen(1),r.gen(3)].join("-"))},r.createEmpty=function(){return new r("emptyguid")},r.parse=function(t){return new r(t)},r.raw=function(){return[r.gen(2),r.gen(1),r.gen(1),r.gen(1),r.gen(3)].join("-")},r.gen=function(t){for(var n="",e=0;e<t;e++)n+=((1+Math.random())*65536|0).toString(16).substring(1);return n},r.prototype.equals=function(t){return r.isGuid(t)&&this.value===t.toString()},r.prototype.isEmpty=function(){return this.value===r.EMPTY},r.prototype.toString=function(){return this.value},r.prototype.toJSON=function(){return{value:this.value}},r.validator=new RegExp("^[a-z0-9]{8}-[a-z0-9]{4}-[a-z0-9]{4}-[a-z0-9]{4}-[a-z0-9]{12}$","i"),r.EMPTY="00000000-0000-0000-0000-000000000000",r}();Gr.Guid=Od});function U(r,t,n){this.low=r|0,this.high=t|0,this.unsigned=!!n}function rt(r){return(r&&r.__isLong__)===!0}function Ba(r){var t=Math.clz32(r&-r);return r?31-t:t}function Se(r,t){var n,e,o;return t?(r>>>=0,(o=0<=r&&r<256)&&(e=Ua[r],e)?e:(n=k(r,0,!0),o&&(Ua[r]=n),n)):(r|=0,(o=-128<=r&&r<128)&&(e=za[r],e)?e:(n=k(r,r<0?-1:0,!1),o&&(za[r]=n),n))}function mt(r,t){if(isNaN(r))return t?pe:vt;if(t){if(r<0)return pe;if(r>=qa)return Za}else{if(r<=-Wa)return st;if(r+1>=Wa)return Xa}return r<0?mt(-r,t).neg():k(r%Be|0,r/Be|0,t)}function k(r,t,n){return new U(r,t,n)}function Mr(r,t,n){if(r.length===0)throw Error("empty string");if(typeof t=="number"?(n=t,t=!1):t=!!t,r==="NaN"||r==="Infinity"||r==="+Infinity"||r==="-Infinity")return t?pe:vt;if(n=n||10,n<2||36<n)throw RangeError("radix");var e;if((e=r.indexOf("-"))>0)throw Error("interior hyphen");if(e===0)return Mr(r.substring(1),t,n).neg();for(var o=mt($n(n,8)),i=vt,a=0;a<r.length;a+=8){var s=Math.min(8,r.length-a),u=parseInt(r.substring(a,a+s),n);if(s<8){var l=mt($n(n,s));i=i.mul(l).add(mt(u))}else i=i.mul(o),i=i.add(mt(u))}return i.unsigned=t,i}function wt(r,t){return typeof r=="number"?mt(r,t):typeof r=="string"?Mr(r,t):k(r.low,r.high,typeof t=="boolean"?t:r.unsigned)}var ht,za,Ua,$n,ja,Pd,Be,qa,Wa,Ha,vt,pe,Ve,Ka,kr,Xa,Za,st,T,ge,Vr=y(()=>{ht=null;try{ht=new WebAssembly.Instance(new WebAssembly.Module(new Uint8Array([0,97,115,109,1,0,0,0,1,13,2,96,0,1,127,96,4,127,127,127,127,1,127,3,7,6,0,1,1,1,1,1,6,6,1,127,1,65,0,11,7,50,6,3,109,117,108,0,1,5,100,105,118,95,115,0,2,5,100,105,118,95,117,0,3,5,114,101,109,95,115,0,4,5,114,101,109,95,117,0,5,8,103,101,116,95,104,105,103,104,0,0,10,191,1,6,4,0,35,0,11,36,1,1,126,32,0,173,32,1,173,66,32,134,132,32,2,173,32,3,173,66,32,134,132,126,34,4,66,32,135,167,36,0,32,4,167,11,36,1,1,126,32,0,173,32,1,173,66,32,134,132,32,2,173,32,3,173,66,32,134,132,127,34,4,66,32,135,167,36,0,32,4,167,11,36,1,1,126,32,0,173,32,1,173,66,32,134,132,32,2,173,32,3,173,66,32,134,132,128,34,4,66,32,135,167,36,0,32,4,167,11,36,1,1,126,32,0,173,32,1,173,66,32,134,132,32,2,173,32,3,173,66,32,134,132,129,34,4,66,32,135,167,36,0,32,4,167,11,36,1,1,126,32,0,173,32,1,173,66,32,134,132,32,2,173,32,3,173,66,32,134,132,130,34,4,66,32,135,167,36,0,32,4,167,11])),{}).exports}catch{}U.prototype.__isLong__;Object.defineProperty(U.prototype,"__isLong__",{value:!0});U.isLong=rt;za={},Ua={};U.fromInt=Se;U.fromNumber=mt;U.fromBits=k;$n=Math.pow;U.fromString=Mr;U.fromValue=wt;ja=65536,Pd=1<<24,Be=ja*ja,qa=Be*Be,Wa=qa/2,Ha=Se(Pd),vt=Se(0);U.ZERO=vt;pe=Se(0,!0);U.UZERO=pe;Ve=Se(1);U.ONE=Ve;Ka=Se(1,!0);U.UONE=Ka;kr=Se(-1);U.NEG_ONE=kr;Xa=k(-1,2147483647,!1);U.MAX_VALUE=Xa;Za=k(-1,-1,!0);U.MAX_UNSIGNED_VALUE=Za;st=k(0,-2147483648,!1);U.MIN_VALUE=st;T=U.prototype;T.toInt=function(){return this.unsigned?this.low>>>0:this.low};T.toNumber=function(){return this.unsigned?(this.high>>>0)*Be+(this.low>>>0):this.high*Be+(this.low>>>0)};T.toString=function(t){if(t=t||10,t<2||36<t)throw RangeError("radix");if(this.isZero())return"0";if(this.isNegative())if(this.eq(st)){var n=mt(t),e=this.div(n),o=e.mul(n).sub(this);return e.toString(t)+o.toInt().toString(t)}else return"-"+this.neg().toString(t);for(var i=mt($n(t,6),this.unsigned),a=this,s="";;){var u=a.div(i),l=a.sub(u.mul(i)).toInt()>>>0,c=l.toString(t);if(a=u,a.isZero())return c+s;for(;c.length<6;)c="0"+c;s=""+c+s}};T.getHighBits=function(){return this.high};T.getHighBitsUnsigned=function(){return this.high>>>0};T.getLowBits=function(){return this.low};T.getLowBitsUnsigned=function(){return this.low>>>0};T.getNumBitsAbs=function(){if(this.isNegative())return this.eq(st)?64:this.neg().getNumBitsAbs();for(var t=this.high!=0?this.high:this.low,n=31;n>0&&!(t&1<<n);n--);return this.high!=0?n+33:n+1};T.isZero=function(){return this.high===0&&this.low===0};T.eqz=T.isZero;T.isNegative=function(){return!this.unsigned&&this.high<0};T.isPositive=function(){return this.unsigned||this.high>=0};T.isOdd=function(){return(this.low&1)===1};T.isEven=function(){return(this.low&1)===0};T.equals=function(t){return rt(t)||(t=wt(t)),this.unsigned!==t.unsigned&&this.high>>>31===1&&t.high>>>31===1?!1:this.high===t.high&&this.low===t.low};T.eq=T.equals;T.notEquals=function(t){return!this.eq(t)};T.neq=T.notEquals;T.ne=T.notEquals;T.lessThan=function(t){return this.comp(t)<0};T.lt=T.lessThan;T.lessThanOrEqual=function(t){return this.comp(t)<=0};T.lte=T.lessThanOrEqual;T.le=T.lessThanOrEqual;T.greaterThan=function(t){return this.comp(t)>0};T.gt=T.greaterThan;T.greaterThanOrEqual=function(t){return this.comp(t)>=0};T.gte=T.greaterThanOrEqual;T.ge=T.greaterThanOrEqual;T.compare=function(t){if(rt(t)||(t=wt(t)),this.eq(t))return 0;var n=this.isNegative(),e=t.isNegative();return n&&!e?-1:!n&&e?1:this.unsigned?t.high>>>0>this.high>>>0||t.high===this.high&&t.low>>>0>this.low>>>0?-1:1:this.sub(t).isNegative()?-1:1};T.comp=T.compare;T.negate=function(){return!this.unsigned&&this.eq(st)?st:this.not().add(Ve)};T.neg=T.negate;T.add=function(t){rt(t)||(t=wt(t));var n=this.high>>>16,e=this.high&65535,o=this.low>>>16,i=this.low&65535,a=t.high>>>16,s=t.high&65535,u=t.low>>>16,l=t.low&65535,c=0,d=0,p=0,m=0;return m+=i+l,p+=m>>>16,m&=65535,p+=o+u,d+=p>>>16,p&=65535,d+=e+s,c+=d>>>16,d&=65535,c+=n+a,c&=65535,k(p<<16|m,c<<16|d,this.unsigned)};T.subtract=function(t){return rt(t)||(t=wt(t)),this.add(t.neg())};T.sub=T.subtract;T.multiply=function(t){if(this.isZero())return this;if(rt(t)||(t=wt(t)),ht){var n=ht.mul(this.low,this.high,t.low,t.high);return k(n,ht.get_high(),this.unsigned)}if(t.isZero())return this.unsigned?pe:vt;if(this.eq(st))return t.isOdd()?st:vt;if(t.eq(st))return this.isOdd()?st:vt;if(this.isNegative())return t.isNegative()?this.neg().mul(t.neg()):this.neg().mul(t).neg();if(t.isNegative())return this.mul(t.neg()).neg();if(this.lt(Ha)&&t.lt(Ha))return mt(this.toNumber()*t.toNumber(),this.unsigned);var e=this.high>>>16,o=this.high&65535,i=this.low>>>16,a=this.low&65535,s=t.high>>>16,u=t.high&65535,l=t.low>>>16,c=t.low&65535,d=0,p=0,m=0,b=0;return b+=a*c,m+=b>>>16,b&=65535,m+=i*c,p+=m>>>16,m&=65535,m+=a*l,p+=m>>>16,m&=65535,p+=o*c,d+=p>>>16,p&=65535,p+=i*l,d+=p>>>16,p&=65535,p+=a*u,d+=p>>>16,p&=65535,d+=e*c+o*l+i*u+a*s,d&=65535,k(m<<16|b,d<<16|p,this.unsigned)};T.mul=T.multiply;T.divide=function(t){if(rt(t)||(t=wt(t)),t.isZero())throw Error("division by zero");if(ht){if(!this.unsigned&&this.high===-2147483648&&t.low===-1&&t.high===-1)return this;var n=(this.unsigned?ht.div_u:ht.div_s)(this.low,this.high,t.low,t.high);return k(n,ht.get_high(),this.unsigned)}if(this.isZero())return this.unsigned?pe:vt;var e,o,i;if(this.unsigned){if(t.unsigned||(t=t.toUnsigned()),t.gt(this))return pe;if(t.gt(this.shru(1)))return Ka;i=pe}else{if(this.eq(st)){if(t.eq(Ve)||t.eq(kr))return st;if(t.eq(st))return Ve;var a=this.shr(1);return e=a.div(t).shl(1),e.eq(vt)?t.isNegative()?Ve:kr:(o=this.sub(t.mul(e)),i=e.add(o.div(t)),i)}else if(t.eq(st))return this.unsigned?pe:vt;if(this.isNegative())return t.isNegative()?this.neg().div(t.neg()):this.neg().div(t).neg();if(t.isNegative())return this.div(t.neg()).neg();i=vt}for(o=this;o.gte(t);){e=Math.max(1,Math.floor(o.toNumber()/t.toNumber()));for(var s=Math.ceil(Math.log(e)/Math.LN2),u=s<=48?1:$n(2,s-48),l=mt(e),c=l.mul(t);c.isNegative()||c.gt(o);)e-=u,l=mt(e,this.unsigned),c=l.mul(t);l.isZero()&&(l=Ve),i=i.add(l),o=o.sub(c)}return i};T.div=T.divide;T.modulo=function(t){if(rt(t)||(t=wt(t)),ht){var n=(this.unsigned?ht.rem_u:ht.rem_s)(this.low,this.high,t.low,t.high);return k(n,ht.get_high(),this.unsigned)}return this.sub(this.div(t).mul(t))};T.mod=T.modulo;T.rem=T.modulo;T.not=function(){return k(~this.low,~this.high,this.unsigned)};T.countLeadingZeros=function(){return this.high?Math.clz32(this.high):Math.clz32(this.low)+32};T.clz=T.countLeadingZeros;T.countTrailingZeros=function(){return this.low?Ba(this.low):Ba(this.high)+32};T.ctz=T.countTrailingZeros;T.and=function(t){return rt(t)||(t=wt(t)),k(this.low&t.low,this.high&t.high,this.unsigned)};T.or=function(t){return rt(t)||(t=wt(t)),k(this.low|t.low,this.high|t.high,this.unsigned)};T.xor=function(t){return rt(t)||(t=wt(t)),k(this.low^t.low,this.high^t.high,this.unsigned)};T.shiftLeft=function(t){return rt(t)&&(t=t.toInt()),(t&=63)===0?this:t<32?k(this.low<<t,this.high<<t|this.low>>>32-t,this.unsigned):k(0,this.low<<t-32,this.unsigned)};T.shl=T.shiftLeft;T.shiftRight=function(t){return rt(t)&&(t=t.toInt()),(t&=63)===0?this:t<32?k(this.low>>>t|this.high<<32-t,this.high>>t,this.unsigned):k(this.high>>t-32,this.high>=0?0:-1,this.unsigned)};T.shr=T.shiftRight;T.shiftRightUnsigned=function(t){return rt(t)&&(t=t.toInt()),(t&=63)===0?this:t<32?k(this.low>>>t|this.high<<32-t,this.high>>>t,this.unsigned):t===32?k(this.high,0,this.unsigned):k(this.high>>>t-32,0,this.unsigned)};T.shru=T.shiftRightUnsigned;T.shr_u=T.shiftRightUnsigned;T.rotateLeft=function(t){var n;return rt(t)&&(t=t.toInt()),(t&=63)===0?this:t===32?k(this.high,this.low,this.unsigned):t<32?(n=32-t,k(this.low<<t|this.high>>>n,this.high<<t|this.low>>>n,this.unsigned)):(t-=32,n=32-t,k(this.high<<t|this.low>>>n,this.low<<t|this.high>>>n,this.unsigned))};T.rotl=T.rotateLeft;T.rotateRight=function(t){var n;return rt(t)&&(t=t.toInt()),(t&=63)===0?this:t===32?k(this.high,this.low,this.unsigned):t<32?(n=32-t,k(this.high<<n|this.low>>>t,this.low<<n|this.high>>>t,this.unsigned)):(t-=32,n=32-t,k(this.low<<n|this.high>>>t,this.high<<n|this.low>>>t,this.unsigned))};T.rotr=T.rotateRight;T.toSigned=function(){return this.unsigned?k(this.low,this.high,!1):this};T.toUnsigned=function(){return this.unsigned?this:k(this.low,this.high,!0)};T.toBytes=function(t){return t?this.toBytesLE():this.toBytesBE()};T.toBytesLE=function(){var t=this.high,n=this.low;return[n&255,n>>>8&255,n>>>16&255,n>>>24,t&255,t>>>8&255,t>>>16&255,t>>>24]};T.toBytesBE=function(){var t=this.high,n=this.low;return[t>>>24,t>>>16&255,t>>>8&255,t&255,n>>>24,n>>>16&255,n>>>8&255,n&255]};U.fromBytes=function(t,n,e){return e?U.fromBytesLE(t,n):U.fromBytesBE(t,n)};U.fromBytesLE=function(t,n){return new U(t[0]|t[1]<<8|t[2]<<16|t[3]<<24,t[4]|t[5]<<8|t[6]<<16|t[7]<<24,n)};U.fromBytesBE=function(t,n){return new U(t[4]<<24|t[5]<<16|t[6]<<8|t[7],t[0]<<24|t[1]<<16|t[2]<<8|t[3],n)};ge=U});var Br=P(Nn=>{"use strict";Object.defineProperty(Nn,"__esModule",{value:!0});Nn.ArgType=void 0;var Ja;(function(r){r[r.INPUT=0]="INPUT",r[r.OUTPUT=1]="OUTPUT"})(Ja||(Nn.ArgType=Ja={}))});var Ae=P(Et=>{"use strict";Object.defineProperty(Et,"__esModule",{value:!0});Et.SIZE_PREFIX_LENGTH=Et.FILE_IDENTIFIER_LENGTH=Et.SIZEOF_INT=Et.SIZEOF_SHORT=void 0;Et.SIZEOF_SHORT=2;Et.SIZEOF_INT=4;Et.FILE_IDENTIFIER_LENGTH=4;Et.SIZE_PREFIX_LENGTH=4});var zr=P(bt=>{"use strict";Object.defineProperty(bt,"__esModule",{value:!0});bt.isLittleEndian=bt.float64=bt.float32=bt.int32=void 0;bt.int32=new Int32Array(2);bt.float32=new Float32Array(bt.int32.buffer);bt.float64=new Float64Array(bt.int32.buffer);bt.isLittleEndian=new Uint16Array(new Uint8Array([1,0]).buffer)[0]===1});var Ur=P(Fn=>{"use strict";Object.defineProperty(Fn,"__esModule",{value:!0});Fn.Encoding=void 0;var Ya;(function(r){r[r.UTF8_BYTES=1]="UTF8_BYTES",r[r.UTF16_STRING=2]="UTF16_STRING"})(Ya||(Fn.Encoding=Ya={}))});var Wr=P(Cn=>{"use strict";Object.defineProperty(Cn,"__esModule",{value:!0});Cn.ByteBuffer=void 0;var Dt=Ae(),ut=zr(),Sd=Ur(),jr=class r{constructor(t){this.bytes_=t,this.position_=0,this.text_decoder_=new TextDecoder}static allocate(t){return new r(new Uint8Array(t))}clear(){this.position_=0}bytes(){return this.bytes_}position(){return this.position_}setPosition(t){this.position_=t}capacity(){return this.bytes_.length}readInt8(t){return this.readUint8(t)<<24>>24}readUint8(t){return this.bytes_[t]}readInt16(t){return this.readUint16(t)<<16>>16}readUint16(t){return this.bytes_[t]|this.bytes_[t+1]<<8}readInt32(t){return this.bytes_[t]|this.bytes_[t+1]<<8|this.bytes_[t+2]<<16|this.bytes_[t+3]<<24}readUint32(t){return this.readInt32(t)>>>0}readInt64(t){return BigInt.asIntN(64,BigInt(this.readUint32(t))+(BigInt(this.readUint32(t+4))<<BigInt(32)))}readUint64(t){return BigInt.asUintN(64,BigInt(this.readUint32(t))+(BigInt(this.readUint32(t+4))<<BigInt(32)))}readFloat32(t){return ut.int32[0]=this.readInt32(t),ut.float32[0]}readFloat64(t){return ut.int32[ut.isLittleEndian?0:1]=this.readInt32(t),ut.int32[ut.isLittleEndian?1:0]=this.readInt32(t+4),ut.float64[0]}writeInt8(t,n){this.bytes_[t]=n}writeUint8(t,n){this.bytes_[t]=n}writeInt16(t,n){this.bytes_[t]=n,this.bytes_[t+1]=n>>8}writeUint16(t,n){this.bytes_[t]=n,this.bytes_[t+1]=n>>8}writeInt32(t,n){this.bytes_[t]=n,this.bytes_[t+1]=n>>8,this.bytes_[t+2]=n>>16,this.bytes_[t+3]=n>>24}writeUint32(t,n){this.bytes_[t]=n,this.bytes_[t+1]=n>>8,this.bytes_[t+2]=n>>16,this.bytes_[t+3]=n>>24}writeInt64(t,n){this.writeInt32(t,Number(BigInt.asIntN(32,n))),this.writeInt32(t+4,Number(BigInt.asIntN(32,n>>BigInt(32))))}writeUint64(t,n){this.writeUint32(t,Number(BigInt.asUintN(32,n))),this.writeUint32(t+4,Number(BigInt.asUintN(32,n>>BigInt(32))))}writeFloat32(t,n){ut.float32[0]=n,this.writeInt32(t,ut.int32[0])}writeFloat64(t,n){ut.float64[0]=n,this.writeInt32(t,ut.int32[ut.isLittleEndian?0:1]),this.writeInt32(t+4,ut.int32[ut.isLittleEndian?1:0])}getBufferIdentifier(){if(this.bytes_.length<this.position_+Dt.SIZEOF_INT+Dt.FILE_IDENTIFIER_LENGTH)throw new Error("FlatBuffers: ByteBuffer is too short to contain an identifier.");let t="";for(let n=0;n<Dt.FILE_IDENTIFIER_LENGTH;n++)t+=String.fromCharCode(this.readInt8(this.position_+Dt.SIZEOF_INT+n));return t}__offset(t,n){let e=t-this.readInt32(t);return n<this.readInt16(e)?this.readInt16(e+n):0}__union(t,n){return t.bb_pos=n+this.readInt32(n),t.bb=this,t}__string(t,n){t+=this.readInt32(t);let e=this.readInt32(t);t+=Dt.SIZEOF_INT;let o=this.bytes_.subarray(t,t+e);return n===Sd.Encoding.UTF8_BYTES?o:this.text_decoder_.decode(o)}__union_with_string(t,n){return typeof t=="string"?this.__string(n):this.__union(t,n)}__indirect(t){return t+this.readInt32(t)}__vector(t){return t+this.readInt32(t)+Dt.SIZEOF_INT}__vector_len(t){return this.readInt32(t+this.readInt32(t))}__has_identifier(t){if(t.length!=Dt.FILE_IDENTIFIER_LENGTH)throw new Error("FlatBuffers: file identifier must be length "+Dt.FILE_IDENTIFIER_LENGTH);for(let n=0;n<Dt.FILE_IDENTIFIER_LENGTH;n++)if(t.charCodeAt(n)!=this.readInt8(this.position()+Dt.SIZEOF_INT+n))return!1;return!0}createScalarList(t,n){let e=[];for(let o=0;o<n;++o){let i=t(o);i!==null&&e.push(i)}return e}createObjList(t,n){let e=[];for(let o=0;o<n;++o){let i=t(o);i!==null&&e.push(i.unpack())}return e}};Cn.ByteBuffer=jr});var ts=P(Rn=>{"use strict";Object.defineProperty(Rn,"__esModule",{value:!0});Rn.Builder=void 0;var Qa=Wr(),dt=Ae(),Hr=class r{constructor(t){this.minalign=1,this.vtable=null,this.vtable_in_use=0,this.isNested=!1,this.object_start=0,this.vtables=[],this.vector_num_elems=0,this.force_defaults=!1,this.string_maps=null,this.text_encoder=new TextEncoder;let n;t?n=t:n=1024,this.bb=Qa.ByteBuffer.allocate(n),this.space=n}clear(){this.bb.clear(),this.space=this.bb.capacity(),this.minalign=1,this.vtable=null,this.vtable_in_use=0,this.isNested=!1,this.object_start=0,this.vtables=[],this.vector_num_elems=0,this.force_defaults=!1,this.string_maps=null}forceDefaults(t){this.force_defaults=t}dataBuffer(){return this.bb}asUint8Array(){return this.bb.bytes().subarray(this.bb.position(),this.bb.position()+this.offset())}prep(t,n){t>this.minalign&&(this.minalign=t);let e=~(this.bb.capacity()-this.space+n)+1&t-1;for(;this.space<e+t+n;){let o=this.bb.capacity();this.bb=r.growByteBuffer(this.bb),this.space+=this.bb.capacity()-o}this.pad(e)}pad(t){for(let n=0;n<t;n++)this.bb.writeInt8(--this.space,0)}writeInt8(t){this.bb.writeInt8(this.space-=1,t)}writeInt16(t){this.bb.writeInt16(this.space-=2,t)}writeInt32(t){this.bb.writeInt32(this.space-=4,t)}writeInt64(t){this.bb.writeInt64(this.space-=8,t)}writeFloat32(t){this.bb.writeFloat32(this.space-=4,t)}writeFloat64(t){this.bb.writeFloat64(this.space-=8,t)}addInt8(t){this.prep(1,0),this.writeInt8(t)}addInt16(t){this.prep(2,0),this.writeInt16(t)}addInt32(t){this.prep(4,0),this.writeInt32(t)}addInt64(t){this.prep(8,0),this.writeInt64(t)}addFloat32(t){this.prep(4,0),this.writeFloat32(t)}addFloat64(t){this.prep(8,0),this.writeFloat64(t)}addFieldInt8(t,n,e){(this.force_defaults||n!=e)&&(this.addInt8(n),this.slot(t))}addFieldInt16(t,n,e){(this.force_defaults||n!=e)&&(this.addInt16(n),this.slot(t))}addFieldInt32(t,n,e){(this.force_defaults||n!=e)&&(this.addInt32(n),this.slot(t))}addFieldInt64(t,n,e){(this.force_defaults||n!==e)&&(this.addInt64(n),this.slot(t))}addFieldFloat32(t,n,e){(this.force_defaults||n!=e)&&(this.addFloat32(n),this.slot(t))}addFieldFloat64(t,n,e){(this.force_defaults||n!=e)&&(this.addFloat64(n),this.slot(t))}addFieldOffset(t,n,e){(this.force_defaults||n!=e)&&(this.addOffset(n),this.slot(t))}addFieldStruct(t,n,e){n!=e&&(this.nested(n),this.slot(t))}nested(t){if(t!=this.offset())throw new TypeError("FlatBuffers: struct must be serialized inline.")}notNested(){if(this.isNested)throw new TypeError("FlatBuffers: object serialization must not be nested.")}slot(t){this.vtable!==null&&(this.vtable[t]=this.offset())}offset(){return this.bb.capacity()-this.space}static growByteBuffer(t){let n=t.capacity();if(n&3221225472)throw new Error("FlatBuffers: cannot grow buffer beyond 2 gigabytes.");let e=n<<1,o=Qa.ByteBuffer.allocate(e);return o.setPosition(e-n),o.bytes().set(t.bytes(),e-n),o}addOffset(t){this.prep(dt.SIZEOF_INT,0),this.writeInt32(this.offset()-t+dt.SIZEOF_INT)}startObject(t){this.notNested(),this.vtable==null&&(this.vtable=[]),this.vtable_in_use=t;for(let n=0;n<t;n++)this.vtable[n]=0;this.isNested=!0,this.object_start=this.offset()}endObject(){if(this.vtable==null||!this.isNested)throw new Error("FlatBuffers: endObject called without startObject");this.addInt32(0);let t=this.offset(),n=this.vtable_in_use-1;for(;n>=0&&this.vtable[n]==0;n--);let e=n+1;for(;n>=0;n--)this.addInt16(this.vtable[n]!=0?t-this.vtable[n]:0);let o=2;this.addInt16(t-this.object_start);let i=(e+o)*dt.SIZEOF_SHORT;this.addInt16(i);let a=0,s=this.space;t:for(n=0;n<this.vtables.length;n++){let u=this.bb.capacity()-this.vtables[n];if(i==this.bb.readInt16(u)){for(let l=dt.SIZEOF_SHORT;l<i;l+=dt.SIZEOF_SHORT)if(this.bb.readInt16(s+l)!=this.bb.readInt16(u+l))continue t;a=this.vtables[n];break}}return a?(this.space=this.bb.capacity()-t,this.bb.writeInt32(this.space,a-t)):(this.vtables.push(this.offset()),this.bb.writeInt32(this.bb.capacity()-t,this.offset()-t)),this.isNested=!1,t}finish(t,n,e){let o=e?dt.SIZE_PREFIX_LENGTH:0;if(n){let i=n;if(this.prep(this.minalign,dt.SIZEOF_INT+dt.FILE_IDENTIFIER_LENGTH+o),i.length!=dt.FILE_IDENTIFIER_LENGTH)throw new TypeError("FlatBuffers: file identifier must be length "+dt.FILE_IDENTIFIER_LENGTH);for(let a=dt.FILE_IDENTIFIER_LENGTH-1;a>=0;a--)this.writeInt8(i.charCodeAt(a))}this.prep(this.minalign,dt.SIZEOF_INT+o),this.addOffset(t),o&&this.addInt32(this.bb.capacity()-this.space),this.bb.setPosition(this.space)}finishSizePrefixed(t,n){this.finish(t,n,!0)}requiredField(t,n){let e=this.bb.capacity()-t,o=e-this.bb.readInt32(e);if(!(n<this.bb.readInt16(o)&&this.bb.readInt16(o+n)!=0))throw new TypeError("FlatBuffers: field "+n+" must be set")}startVector(t,n,e){this.notNested(),this.vector_num_elems=n,this.prep(dt.SIZEOF_INT,t*n),this.prep(e,t*n)}endVector(){return this.writeInt32(this.vector_num_elems),this.offset()}createSharedString(t){if(!t)return 0;if(this.string_maps||(this.string_maps=new Map),this.string_maps.has(t))return this.string_maps.get(t);let n=this.createString(t);return this.string_maps.set(t,n),n}createString(t){if(t==null)return 0;let n;return t instanceof Uint8Array?n=t:n=this.text_encoder.encode(t),this.addInt8(0),this.startVector(1,n.length,1),this.bb.setPosition(this.space-=n.length),this.bb.bytes().set(n,this.space),this.endVector()}createByteVector(t){return t==null?0:(this.startVector(1,t.length,1),this.bb.setPosition(this.space-=t.length),this.bb.bytes().set(t,this.space),this.endVector())}createObjectOffset(t){return t===null?0:typeof t=="string"?this.createString(t):t.pack(this)}createObjectOffsetList(t){let n=[];for(let e=0;e<t.length;++e){let o=t[e];if(o!==null)n.push(this.createObjectOffset(o));else throw new TypeError("FlatBuffers: Argument for createObjectOffsetList cannot contain null.")}return n}createStructOffsetList(t,n){return n(this,t.length),this.createObjectOffsetList(t.slice().reverse()),this.endVector()}};Rn.Builder=Hr});var M=P(H=>{"use strict";Object.defineProperty(H,"__esModule",{value:!0});H.ByteBuffer=H.Builder=H.Encoding=H.isLittleEndian=H.float64=H.float32=H.int32=H.SIZE_PREFIX_LENGTH=H.FILE_IDENTIFIER_LENGTH=H.SIZEOF_INT=H.SIZEOF_SHORT=void 0;var Ad=Ae();Object.defineProperty(H,"SIZEOF_SHORT",{enumerable:!0,get:function(){return Ad.SIZEOF_SHORT}});var Ed=Ae();Object.defineProperty(H,"SIZEOF_INT",{enumerable:!0,get:function(){return Ed.SIZEOF_INT}});var Dd=Ae();Object.defineProperty(H,"FILE_IDENTIFIER_LENGTH",{enumerable:!0,get:function(){return Dd.FILE_IDENTIFIER_LENGTH}});var Ld=Ae();Object.defineProperty(H,"SIZE_PREFIX_LENGTH",{enumerable:!0,get:function(){return Ld.SIZE_PREFIX_LENGTH}});var Gn=zr();Object.defineProperty(H,"int32",{enumerable:!0,get:function(){return Gn.int32}});Object.defineProperty(H,"float32",{enumerable:!0,get:function(){return Gn.float32}});Object.defineProperty(H,"float64",{enumerable:!0,get:function(){return Gn.float64}});Object.defineProperty(H,"isLittleEndian",{enumerable:!0,get:function(){return Gn.isLittleEndian}});var $d=Ur();Object.defineProperty(H,"Encoding",{enumerable:!0,get:function(){return $d.Encoding}});var Nd=ts();Object.defineProperty(H,"Builder",{enumerable:!0,get:function(){return Nd.Builder}});var Fd=Wr();Object.defineProperty(H,"ByteBuffer",{enumerable:!0,get:function(){return Fd.ByteBuffer}})});var Kr=P(Lt=>{"use strict";var Cd=Lt&&Lt.__createBinding||(Object.create?function(r,t,n,e){e===void 0&&(e=n);var o=Object.getOwnPropertyDescriptor(t,n);(!o||("get"in o?!t.__esModule:o.writable||o.configurable))&&(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(r,e,o)}:function(r,t,n,e){e===void 0&&(e=n),r[e]=t[n]}),Rd=Lt&&Lt.__setModuleDefault||(Object.create?function(r,t){Object.defineProperty(r,"default",{enumerable:!0,value:t})}:function(r,t){r.default=t}),Gd=Lt&&Lt.__importStar||function(r){if(r&&r.__esModule)return r;var t={};if(r!=null)for(var n in r)n!=="default"&&Object.prototype.hasOwnProperty.call(r,n)&&Cd(t,r,n);return Rd(t,r),t};Object.defineProperty(Lt,"__esModule",{value:!0});Lt.ArgTypeAndIndex=void 0;var kd=Gd(M()),es=Br(),qr=class r{constructor(){this.bb=null,this.bb_pos=0}__init(t,n){return this.bb_pos=t,this.bb=n,this}static getRootAsArgTypeAndIndex(t,n){return(n||new r).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsArgTypeAndIndex(t,n){return t.setPosition(t.position()+kd.SIZE_PREFIX_LENGTH),(n||new r).__init(t.readInt32(t.position())+t.position(),t)}argType(){let t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt8(this.bb_pos+t):es.ArgType.INPUT}index(){let t=this.bb.__offset(this.bb_pos,6);return t?this.bb.readUint32(this.bb_pos+t):0}static startArgTypeAndIndex(t){t.startObject(2)}static addArgType(t,n){t.addFieldInt8(0,n,es.ArgType.INPUT)}static addIndex(t,n){t.addFieldInt32(1,n,0)}static endArgTypeAndIndex(t){return t.endObject()}static createArgTypeAndIndex(t,n,e){return r.startArgTypeAndIndex(t),r.addArgType(t,n),r.addIndex(t,e),r.endArgTypeAndIndex(t)}};Lt.ArgTypeAndIndex=qr});var Xr=P(kn=>{"use strict";Object.defineProperty(kn,"__esModule",{value:!0});kn.AttributeType=void 0;var ns;(function(r){r[r.UNDEFINED=0]="UNDEFINED",r[r.FLOAT=1]="FLOAT",r[r.INT=2]="INT",r[r.STRING=3]="STRING",r[r.TENSOR=4]="TENSOR",r[r.GRAPH=5]="GRAPH",r[r.FLOATS=6]="FLOATS",r[r.INTS=7]="INTS",r[r.STRINGS=8]="STRINGS",r[r.TENSORS=9]="TENSORS",r[r.GRAPHS=10]="GRAPHS",r[r.SPARSE_TENSOR=11]="SPARSE_TENSOR",r[r.SPARSE_TENSORS=12]="SPARSE_TENSORS"})(ns||(kn.AttributeType=ns={}))});var Zr=P(Mn=>{"use strict";Object.defineProperty(Mn,"__esModule",{value:!0});Mn.NodeType=void 0;var rs;(function(r){r[r.Primitive=0]="Primitive",r[r.Fused=1]="Fused"})(rs||(Mn.NodeType=rs={}))});var Yr=P($t=>{"use strict";var Md=$t&&$t.__createBinding||(Object.create?function(r,t,n,e){e===void 0&&(e=n);var o=Object.getOwnPropertyDescriptor(t,n);(!o||("get"in o?!t.__esModule:o.writable||o.configurable))&&(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(r,e,o)}:function(r,t,n,e){e===void 0&&(e=n),r[e]=t[n]}),Vd=$t&&$t.__setModuleDefault||(Object.create?function(r,t){Object.defineProperty(r,"default",{enumerable:!0,value:t})}:function(r,t){r.default=t}),Bd=$t&&$t.__importStar||function(r){if(r&&r.__esModule)return r;var t={};if(r!=null)for(var n in r)n!=="default"&&Object.prototype.hasOwnProperty.call(r,n)&&Md(t,r,n);return Vd(t,r),t};Object.defineProperty($t,"__esModule",{value:!0});$t.Node=void 0;var zd=Bd(M()),Ud=Qr(),os=Zr(),Jr=class r{constructor(){this.bb=null,this.bb_pos=0}__init(t,n){return this.bb_pos=t,this.bb=n,this}static getRootAsNode(t,n){return(n||new r).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsNode(t,n){return t.setPosition(t.position()+zd.SIZE_PREFIX_LENGTH),(n||new r).__init(t.readInt32(t.position())+t.position(),t)}name(t){let n=this.bb.__offset(this.bb_pos,4);return n?this.bb.__string(this.bb_pos+n,t):null}docString(t){let n=this.bb.__offset(this.bb_pos,6);return n?this.bb.__string(this.bb_pos+n,t):null}domain(t){let n=this.bb.__offset(this.bb_pos,8);return n?this.bb.__string(this.bb_pos+n,t):null}sinceVersion(){let t=this.bb.__offset(this.bb_pos,10);return t?this.bb.readInt32(this.bb_pos+t):0}index(){let t=this.bb.__offset(this.bb_pos,12);return t?this.bb.readUint32(this.bb_pos+t):0}opType(t){let n=this.bb.__offset(this.bb_pos,14);return n?this.bb.__string(this.bb_pos+n,t):null}type(){let t=this.bb.__offset(this.bb_pos,16);return t?this.bb.readInt32(this.bb_pos+t):os.NodeType.Primitive}executionProviderType(t){let n=this.bb.__offset(this.bb_pos,18);return n?this.bb.__string(this.bb_pos+n,t):null}inputs(t,n){let e=this.bb.__offset(this.bb_pos,20);return e?this.bb.__string(this.bb.__vector(this.bb_pos+e)+t*4,n):null}inputsLength(){let t=this.bb.__offset(this.bb_pos,20);return t?this.bb.__vector_len(this.bb_pos+t):0}outputs(t,n){let e=this.bb.__offset(this.bb_pos,22);return e?this.bb.__string(this.bb.__vector(this.bb_pos+e)+t*4,n):null}outputsLength(){let t=this.bb.__offset(this.bb_pos,22);return t?this.bb.__vector_len(this.bb_pos+t):0}attributes(t,n){let e=this.bb.__offset(this.bb_pos,24);return e?(n||new Ud.Attribute).__init(this.bb.__indirect(this.bb.__vector(this.bb_pos+e)+t*4),this.bb):null}attributesLength(){let t=this.bb.__offset(this.bb_pos,24);return t?this.bb.__vector_len(this.bb_pos+t):0}inputArgCounts(t){let n=this.bb.__offset(this.bb_pos,26);return n?this.bb.readInt32(this.bb.__vector(this.bb_pos+n)+t*4):0}inputArgCountsLength(){let t=this.bb.__offset(this.bb_pos,26);return t?this.bb.__vector_len(this.bb_pos+t):0}inputArgCountsArray(){let t=this.bb.__offset(this.bb_pos,26);return t?new Int32Array(this.bb.bytes().buffer,this.bb.bytes().byteOffset+this.bb.__vector(this.bb_pos+t),this.bb.__vector_len(this.bb_pos+t)):null}implicitInputs(t,n){let e=this.bb.__offset(this.bb_pos,28);return e?this.bb.__string(this.bb.__vector(this.bb_pos+e)+t*4,n):null}implicitInputsLength(){let t=this.bb.__offset(this.bb_pos,28);return t?this.bb.__vector_len(this.bb_pos+t):0}static startNode(t){t.startObject(13)}static addName(t,n){t.addFieldOffset(0,n,0)}static addDocString(t,n){t.addFieldOffset(1,n,0)}static addDomain(t,n){t.addFieldOffset(2,n,0)}static addSinceVersion(t,n){t.addFieldInt32(3,n,0)}static addIndex(t,n){t.addFieldInt32(4,n,0)}static addOpType(t,n){t.addFieldOffset(5,n,0)}static addType(t,n){t.addFieldInt32(6,n,os.NodeType.Primitive)}static addExecutionProviderType(t,n){t.addFieldOffset(7,n,0)}static addInputs(t,n){t.addFieldOffset(8,n,0)}static createInputsVector(t,n){t.startVector(4,n.length,4);for(let e=n.length-1;e>=0;e--)t.addOffset(n[e]);return t.endVector()}static startInputsVector(t,n){t.startVector(4,n,4)}static addOutputs(t,n){t.addFieldOffset(9,n,0)}static createOutputsVector(t,n){t.startVector(4,n.length,4);for(let e=n.length-1;e>=0;e--)t.addOffset(n[e]);return t.endVector()}static startOutputsVector(t,n){t.startVector(4,n,4)}static addAttributes(t,n){t.addFieldOffset(10,n,0)}static createAttributesVector(t,n){t.startVector(4,n.length,4);for(let e=n.length-1;e>=0;e--)t.addOffset(n[e]);return t.endVector()}static startAttributesVector(t,n){t.startVector(4,n,4)}static addInputArgCounts(t,n){t.addFieldOffset(11,n,0)}static createInputArgCountsVector(t,n){t.startVector(4,n.length,4);for(let e=n.length-1;e>=0;e--)t.addInt32(n[e]);return t.endVector()}static startInputArgCountsVector(t,n){t.startVector(4,n,4)}static addImplicitInputs(t,n){t.addFieldOffset(12,n,0)}static createImplicitInputsVector(t,n){t.startVector(4,n.length,4);for(let e=n.length-1;e>=0;e--)t.addOffset(n[e]);return t.endVector()}static startImplicitInputsVector(t,n){t.startVector(4,n,4)}static endNode(t){return t.endObject()}static createNode(t,n,e,o,i,a,s,u,l,c,d,p,m,b){return r.startNode(t),r.addName(t,n),r.addDocString(t,e),r.addDomain(t,o),r.addSinceVersion(t,i),r.addIndex(t,a),r.addOpType(t,s),r.addType(t,u),r.addExecutionProviderType(t,l),r.addInputs(t,c),r.addOutputs(t,d),r.addAttributes(t,p),r.addInputArgCounts(t,m),r.addImplicitInputs(t,b),r.endNode(t)}};$t.Node=Jr});var eo=P(Vn=>{"use strict";Object.defineProperty(Vn,"__esModule",{value:!0});Vn.EdgeEnd=void 0;var to=class{constructor(){this.bb=null,this.bb_pos=0}__init(t,n){return this.bb_pos=t,this.bb=n,this}nodeIndex(){return this.bb.readUint32(this.bb_pos)}srcArgIndex(){return this.bb.readInt32(this.bb_pos+4)}dstArgIndex(){return this.bb.readInt32(this.bb_pos+8)}static sizeOf(){return 12}static createEdgeEnd(t,n,e,o){return t.prep(4,12),t.writeInt32(o),t.writeInt32(e),t.writeInt32(n),t.offset()}};Vn.EdgeEnd=to});var ro=P(Nt=>{"use strict";var jd=Nt&&Nt.__createBinding||(Object.create?function(r,t,n,e){e===void 0&&(e=n);var o=Object.getOwnPropertyDescriptor(t,n);(!o||("get"in o?!t.__esModule:o.writable||o.configurable))&&(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(r,e,o)}:function(r,t,n,e){e===void 0&&(e=n),r[e]=t[n]}),Wd=Nt&&Nt.__setModuleDefault||(Object.create?function(r,t){Object.defineProperty(r,"default",{enumerable:!0,value:t})}:function(r,t){r.default=t}),Hd=Nt&&Nt.__importStar||function(r){if(r&&r.__esModule)return r;var t={};if(r!=null)for(var n in r)n!=="default"&&Object.prototype.hasOwnProperty.call(r,n)&&jd(t,r,n);return Wd(t,r),t};Object.defineProperty(Nt,"__esModule",{value:!0});Nt.NodeEdge=void 0;var qd=Hd(M()),is=eo(),no=class r{constructor(){this.bb=null,this.bb_pos=0}__init(t,n){return this.bb_pos=t,this.bb=n,this}static getRootAsNodeEdge(t,n){return(n||new r).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsNodeEdge(t,n){return t.setPosition(t.position()+qd.SIZE_PREFIX_LENGTH),(n||new r).__init(t.readInt32(t.position())+t.position(),t)}nodeIndex(){let t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readUint32(this.bb_pos+t):0}inputEdges(t,n){let e=this.bb.__offset(this.bb_pos,6);return e?(n||new is.EdgeEnd).__init(this.bb.__vector(this.bb_pos+e)+t*12,this.bb):null}inputEdgesLength(){let t=this.bb.__offset(this.bb_pos,6);return t?this.bb.__vector_len(this.bb_pos+t):0}outputEdges(t,n){let e=this.bb.__offset(this.bb_pos,8);return e?(n||new is.EdgeEnd).__init(this.bb.__vector(this.bb_pos+e)+t*12,this.bb):null}outputEdgesLength(){let t=this.bb.__offset(this.bb_pos,8);return t?this.bb.__vector_len(this.bb_pos+t):0}static startNodeEdge(t){t.startObject(3)}static addNodeIndex(t,n){t.addFieldInt32(0,n,0)}static addInputEdges(t,n){t.addFieldOffset(1,n,0)}static startInputEdgesVector(t,n){t.startVector(12,n,4)}static addOutputEdges(t,n){t.addFieldOffset(2,n,0)}static startOutputEdgesVector(t,n){t.startVector(12,n,4)}static endNodeEdge(t){return t.endObject()}static createNodeEdge(t,n,e,o){return r.startNodeEdge(t),r.addNodeIndex(t,n),r.addInputEdges(t,e),r.addOutputEdges(t,o),r.endNodeEdge(t)}};Nt.NodeEdge=no});var io=P(Ft=>{"use strict";var Kd=Ft&&Ft.__createBinding||(Object.create?function(r,t,n,e){e===void 0&&(e=n);var o=Object.getOwnPropertyDescriptor(t,n);(!o||("get"in o?!t.__esModule:o.writable||o.configurable))&&(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(r,e,o)}:function(r,t,n,e){e===void 0&&(e=n),r[e]=t[n]}),Xd=Ft&&Ft.__setModuleDefault||(Object.create?function(r,t){Object.defineProperty(r,"default",{enumerable:!0,value:t})}:function(r,t){r.default=t}),Zd=Ft&&Ft.__importStar||function(r){if(r&&r.__esModule)return r;var t={};if(r!=null)for(var n in r)n!=="default"&&Object.prototype.hasOwnProperty.call(r,n)&&Kd(t,r,n);return Xd(t,r),t};Object.defineProperty(Ft,"__esModule",{value:!0});Ft.NodesToOptimizeIndices=void 0;var Jd=Zd(M()),oo=class r{constructor(){this.bb=null,this.bb_pos=0}__init(t,n){return this.bb_pos=t,this.bb=n,this}static getRootAsNodesToOptimizeIndices(t,n){return(n||new r).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsNodesToOptimizeIndices(t,n){return t.setPosition(t.position()+Jd.SIZE_PREFIX_LENGTH),(n||new r).__init(t.readInt32(t.position())+t.position(),t)}nodeIndices(t){let n=this.bb.__offset(this.bb_pos,4);return n?this.bb.readUint32(this.bb.__vector(this.bb_pos+n)+t*4):0}nodeIndicesLength(){let t=this.bb.__offset(this.bb_pos,4);return t?this.bb.__vector_len(this.bb_pos+t):0}nodeIndicesArray(){let t=this.bb.__offset(this.bb_pos,4);return t?new Uint32Array(this.bb.bytes().buffer,this.bb.bytes().byteOffset+this.bb.__vector(this.bb_pos+t),this.bb.__vector_len(this.bb_pos+t)):null}numInputs(){let t=this.bb.__offset(this.bb_pos,6);return t?this.bb.readUint32(this.bb_pos+t):0}numOutputs(){let t=this.bb.__offset(this.bb_pos,8);return t?this.bb.readUint32(this.bb_pos+t):0}hasVariadicInput(){let t=this.bb.__offset(this.bb_pos,10);return t?!!this.bb.readInt8(this.bb_pos+t):!1}hasVariadicOutput(){let t=this.bb.__offset(this.bb_pos,12);return t?!!this.bb.readInt8(this.bb_pos+t):!1}numVariadicInputs(){let t=this.bb.__offset(this.bb_pos,14);return t?this.bb.readUint32(this.bb_pos+t):0}numVariadicOutputs(){let t=this.bb.__offset(this.bb_pos,16);return t?this.bb.readUint32(this.bb_pos+t):0}static startNodesToOptimizeIndices(t){t.startObject(7)}static addNodeIndices(t,n){t.addFieldOffset(0,n,0)}static createNodeIndicesVector(t,n){t.startVector(4,n.length,4);for(let e=n.length-1;e>=0;e--)t.addInt32(n[e]);return t.endVector()}static startNodeIndicesVector(t,n){t.startVector(4,n,4)}static addNumInputs(t,n){t.addFieldInt32(1,n,0)}static addNumOutputs(t,n){t.addFieldInt32(2,n,0)}static addHasVariadicInput(t,n){t.addFieldInt8(3,+n,0)}static addHasVariadicOutput(t,n){t.addFieldInt8(4,+n,0)}static addNumVariadicInputs(t,n){t.addFieldInt32(5,n,0)}static addNumVariadicOutputs(t,n){t.addFieldInt32(6,n,0)}static endNodesToOptimizeIndices(t){return t.endObject()}static createNodesToOptimizeIndices(t,n,e,o,i,a,s,u){return r.startNodesToOptimizeIndices(t),r.addNodeIndices(t,n),r.addNumInputs(t,e),r.addNumOutputs(t,o),r.addHasVariadicInput(t,i),r.addHasVariadicOutput(t,a),r.addNumVariadicInputs(t,s),r.addNumVariadicOutputs(t,u),r.endNodesToOptimizeIndices(t)}};Ft.NodesToOptimizeIndices=oo});var so=P(Ct=>{"use strict";var Yd=Ct&&Ct.__createBinding||(Object.create?function(r,t,n,e){e===void 0&&(e=n);var o=Object.getOwnPropertyDescriptor(t,n);(!o||("get"in o?!t.__esModule:o.writable||o.configurable))&&(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(r,e,o)}:function(r,t,n,e){e===void 0&&(e=n),r[e]=t[n]}),Qd=Ct&&Ct.__setModuleDefault||(Object.create?function(r,t){Object.defineProperty(r,"default",{enumerable:!0,value:t})}:function(r,t){r.default=t}),tp=Ct&&Ct.__importStar||function(r){if(r&&r.__esModule)return r;var t={};if(r!=null)for(var n in r)n!=="default"&&Object.prototype.hasOwnProperty.call(r,n)&&Yd(t,r,n);return Qd(t,r),t};Object.defineProperty(Ct,"__esModule",{value:!0});Ct.RuntimeOptimizationRecord=void 0;var ep=tp(M()),np=io(),ao=class r{constructor(){this.bb=null,this.bb_pos=0}__init(t,n){return this.bb_pos=t,this.bb=n,this}static getRootAsRuntimeOptimizationRecord(t,n){return(n||new r).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsRuntimeOptimizationRecord(t,n){return t.setPosition(t.position()+ep.SIZE_PREFIX_LENGTH),(n||new r).__init(t.readInt32(t.position())+t.position(),t)}actionId(t){let n=this.bb.__offset(this.bb_pos,4);return n?this.bb.__string(this.bb_pos+n,t):null}nodesToOptimizeIndices(t){let n=this.bb.__offset(this.bb_pos,6);return n?(t||new np.NodesToOptimizeIndices).__init(this.bb.__indirect(this.bb_pos+n),this.bb):null}producedOpIds(t,n){let e=this.bb.__offset(this.bb_pos,10);return e?this.bb.__string(this.bb.__vector(this.bb_pos+e)+t*4,n):null}producedOpIdsLength(){let t=this.bb.__offset(this.bb_pos,10);return t?this.bb.__vector_len(this.bb_pos+t):0}static startRuntimeOptimizationRecord(t){t.startObject(4)}static addActionId(t,n){t.addFieldOffset(0,n,0)}static addNodesToOptimizeIndices(t,n){t.addFieldOffset(1,n,0)}static addProducedOpIds(t,n){t.addFieldOffset(3,n,0)}static createProducedOpIdsVector(t,n){t.startVector(4,n.length,4);for(let e=n.length-1;e>=0;e--)t.addOffset(n[e]);return t.endVector()}static startProducedOpIdsVector(t,n){t.startVector(4,n,4)}static endRuntimeOptimizationRecord(t){return t.endObject()}};Ct.RuntimeOptimizationRecord=ao});var lo=P(Rt=>{"use strict";var rp=Rt&&Rt.__createBinding||(Object.create?function(r,t,n,e){e===void 0&&(e=n);var o=Object.getOwnPropertyDescriptor(t,n);(!o||("get"in o?!t.__esModule:o.writable||o.configurable))&&(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(r,e,o)}:function(r,t,n,e){e===void 0&&(e=n),r[e]=t[n]}),op=Rt&&Rt.__setModuleDefault||(Object.create?function(r,t){Object.defineProperty(r,"default",{enumerable:!0,value:t})}:function(r,t){r.default=t}),ip=Rt&&Rt.__importStar||function(r){if(r&&r.__esModule)return r;var t={};if(r!=null)for(var n in r)n!=="default"&&Object.prototype.hasOwnProperty.call(r,n)&&rp(t,r,n);return op(t,r),t};Object.defineProperty(Rt,"__esModule",{value:!0});Rt.RuntimeOptimizationRecordContainerEntry=void 0;var ap=ip(M()),sp=so(),uo=class r{constructor(){this.bb=null,this.bb_pos=0}__init(t,n){return this.bb_pos=t,this.bb=n,this}static getRootAsRuntimeOptimizationRecordContainerEntry(t,n){return(n||new r).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsRuntimeOptimizationRecordContainerEntry(t,n){return t.setPosition(t.position()+ap.SIZE_PREFIX_LENGTH),(n||new r).__init(t.readInt32(t.position())+t.position(),t)}optimizerName(t){let n=this.bb.__offset(this.bb_pos,4);return n?this.bb.__string(this.bb_pos+n,t):null}runtimeOptimizationRecords(t,n){let e=this.bb.__offset(this.bb_pos,6);return e?(n||new sp.RuntimeOptimizationRecord).__init(this.bb.__indirect(this.bb.__vector(this.bb_pos+e)+t*4),this.bb):null}runtimeOptimizationRecordsLength(){let t=this.bb.__offset(this.bb_pos,6);return t?this.bb.__vector_len(this.bb_pos+t):0}static startRuntimeOptimizationRecordContainerEntry(t){t.startObject(2)}static addOptimizerName(t,n){t.addFieldOffset(0,n,0)}static addRuntimeOptimizationRecords(t,n){t.addFieldOffset(1,n,0)}static createRuntimeOptimizationRecordsVector(t,n){t.startVector(4,n.length,4);for(let e=n.length-1;e>=0;e--)t.addOffset(n[e]);return t.endVector()}static startRuntimeOptimizationRecordsVector(t,n){t.startVector(4,n,4)}static endRuntimeOptimizationRecordContainerEntry(t){let n=t.endObject();return t.requiredField(n,4),n}static createRuntimeOptimizationRecordContainerEntry(t,n,e){return r.startRuntimeOptimizationRecordContainerEntry(t),r.addOptimizerName(t,n),r.addRuntimeOptimizationRecords(t,e),r.endRuntimeOptimizationRecordContainerEntry(t)}};Rt.RuntimeOptimizationRecordContainerEntry=uo});var fo=P(Gt=>{"use strict";var up=Gt&&Gt.__createBinding||(Object.create?function(r,t,n,e){e===void 0&&(e=n);var o=Object.getOwnPropertyDescriptor(t,n);(!o||("get"in o?!t.__esModule:o.writable||o.configurable))&&(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(r,e,o)}:function(r,t,n,e){e===void 0&&(e=n),r[e]=t[n]}),lp=Gt&&Gt.__setModuleDefault||(Object.create?function(r,t){Object.defineProperty(r,"default",{enumerable:!0,value:t})}:function(r,t){r.default=t}),cp=Gt&&Gt.__importStar||function(r){if(r&&r.__esModule)return r;var t={};if(r!=null)for(var n in r)n!=="default"&&Object.prototype.hasOwnProperty.call(r,n)&&up(t,r,n);return lp(t,r),t};Object.defineProperty(Gt,"__esModule",{value:!0});Gt.RuntimeOptimizations=void 0;var fp=cp(M()),dp=lo(),co=class r{constructor(){this.bb=null,this.bb_pos=0}__init(t,n){return this.bb_pos=t,this.bb=n,this}static getRootAsRuntimeOptimizations(t,n){return(n||new r).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsRuntimeOptimizations(t,n){return t.setPosition(t.position()+fp.SIZE_PREFIX_LENGTH),(n||new r).__init(t.readInt32(t.position())+t.position(),t)}records(t,n){let e=this.bb.__offset(this.bb_pos,4);return e?(n||new dp.RuntimeOptimizationRecordContainerEntry).__init(this.bb.__indirect(this.bb.__vector(this.bb_pos+e)+t*4),this.bb):null}recordsLength(){let t=this.bb.__offset(this.bb_pos,4);return t?this.bb.__vector_len(this.bb_pos+t):0}static startRuntimeOptimizations(t){t.startObject(1)}static addRecords(t,n){t.addFieldOffset(0,n,0)}static createRecordsVector(t,n){t.startVector(4,n.length,4);for(let e=n.length-1;e>=0;e--)t.addOffset(n[e]);return t.endVector()}static startRecordsVector(t,n){t.startVector(4,n,4)}static endRuntimeOptimizations(t){return t.endObject()}static createRuntimeOptimizations(t,n){return r.startRuntimeOptimizations(t),r.addRecords(t,n),r.endRuntimeOptimizations(t)}};Gt.RuntimeOptimizations=co});var tn=P(Bn=>{"use strict";Object.defineProperty(Bn,"__esModule",{value:!0});Bn.TensorDataType=void 0;var as;(function(r){r[r.UNDEFINED=0]="UNDEFINED",r[r.FLOAT=1]="FLOAT",r[r.UINT8=2]="UINT8",r[r.INT8=3]="INT8",r[r.UINT16=4]="UINT16",r[r.INT16=5]="INT16",r[r.INT32=6]="INT32",r[r.INT64=7]="INT64",r[r.STRING=8]="STRING",r[r.BOOL=9]="BOOL",r[r.FLOAT16=10]="FLOAT16",r[r.DOUBLE=11]="DOUBLE",r[r.UINT32=12]="UINT32",r[r.UINT64=13]="UINT64",r[r.COMPLEX64=14]="COMPLEX64",r[r.COMPLEX128=15]="COMPLEX128",r[r.BFLOAT16=16]="BFLOAT16",r[r.FLOAT8E4M3FN=17]="FLOAT8E4M3FN",r[r.FLOAT8E4M3FNUZ=18]="FLOAT8E4M3FNUZ",r[r.FLOAT8E5M2=19]="FLOAT8E5M2",r[r.FLOAT8E5M2FNUZ=20]="FLOAT8E5M2FNUZ"})(as||(Bn.TensorDataType=as={}))});var en=P(kt=>{"use strict";var pp=kt&&kt.__createBinding||(Object.create?function(r,t,n,e){e===void 0&&(e=n);var o=Object.getOwnPropertyDescriptor(t,n);(!o||("get"in o?!t.__esModule:o.writable||o.configurable))&&(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(r,e,o)}:function(r,t,n,e){e===void 0&&(e=n),r[e]=t[n]}),hp=kt&&kt.__setModuleDefault||(Object.create?function(r,t){Object.defineProperty(r,"default",{enumerable:!0,value:t})}:function(r,t){r.default=t}),mp=kt&&kt.__importStar||function(r){if(r&&r.__esModule)return r;var t={};if(r!=null)for(var n in r)n!=="default"&&Object.prototype.hasOwnProperty.call(r,n)&&pp(t,r,n);return hp(t,r),t};Object.defineProperty(kt,"__esModule",{value:!0});kt.Tensor=void 0;var bp=mp(M()),ss=tn(),po=class r{constructor(){this.bb=null,this.bb_pos=0}__init(t,n){return this.bb_pos=t,this.bb=n,this}static getRootAsTensor(t,n){return(n||new r).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsTensor(t,n){return t.setPosition(t.position()+bp.SIZE_PREFIX_LENGTH),(n||new r).__init(t.readInt32(t.position())+t.position(),t)}name(t){let n=this.bb.__offset(this.bb_pos,4);return n?this.bb.__string(this.bb_pos+n,t):null}docString(t){let n=this.bb.__offset(this.bb_pos,6);return n?this.bb.__string(this.bb_pos+n,t):null}dims(t){let n=this.bb.__offset(this.bb_pos,8);return n?this.bb.readInt64(this.bb.__vector(this.bb_pos+n)+t*8):BigInt(0)}dimsLength(){let t=this.bb.__offset(this.bb_pos,8);return t?this.bb.__vector_len(this.bb_pos+t):0}dataType(){let t=this.bb.__offset(this.bb_pos,10);return t?this.bb.readInt32(this.bb_pos+t):ss.TensorDataType.UNDEFINED}rawData(t){let n=this.bb.__offset(this.bb_pos,12);return n?this.bb.readUint8(this.bb.__vector(this.bb_pos+n)+t):0}rawDataLength(){let t=this.bb.__offset(this.bb_pos,12);return t?this.bb.__vector_len(this.bb_pos+t):0}rawDataArray(){let t=this.bb.__offset(this.bb_pos,12);return t?new Uint8Array(this.bb.bytes().buffer,this.bb.bytes().byteOffset+this.bb.__vector(this.bb_pos+t),this.bb.__vector_len(this.bb_pos+t)):null}stringData(t,n){let e=this.bb.__offset(this.bb_pos,14);return e?this.bb.__string(this.bb.__vector(this.bb_pos+e)+t*4,n):null}stringDataLength(){let t=this.bb.__offset(this.bb_pos,14);return t?this.bb.__vector_len(this.bb_pos+t):0}externalDataOffset(){let t=this.bb.__offset(this.bb_pos,16);return t?this.bb.readInt64(this.bb_pos+t):BigInt("-1")}static startTensor(t){t.startObject(7)}static addName(t,n){t.addFieldOffset(0,n,0)}static addDocString(t,n){t.addFieldOffset(1,n,0)}static addDims(t,n){t.addFieldOffset(2,n,0)}static createDimsVector(t,n){t.startVector(8,n.length,8);for(let e=n.length-1;e>=0;e--)t.addInt64(n[e]);return t.endVector()}static startDimsVector(t,n){t.startVector(8,n,8)}static addDataType(t,n){t.addFieldInt32(3,n,ss.TensorDataType.UNDEFINED)}static addRawData(t,n){t.addFieldOffset(4,n,0)}static createRawDataVector(t,n){t.startVector(1,n.length,1);for(let e=n.length-1;e>=0;e--)t.addInt8(n[e]);return t.endVector()}static startRawDataVector(t,n){t.startVector(1,n,1)}static addStringData(t,n){t.addFieldOffset(5,n,0)}static createStringDataVector(t,n){t.startVector(4,n.length,4);for(let e=n.length-1;e>=0;e--)t.addOffset(n[e]);return t.endVector()}static startStringDataVector(t,n){t.startVector(4,n,4)}static addExternalDataOffset(t,n){t.addFieldInt64(6,n,BigInt("-1"))}static endTensor(t){return t.endObject()}static createTensor(t,n,e,o,i,a,s,u){return r.startTensor(t),r.addName(t,n),r.addDocString(t,e),r.addDims(t,o),r.addDataType(t,i),r.addRawData(t,a),r.addStringData(t,s),r.addExternalDataOffset(t,u),r.endTensor(t)}};kt.Tensor=po});var mo=P(Mt=>{"use strict";var gp=Mt&&Mt.__createBinding||(Object.create?function(r,t,n,e){e===void 0&&(e=n);var o=Object.getOwnPropertyDescriptor(t,n);(!o||("get"in o?!t.__esModule:o.writable||o.configurable))&&(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(r,e,o)}:function(r,t,n,e){e===void 0&&(e=n),r[e]=t[n]}),yp=Mt&&Mt.__setModuleDefault||(Object.create?function(r,t){Object.defineProperty(r,"default",{enumerable:!0,value:t})}:function(r,t){r.default=t}),Tp=Mt&&Mt.__importStar||function(r){if(r&&r.__esModule)return r;var t={};if(r!=null)for(var n in r)n!=="default"&&Object.prototype.hasOwnProperty.call(r,n)&&gp(t,r,n);return yp(t,r),t};Object.defineProperty(Mt,"__esModule",{value:!0});Mt.SparseTensor=void 0;var _p=Tp(M()),us=en(),ho=class r{constructor(){this.bb=null,this.bb_pos=0}__init(t,n){return this.bb_pos=t,this.bb=n,this}static getRootAsSparseTensor(t,n){return(n||new r).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsSparseTensor(t,n){return t.setPosition(t.position()+_p.SIZE_PREFIX_LENGTH),(n||new r).__init(t.readInt32(t.position())+t.position(),t)}values(t){let n=this.bb.__offset(this.bb_pos,4);return n?(t||new us.Tensor).__init(this.bb.__indirect(this.bb_pos+n),this.bb):null}indices(t){let n=this.bb.__offset(this.bb_pos,6);return n?(t||new us.Tensor).__init(this.bb.__indirect(this.bb_pos+n),this.bb):null}dims(t){let n=this.bb.__offset(this.bb_pos,8);return n?this.bb.readInt64(this.bb.__vector(this.bb_pos+n)+t*8):BigInt(0)}dimsLength(){let t=this.bb.__offset(this.bb_pos,8);return t?this.bb.__vector_len(this.bb_pos+t):0}static startSparseTensor(t){t.startObject(3)}static addValues(t,n){t.addFieldOffset(0,n,0)}static addIndices(t,n){t.addFieldOffset(1,n,0)}static addDims(t,n){t.addFieldOffset(2,n,0)}static createDimsVector(t,n){t.startVector(8,n.length,8);for(let e=n.length-1;e>=0;e--)t.addInt64(n[e]);return t.endVector()}static startDimsVector(t,n){t.startVector(8,n,8)}static endSparseTensor(t){return t.endObject()}};Mt.SparseTensor=ho});var go=P(Vt=>{"use strict";var xp=Vt&&Vt.__createBinding||(Object.create?function(r,t,n,e){e===void 0&&(e=n);var o=Object.getOwnPropertyDescriptor(t,n);(!o||("get"in o?!t.__esModule:o.writable||o.configurable))&&(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(r,e,o)}:function(r,t,n,e){e===void 0&&(e=n),r[e]=t[n]}),vp=Vt&&Vt.__setModuleDefault||(Object.create?function(r,t){Object.defineProperty(r,"default",{enumerable:!0,value:t})}:function(r,t){r.default=t}),wp=Vt&&Vt.__importStar||function(r){if(r&&r.__esModule)return r;var t={};if(r!=null)for(var n in r)n!=="default"&&Object.prototype.hasOwnProperty.call(r,n)&&xp(t,r,n);return vp(t,r),t};Object.defineProperty(Vt,"__esModule",{value:!0});Vt.MapType=void 0;var Ip=wp(M()),ls=tn(),Op=nn(),bo=class r{constructor(){this.bb=null,this.bb_pos=0}__init(t,n){return this.bb_pos=t,this.bb=n,this}static getRootAsMapType(t,n){return(n||new r).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsMapType(t,n){return t.setPosition(t.position()+Ip.SIZE_PREFIX_LENGTH),(n||new r).__init(t.readInt32(t.position())+t.position(),t)}keyType(){let t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt32(this.bb_pos+t):ls.TensorDataType.UNDEFINED}valueType(t){let n=this.bb.__offset(this.bb_pos,6);return n?(t||new Op.TypeInfo).__init(this.bb.__indirect(this.bb_pos+n),this.bb):null}static startMapType(t){t.startObject(2)}static addKeyType(t,n){t.addFieldInt32(0,n,ls.TensorDataType.UNDEFINED)}static addValueType(t,n){t.addFieldOffset(1,n,0)}static endMapType(t){return t.endObject()}};Vt.MapType=bo});var To=P(Bt=>{"use strict";var Pp=Bt&&Bt.__createBinding||(Object.create?function(r,t,n,e){e===void 0&&(e=n);var o=Object.getOwnPropertyDescriptor(t,n);(!o||("get"in o?!t.__esModule:o.writable||o.configurable))&&(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(r,e,o)}:function(r,t,n,e){e===void 0&&(e=n),r[e]=t[n]}),Sp=Bt&&Bt.__setModuleDefault||(Object.create?function(r,t){Object.defineProperty(r,"default",{enumerable:!0,value:t})}:function(r,t){r.default=t}),Ap=Bt&&Bt.__importStar||function(r){if(r&&r.__esModule)return r;var t={};if(r!=null)for(var n in r)n!=="default"&&Object.prototype.hasOwnProperty.call(r,n)&&Pp(t,r,n);return Sp(t,r),t};Object.defineProperty(Bt,"__esModule",{value:!0});Bt.SequenceType=void 0;var Ep=Ap(M()),Dp=nn(),yo=class r{constructor(){this.bb=null,this.bb_pos=0}__init(t,n){return this.bb_pos=t,this.bb=n,this}static getRootAsSequenceType(t,n){return(n||new r).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsSequenceType(t,n){return t.setPosition(t.position()+Ep.SIZE_PREFIX_LENGTH),(n||new r).__init(t.readInt32(t.position())+t.position(),t)}elemType(t){let n=this.bb.__offset(this.bb_pos,4);return n?(t||new Dp.TypeInfo).__init(this.bb.__indirect(this.bb_pos+n),this.bb):null}static startSequenceType(t){t.startObject(1)}static addElemType(t,n){t.addFieldOffset(0,n,0)}static endSequenceType(t){return t.endObject()}static createSequenceType(t,n){return r.startSequenceType(t),r.addElemType(t,n),r.endSequenceType(t)}};Bt.SequenceType=yo});var _o=P(zn=>{"use strict";Object.defineProperty(zn,"__esModule",{value:!0});zn.DimensionValueType=void 0;var cs;(function(r){r[r.UNKNOWN=0]="UNKNOWN",r[r.VALUE=1]="VALUE",r[r.PARAM=2]="PARAM"})(cs||(zn.DimensionValueType=cs={}))});var vo=P(zt=>{"use strict";var Lp=zt&&zt.__createBinding||(Object.create?function(r,t,n,e){e===void 0&&(e=n);var o=Object.getOwnPropertyDescriptor(t,n);(!o||("get"in o?!t.__esModule:o.writable||o.configurable))&&(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(r,e,o)}:function(r,t,n,e){e===void 0&&(e=n),r[e]=t[n]}),$p=zt&&zt.__setModuleDefault||(Object.create?function(r,t){Object.defineProperty(r,"default",{enumerable:!0,value:t})}:function(r,t){r.default=t}),Np=zt&&zt.__importStar||function(r){if(r&&r.__esModule)return r;var t={};if(r!=null)for(var n in r)n!=="default"&&Object.prototype.hasOwnProperty.call(r,n)&&Lp(t,r,n);return $p(t,r),t};Object.defineProperty(zt,"__esModule",{value:!0});zt.DimensionValue=void 0;var Fp=Np(M()),fs=_o(),xo=class r{constructor(){this.bb=null,this.bb_pos=0}__init(t,n){return this.bb_pos=t,this.bb=n,this}static getRootAsDimensionValue(t,n){return(n||new r).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsDimensionValue(t,n){return t.setPosition(t.position()+Fp.SIZE_PREFIX_LENGTH),(n||new r).__init(t.readInt32(t.position())+t.position(),t)}dimType(){let t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt8(this.bb_pos+t):fs.DimensionValueType.UNKNOWN}dimValue(){let t=this.bb.__offset(this.bb_pos,6);return t?this.bb.readInt64(this.bb_pos+t):BigInt("0")}dimParam(t){let n=this.bb.__offset(this.bb_pos,8);return n?this.bb.__string(this.bb_pos+n,t):null}static startDimensionValue(t){t.startObject(3)}static addDimType(t,n){t.addFieldInt8(0,n,fs.DimensionValueType.UNKNOWN)}static addDimValue(t,n){t.addFieldInt64(1,n,BigInt("0"))}static addDimParam(t,n){t.addFieldOffset(2,n,0)}static endDimensionValue(t){return t.endObject()}static createDimensionValue(t,n,e,o){return r.startDimensionValue(t),r.addDimType(t,n),r.addDimValue(t,e),r.addDimParam(t,o),r.endDimensionValue(t)}};zt.DimensionValue=xo});var Io=P(Ut=>{"use strict";var Cp=Ut&&Ut.__createBinding||(Object.create?function(r,t,n,e){e===void 0&&(e=n);var o=Object.getOwnPropertyDescriptor(t,n);(!o||("get"in o?!t.__esModule:o.writable||o.configurable))&&(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(r,e,o)}:function(r,t,n,e){e===void 0&&(e=n),r[e]=t[n]}),Rp=Ut&&Ut.__setModuleDefault||(Object.create?function(r,t){Object.defineProperty(r,"default",{enumerable:!0,value:t})}:function(r,t){r.default=t}),Gp=Ut&&Ut.__importStar||function(r){if(r&&r.__esModule)return r;var t={};if(r!=null)for(var n in r)n!=="default"&&Object.prototype.hasOwnProperty.call(r,n)&&Cp(t,r,n);return Rp(t,r),t};Object.defineProperty(Ut,"__esModule",{value:!0});Ut.Dimension=void 0;var kp=Gp(M()),Mp=vo(),wo=class r{constructor(){this.bb=null,this.bb_pos=0}__init(t,n){return this.bb_pos=t,this.bb=n,this}static getRootAsDimension(t,n){return(n||new r).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsDimension(t,n){return t.setPosition(t.position()+kp.SIZE_PREFIX_LENGTH),(n||new r).__init(t.readInt32(t.position())+t.position(),t)}value(t){let n=this.bb.__offset(this.bb_pos,4);return n?(t||new Mp.DimensionValue).__init(this.bb.__indirect(this.bb_pos+n),this.bb):null}denotation(t){let n=this.bb.__offset(this.bb_pos,6);return n?this.bb.__string(this.bb_pos+n,t):null}static startDimension(t){t.startObject(2)}static addValue(t,n){t.addFieldOffset(0,n,0)}static addDenotation(t,n){t.addFieldOffset(1,n,0)}static endDimension(t){return t.endObject()}static createDimension(t,n,e){return r.startDimension(t),r.addValue(t,n),r.addDenotation(t,e),r.endDimension(t)}};Ut.Dimension=wo});var Po=P(jt=>{"use strict";var Vp=jt&&jt.__createBinding||(Object.create?function(r,t,n,e){e===void 0&&(e=n);var o=Object.getOwnPropertyDescriptor(t,n);(!o||("get"in o?!t.__esModule:o.writable||o.configurable))&&(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(r,e,o)}:function(r,t,n,e){e===void 0&&(e=n),r[e]=t[n]}),Bp=jt&&jt.__setModuleDefault||(Object.create?function(r,t){Object.defineProperty(r,"default",{enumerable:!0,value:t})}:function(r,t){r.default=t}),zp=jt&&jt.__importStar||function(r){if(r&&r.__esModule)return r;var t={};if(r!=null)for(var n in r)n!=="default"&&Object.prototype.hasOwnProperty.call(r,n)&&Vp(t,r,n);return Bp(t,r),t};Object.defineProperty(jt,"__esModule",{value:!0});jt.Shape=void 0;var Up=zp(M()),jp=Io(),Oo=class r{constructor(){this.bb=null,this.bb_pos=0}__init(t,n){return this.bb_pos=t,this.bb=n,this}static getRootAsShape(t,n){return(n||new r).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsShape(t,n){return t.setPosition(t.position()+Up.SIZE_PREFIX_LENGTH),(n||new r).__init(t.readInt32(t.position())+t.position(),t)}dim(t,n){let e=this.bb.__offset(this.bb_pos,4);return e?(n||new jp.Dimension).__init(this.bb.__indirect(this.bb.__vector(this.bb_pos+e)+t*4),this.bb):null}dimLength(){let t=this.bb.__offset(this.bb_pos,4);return t?this.bb.__vector_len(this.bb_pos+t):0}static startShape(t){t.startObject(1)}static addDim(t,n){t.addFieldOffset(0,n,0)}static createDimVector(t,n){t.startVector(4,n.length,4);for(let e=n.length-1;e>=0;e--)t.addOffset(n[e]);return t.endVector()}static startDimVector(t,n){t.startVector(4,n,4)}static endShape(t){return t.endObject()}static createShape(t,n){return r.startShape(t),r.addDim(t,n),r.endShape(t)}};jt.Shape=Oo});var Ao=P(Wt=>{"use strict";var Wp=Wt&&Wt.__createBinding||(Object.create?function(r,t,n,e){e===void 0&&(e=n);var o=Object.getOwnPropertyDescriptor(t,n);(!o||("get"in o?!t.__esModule:o.writable||o.configurable))&&(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(r,e,o)}:function(r,t,n,e){e===void 0&&(e=n),r[e]=t[n]}),Hp=Wt&&Wt.__setModuleDefault||(Object.create?function(r,t){Object.defineProperty(r,"default",{enumerable:!0,value:t})}:function(r,t){r.default=t}),qp=Wt&&Wt.__importStar||function(r){if(r&&r.__esModule)return r;var t={};if(r!=null)for(var n in r)n!=="default"&&Object.prototype.hasOwnProperty.call(r,n)&&Wp(t,r,n);return Hp(t,r),t};Object.defineProperty(Wt,"__esModule",{value:!0});Wt.TensorTypeAndShape=void 0;var Kp=qp(M()),Xp=Po(),ds=tn(),So=class r{constructor(){this.bb=null,this.bb_pos=0}__init(t,n){return this.bb_pos=t,this.bb=n,this}static getRootAsTensorTypeAndShape(t,n){return(n||new r).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsTensorTypeAndShape(t,n){return t.setPosition(t.position()+Kp.SIZE_PREFIX_LENGTH),(n||new r).__init(t.readInt32(t.position())+t.position(),t)}elemType(){let t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt32(this.bb_pos+t):ds.TensorDataType.UNDEFINED}shape(t){let n=this.bb.__offset(this.bb_pos,6);return n?(t||new Xp.Shape).__init(this.bb.__indirect(this.bb_pos+n),this.bb):null}static startTensorTypeAndShape(t){t.startObject(2)}static addElemType(t,n){t.addFieldInt32(0,n,ds.TensorDataType.UNDEFINED)}static addShape(t,n){t.addFieldOffset(1,n,0)}static endTensorTypeAndShape(t){return t.endObject()}};Wt.TensorTypeAndShape=So});var Eo=P(ye=>{"use strict";Object.defineProperty(ye,"__esModule",{value:!0});ye.unionListToTypeInfoValue=ye.unionToTypeInfoValue=ye.TypeInfoValue=void 0;var ps=go(),hs=To(),ms=Ao(),Un;(function(r){r[r.NONE=0]="NONE",r[r.tensor_type=1]="tensor_type",r[r.sequence_type=2]="sequence_type",r[r.map_type=3]="map_type"})(Un||(ye.TypeInfoValue=Un={}));function Zp(r,t){switch(Un[r]){case"NONE":return null;case"tensor_type":return t(new ms.TensorTypeAndShape);case"sequence_type":return t(new hs.SequenceType);case"map_type":return t(new ps.MapType);default:return null}}ye.unionToTypeInfoValue=Zp;function Jp(r,t,n){switch(Un[r]){case"NONE":return null;case"tensor_type":return t(n,new ms.TensorTypeAndShape);case"sequence_type":return t(n,new hs.SequenceType);case"map_type":return t(n,new ps.MapType);default:return null}}ye.unionListToTypeInfoValue=Jp});var nn=P(Ht=>{"use strict";var Yp=Ht&&Ht.__createBinding||(Object.create?function(r,t,n,e){e===void 0&&(e=n);var o=Object.getOwnPropertyDescriptor(t,n);(!o||("get"in o?!t.__esModule:o.writable||o.configurable))&&(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(r,e,o)}:function(r,t,n,e){e===void 0&&(e=n),r[e]=t[n]}),Qp=Ht&&Ht.__setModuleDefault||(Object.create?function(r,t){Object.defineProperty(r,"default",{enumerable:!0,value:t})}:function(r,t){r.default=t}),th=Ht&&Ht.__importStar||function(r){if(r&&r.__esModule)return r;var t={};if(r!=null)for(var n in r)n!=="default"&&Object.prototype.hasOwnProperty.call(r,n)&&Yp(t,r,n);return Qp(t,r),t};Object.defineProperty(Ht,"__esModule",{value:!0});Ht.TypeInfo=void 0;var eh=th(M()),bs=Eo(),Do=class r{constructor(){this.bb=null,this.bb_pos=0}__init(t,n){return this.bb_pos=t,this.bb=n,this}static getRootAsTypeInfo(t,n){return(n||new r).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsTypeInfo(t,n){return t.setPosition(t.position()+eh.SIZE_PREFIX_LENGTH),(n||new r).__init(t.readInt32(t.position())+t.position(),t)}denotation(t){let n=this.bb.__offset(this.bb_pos,4);return n?this.bb.__string(this.bb_pos+n,t):null}valueType(){let t=this.bb.__offset(this.bb_pos,6);return t?this.bb.readUint8(this.bb_pos+t):bs.TypeInfoValue.NONE}value(t){let n=this.bb.__offset(this.bb_pos,8);return n?this.bb.__union(t,this.bb_pos+n):null}static startTypeInfo(t){t.startObject(3)}static addDenotation(t,n){t.addFieldOffset(0,n,0)}static addValueType(t,n){t.addFieldInt8(1,n,bs.TypeInfoValue.NONE)}static addValue(t,n){t.addFieldOffset(2,n,0)}static endTypeInfo(t){return t.endObject()}static createTypeInfo(t,n,e,o){return r.startTypeInfo(t),r.addDenotation(t,n),r.addValueType(t,e),r.addValue(t,o),r.endTypeInfo(t)}};Ht.TypeInfo=Do});var $o=P(qt=>{"use strict";var nh=qt&&qt.__createBinding||(Object.create?function(r,t,n,e){e===void 0&&(e=n);var o=Object.getOwnPropertyDescriptor(t,n);(!o||("get"in o?!t.__esModule:o.writable||o.configurable))&&(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(r,e,o)}:function(r,t,n,e){e===void 0&&(e=n),r[e]=t[n]}),rh=qt&&qt.__setModuleDefault||(Object.create?function(r,t){Object.defineProperty(r,"default",{enumerable:!0,value:t})}:function(r,t){r.default=t}),oh=qt&&qt.__importStar||function(r){if(r&&r.__esModule)return r;var t={};if(r!=null)for(var n in r)n!=="default"&&Object.prototype.hasOwnProperty.call(r,n)&&nh(t,r,n);return rh(t,r),t};Object.defineProperty(qt,"__esModule",{value:!0});qt.ValueInfo=void 0;var ih=oh(M()),ah=nn(),Lo=class r{constructor(){this.bb=null,this.bb_pos=0}__init(t,n){return this.bb_pos=t,this.bb=n,this}static getRootAsValueInfo(t,n){return(n||new r).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsValueInfo(t,n){return t.setPosition(t.position()+ih.SIZE_PREFIX_LENGTH),(n||new r).__init(t.readInt32(t.position())+t.position(),t)}name(t){let n=this.bb.__offset(this.bb_pos,4);return n?this.bb.__string(this.bb_pos+n,t):null}docString(t){let n=this.bb.__offset(this.bb_pos,6);return n?this.bb.__string(this.bb_pos+n,t):null}type(t){let n=this.bb.__offset(this.bb_pos,8);return n?(t||new ah.TypeInfo).__init(this.bb.__indirect(this.bb_pos+n),this.bb):null}static startValueInfo(t){t.startObject(3)}static addName(t,n){t.addFieldOffset(0,n,0)}static addDocString(t,n){t.addFieldOffset(1,n,0)}static addType(t,n){t.addFieldOffset(2,n,0)}static endValueInfo(t){return t.endObject()}};qt.ValueInfo=Lo});var jn=P(Kt=>{"use strict";var sh=Kt&&Kt.__createBinding||(Object.create?function(r,t,n,e){e===void 0&&(e=n);var o=Object.getOwnPropertyDescriptor(t,n);(!o||("get"in o?!t.__esModule:o.writable||o.configurable))&&(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(r,e,o)}:function(r,t,n,e){e===void 0&&(e=n),r[e]=t[n]}),uh=Kt&&Kt.__setModuleDefault||(Object.create?function(r,t){Object.defineProperty(r,"default",{enumerable:!0,value:t})}:function(r,t){r.default=t}),lh=Kt&&Kt.__importStar||function(r){if(r&&r.__esModule)return r;var t={};if(r!=null)for(var n in r)n!=="default"&&Object.prototype.hasOwnProperty.call(r,n)&&sh(t,r,n);return uh(t,r),t};Object.defineProperty(Kt,"__esModule",{value:!0});Kt.Graph=void 0;var ch=lh(M()),fh=Yr(),dh=ro(),ph=fo(),hh=mo(),mh=en(),bh=$o(),No=class r{constructor(){this.bb=null,this.bb_pos=0}__init(t,n){return this.bb_pos=t,this.bb=n,this}static getRootAsGraph(t,n){return(n||new r).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsGraph(t,n){return t.setPosition(t.position()+ch.SIZE_PREFIX_LENGTH),(n||new r).__init(t.readInt32(t.position())+t.position(),t)}initializers(t,n){let e=this.bb.__offset(this.bb_pos,4);return e?(n||new mh.Tensor).__init(this.bb.__indirect(this.bb.__vector(this.bb_pos+e)+t*4),this.bb):null}initializersLength(){let t=this.bb.__offset(this.bb_pos,4);return t?this.bb.__vector_len(this.bb_pos+t):0}nodeArgs(t,n){let e=this.bb.__offset(this.bb_pos,6);return e?(n||new bh.ValueInfo).__init(this.bb.__indirect(this.bb.__vector(this.bb_pos+e)+t*4),this.bb):null}nodeArgsLength(){let t=this.bb.__offset(this.bb_pos,6);return t?this.bb.__vector_len(this.bb_pos+t):0}nodes(t,n){let e=this.bb.__offset(this.bb_pos,8);return e?(n||new fh.Node).__init(this.bb.__indirect(this.bb.__vector(this.bb_pos+e)+t*4),this.bb):null}nodesLength(){let t=this.bb.__offset(this.bb_pos,8);return t?this.bb.__vector_len(this.bb_pos+t):0}maxNodeIndex(){let t=this.bb.__offset(this.bb_pos,10);return t?this.bb.readUint32(this.bb_pos+t):0}nodeEdges(t,n){let e=this.bb.__offset(this.bb_pos,12);return e?(n||new dh.NodeEdge).__init(this.bb.__indirect(this.bb.__vector(this.bb_pos+e)+t*4),this.bb):null}nodeEdgesLength(){let t=this.bb.__offset(this.bb_pos,12);return t?this.bb.__vector_len(this.bb_pos+t):0}inputs(t,n){let e=this.bb.__offset(this.bb_pos,14);return e?this.bb.__string(this.bb.__vector(this.bb_pos+e)+t*4,n):null}inputsLength(){let t=this.bb.__offset(this.bb_pos,14);return t?this.bb.__vector_len(this.bb_pos+t):0}outputs(t,n){let e=this.bb.__offset(this.bb_pos,16);return e?this.bb.__string(this.bb.__vector(this.bb_pos+e)+t*4,n):null}outputsLength(){let t=this.bb.__offset(this.bb_pos,16);return t?this.bb.__vector_len(this.bb_pos+t):0}sparseInitializers(t,n){let e=this.bb.__offset(this.bb_pos,18);return e?(n||new hh.SparseTensor).__init(this.bb.__indirect(this.bb.__vector(this.bb_pos+e)+t*4),this.bb):null}sparseInitializersLength(){let t=this.bb.__offset(this.bb_pos,18);return t?this.bb.__vector_len(this.bb_pos+t):0}runtimeOptimizations(t){let n=this.bb.__offset(this.bb_pos,20);return n?(t||new ph.RuntimeOptimizations).__init(this.bb.__indirect(this.bb_pos+n),this.bb):null}static startGraph(t){t.startObject(9)}static addInitializers(t,n){t.addFieldOffset(0,n,0)}static createInitializersVector(t,n){t.startVector(4,n.length,4);for(let e=n.length-1;e>=0;e--)t.addOffset(n[e]);return t.endVector()}static startInitializersVector(t,n){t.startVector(4,n,4)}static addNodeArgs(t,n){t.addFieldOffset(1,n,0)}static createNodeArgsVector(t,n){t.startVector(4,n.length,4);for(let e=n.length-1;e>=0;e--)t.addOffset(n[e]);return t.endVector()}static startNodeArgsVector(t,n){t.startVector(4,n,4)}static addNodes(t,n){t.addFieldOffset(2,n,0)}static createNodesVector(t,n){t.startVector(4,n.length,4);for(let e=n.length-1;e>=0;e--)t.addOffset(n[e]);return t.endVector()}static startNodesVector(t,n){t.startVector(4,n,4)}static addMaxNodeIndex(t,n){t.addFieldInt32(3,n,0)}static addNodeEdges(t,n){t.addFieldOffset(4,n,0)}static createNodeEdgesVector(t,n){t.startVector(4,n.length,4);for(let e=n.length-1;e>=0;e--)t.addOffset(n[e]);return t.endVector()}static startNodeEdgesVector(t,n){t.startVector(4,n,4)}static addInputs(t,n){t.addFieldOffset(5,n,0)}static createInputsVector(t,n){t.startVector(4,n.length,4);for(let e=n.length-1;e>=0;e--)t.addOffset(n[e]);return t.endVector()}static startInputsVector(t,n){t.startVector(4,n,4)}static addOutputs(t,n){t.addFieldOffset(6,n,0)}static createOutputsVector(t,n){t.startVector(4,n.length,4);for(let e=n.length-1;e>=0;e--)t.addOffset(n[e]);return t.endVector()}static startOutputsVector(t,n){t.startVector(4,n,4)}static addSparseInitializers(t,n){t.addFieldOffset(7,n,0)}static createSparseInitializersVector(t,n){t.startVector(4,n.length,4);for(let e=n.length-1;e>=0;e--)t.addOffset(n[e]);return t.endVector()}static startSparseInitializersVector(t,n){t.startVector(4,n,4)}static addRuntimeOptimizations(t,n){t.addFieldOffset(8,n,0)}static endGraph(t){return t.endObject()}};Kt.Graph=No});var Qr=P(Xt=>{"use strict";var gh=Xt&&Xt.__createBinding||(Object.create?function(r,t,n,e){e===void 0&&(e=n);var o=Object.getOwnPropertyDescriptor(t,n);(!o||("get"in o?!t.__esModule:o.writable||o.configurable))&&(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(r,e,o)}:function(r,t,n,e){e===void 0&&(e=n),r[e]=t[n]}),yh=Xt&&Xt.__setModuleDefault||(Object.create?function(r,t){Object.defineProperty(r,"default",{enumerable:!0,value:t})}:function(r,t){r.default=t}),Th=Xt&&Xt.__importStar||function(r){if(r&&r.__esModule)return r;var t={};if(r!=null)for(var n in r)n!=="default"&&Object.prototype.hasOwnProperty.call(r,n)&&gh(t,r,n);return yh(t,r),t};Object.defineProperty(Xt,"__esModule",{value:!0});Xt.Attribute=void 0;var _h=Th(M()),gs=Xr(),ys=jn(),Ts=en(),Fo=class r{constructor(){this.bb=null,this.bb_pos=0}__init(t,n){return this.bb_pos=t,this.bb=n,this}static getRootAsAttribute(t,n){return(n||new r).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsAttribute(t,n){return t.setPosition(t.position()+_h.SIZE_PREFIX_LENGTH),(n||new r).__init(t.readInt32(t.position())+t.position(),t)}name(t){let n=this.bb.__offset(this.bb_pos,4);return n?this.bb.__string(this.bb_pos+n,t):null}docString(t){let n=this.bb.__offset(this.bb_pos,6);return n?this.bb.__string(this.bb_pos+n,t):null}type(){let t=this.bb.__offset(this.bb_pos,8);return t?this.bb.readInt32(this.bb_pos+t):gs.AttributeType.UNDEFINED}f(){let t=this.bb.__offset(this.bb_pos,10);return t?this.bb.readFloat32(this.bb_pos+t):0}i(){let t=this.bb.__offset(this.bb_pos,12);return t?this.bb.readInt64(this.bb_pos+t):BigInt("0")}s(t){let n=this.bb.__offset(this.bb_pos,14);return n?this.bb.__string(this.bb_pos+n,t):null}t(t){let n=this.bb.__offset(this.bb_pos,16);return n?(t||new Ts.Tensor).__init(this.bb.__indirect(this.bb_pos+n),this.bb):null}g(t){let n=this.bb.__offset(this.bb_pos,18);return n?(t||new ys.Graph).__init(this.bb.__indirect(this.bb_pos+n),this.bb):null}floats(t){let n=this.bb.__offset(this.bb_pos,20);return n?this.bb.readFloat32(this.bb.__vector(this.bb_pos+n)+t*4):0}floatsLength(){let t=this.bb.__offset(this.bb_pos,20);return t?this.bb.__vector_len(this.bb_pos+t):0}floatsArray(){let t=this.bb.__offset(this.bb_pos,20);return t?new Float32Array(this.bb.bytes().buffer,this.bb.bytes().byteOffset+this.bb.__vector(this.bb_pos+t),this.bb.__vector_len(this.bb_pos+t)):null}ints(t){let n=this.bb.__offset(this.bb_pos,22);return n?this.bb.readInt64(this.bb.__vector(this.bb_pos+n)+t*8):BigInt(0)}intsLength(){let t=this.bb.__offset(this.bb_pos,22);return t?this.bb.__vector_len(this.bb_pos+t):0}strings(t,n){let e=this.bb.__offset(this.bb_pos,24);return e?this.bb.__string(this.bb.__vector(this.bb_pos+e)+t*4,n):null}stringsLength(){let t=this.bb.__offset(this.bb_pos,24);return t?this.bb.__vector_len(this.bb_pos+t):0}tensors(t,n){let e=this.bb.__offset(this.bb_pos,26);return e?(n||new Ts.Tensor).__init(this.bb.__indirect(this.bb.__vector(this.bb_pos+e)+t*4),this.bb):null}tensorsLength(){let t=this.bb.__offset(this.bb_pos,26);return t?this.bb.__vector_len(this.bb_pos+t):0}graphs(t,n){let e=this.bb.__offset(this.bb_pos,28);return e?(n||new ys.Graph).__init(this.bb.__indirect(this.bb.__vector(this.bb_pos+e)+t*4),this.bb):null}graphsLength(){let t=this.bb.__offset(this.bb_pos,28);return t?this.bb.__vector_len(this.bb_pos+t):0}static startAttribute(t){t.startObject(13)}static addName(t,n){t.addFieldOffset(0,n,0)}static addDocString(t,n){t.addFieldOffset(1,n,0)}static addType(t,n){t.addFieldInt32(2,n,gs.AttributeType.UNDEFINED)}static addF(t,n){t.addFieldFloat32(3,n,0)}static addI(t,n){t.addFieldInt64(4,n,BigInt("0"))}static addS(t,n){t.addFieldOffset(5,n,0)}static addT(t,n){t.addFieldOffset(6,n,0)}static addG(t,n){t.addFieldOffset(7,n,0)}static addFloats(t,n){t.addFieldOffset(8,n,0)}static createFloatsVector(t,n){t.startVector(4,n.length,4);for(let e=n.length-1;e>=0;e--)t.addFloat32(n[e]);return t.endVector()}static startFloatsVector(t,n){t.startVector(4,n,4)}static addInts(t,n){t.addFieldOffset(9,n,0)}static createIntsVector(t,n){t.startVector(8,n.length,8);for(let e=n.length-1;e>=0;e--)t.addInt64(n[e]);return t.endVector()}static startIntsVector(t,n){t.startVector(8,n,8)}static addStrings(t,n){t.addFieldOffset(10,n,0)}static createStringsVector(t,n){t.startVector(4,n.length,4);for(let e=n.length-1;e>=0;e--)t.addOffset(n[e]);return t.endVector()}static startStringsVector(t,n){t.startVector(4,n,4)}static addTensors(t,n){t.addFieldOffset(11,n,0)}static createTensorsVector(t,n){t.startVector(4,n.length,4);for(let e=n.length-1;e>=0;e--)t.addOffset(n[e]);return t.endVector()}static startTensorsVector(t,n){t.startVector(4,n,4)}static addGraphs(t,n){t.addFieldOffset(12,n,0)}static createGraphsVector(t,n){t.startVector(4,n.length,4);for(let e=n.length-1;e>=0;e--)t.addOffset(n[e]);return t.endVector()}static startGraphsVector(t,n){t.startVector(4,n,4)}static endAttribute(t){return t.endObject()}};Xt.Attribute=Fo});var Ro=P(Zt=>{"use strict";var xh=Zt&&Zt.__createBinding||(Object.create?function(r,t,n,e){e===void 0&&(e=n);var o=Object.getOwnPropertyDescriptor(t,n);(!o||("get"in o?!t.__esModule:o.writable||o.configurable))&&(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(r,e,o)}:function(r,t,n,e){e===void 0&&(e=n),r[e]=t[n]}),vh=Zt&&Zt.__setModuleDefault||(Object.create?function(r,t){Object.defineProperty(r,"default",{enumerable:!0,value:t})}:function(r,t){r.default=t}),wh=Zt&&Zt.__importStar||function(r){if(r&&r.__esModule)return r;var t={};if(r!=null)for(var n in r)n!=="default"&&Object.prototype.hasOwnProperty.call(r,n)&&xh(t,r,n);return vh(t,r),t};Object.defineProperty(Zt,"__esModule",{value:!0});Zt.DeprecatedKernelCreateInfos=void 0;var Ih=wh(M()),Co=class r{constructor(){this.bb=null,this.bb_pos=0}__init(t,n){return this.bb_pos=t,this.bb=n,this}static getRootAsDeprecatedKernelCreateInfos(t,n){return(n||new r).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsDeprecatedKernelCreateInfos(t,n){return t.setPosition(t.position()+Ih.SIZE_PREFIX_LENGTH),(n||new r).__init(t.readInt32(t.position())+t.position(),t)}nodeIndices(t){let n=this.bb.__offset(this.bb_pos,4);return n?this.bb.readUint32(this.bb.__vector(this.bb_pos+n)+t*4):0}nodeIndicesLength(){let t=this.bb.__offset(this.bb_pos,4);return t?this.bb.__vector_len(this.bb_pos+t):0}nodeIndicesArray(){let t=this.bb.__offset(this.bb_pos,4);return t?new Uint32Array(this.bb.bytes().buffer,this.bb.bytes().byteOffset+this.bb.__vector(this.bb_pos+t),this.bb.__vector_len(this.bb_pos+t)):null}kernelDefHashes(t){let n=this.bb.__offset(this.bb_pos,6);return n?this.bb.readUint64(this.bb.__vector(this.bb_pos+n)+t*8):BigInt(0)}kernelDefHashesLength(){let t=this.bb.__offset(this.bb_pos,6);return t?this.bb.__vector_len(this.bb_pos+t):0}static startDeprecatedKernelCreateInfos(t){t.startObject(2)}static addNodeIndices(t,n){t.addFieldOffset(0,n,0)}static createNodeIndicesVector(t,n){t.startVector(4,n.length,4);for(let e=n.length-1;e>=0;e--)t.addInt32(n[e]);return t.endVector()}static startNodeIndicesVector(t,n){t.startVector(4,n,4)}static addKernelDefHashes(t,n){t.addFieldOffset(1,n,0)}static createKernelDefHashesVector(t,n){t.startVector(8,n.length,8);for(let e=n.length-1;e>=0;e--)t.addInt64(n[e]);return t.endVector()}static startKernelDefHashesVector(t,n){t.startVector(8,n,8)}static endDeprecatedKernelCreateInfos(t){return t.endObject()}static createDeprecatedKernelCreateInfos(t,n,e){return r.startDeprecatedKernelCreateInfos(t),r.addNodeIndices(t,n),r.addKernelDefHashes(t,e),r.endDeprecatedKernelCreateInfos(t)}};Zt.DeprecatedKernelCreateInfos=Co});var _s=P(Jt=>{"use strict";var Oh=Jt&&Jt.__createBinding||(Object.create?function(r,t,n,e){e===void 0&&(e=n);var o=Object.getOwnPropertyDescriptor(t,n);(!o||("get"in o?!t.__esModule:o.writable||o.configurable))&&(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(r,e,o)}:function(r,t,n,e){e===void 0&&(e=n),r[e]=t[n]}),Ph=Jt&&Jt.__setModuleDefault||(Object.create?function(r,t){Object.defineProperty(r,"default",{enumerable:!0,value:t})}:function(r,t){r.default=t}),Sh=Jt&&Jt.__importStar||function(r){if(r&&r.__esModule)return r;var t={};if(r!=null)for(var n in r)n!=="default"&&Object.prototype.hasOwnProperty.call(r,n)&&Oh(t,r,n);return Ph(t,r),t};Object.defineProperty(Jt,"__esModule",{value:!0});Jt.DeprecatedNodeIndexAndKernelDefHash=void 0;var Ah=Sh(M()),Go=class r{constructor(){this.bb=null,this.bb_pos=0}__init(t,n){return this.bb_pos=t,this.bb=n,this}static getRootAsDeprecatedNodeIndexAndKernelDefHash(t,n){return(n||new r).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsDeprecatedNodeIndexAndKernelDefHash(t,n){return t.setPosition(t.position()+Ah.SIZE_PREFIX_LENGTH),(n||new r).__init(t.readInt32(t.position())+t.position(),t)}nodeIndex(){let t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readUint32(this.bb_pos+t):0}kernelDefHash(){let t=this.bb.__offset(this.bb_pos,6);return t?this.bb.readUint64(this.bb_pos+t):BigInt("0")}static startDeprecatedNodeIndexAndKernelDefHash(t){t.startObject(2)}static addNodeIndex(t,n){t.addFieldInt32(0,n,0)}static addKernelDefHash(t,n){t.addFieldInt64(1,n,BigInt("0"))}static endDeprecatedNodeIndexAndKernelDefHash(t){return t.endObject()}static createDeprecatedNodeIndexAndKernelDefHash(t,n,e){return r.startDeprecatedNodeIndexAndKernelDefHash(t),r.addNodeIndex(t,n),r.addKernelDefHash(t,e),r.endDeprecatedNodeIndexAndKernelDefHash(t)}};Jt.DeprecatedNodeIndexAndKernelDefHash=Go});var Mo=P(Yt=>{"use strict";var Eh=Yt&&Yt.__createBinding||(Object.create?function(r,t,n,e){e===void 0&&(e=n);var o=Object.getOwnPropertyDescriptor(t,n);(!o||("get"in o?!t.__esModule:o.writable||o.configurable))&&(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(r,e,o)}:function(r,t,n,e){e===void 0&&(e=n),r[e]=t[n]}),Dh=Yt&&Yt.__setModuleDefault||(Object.create?function(r,t){Object.defineProperty(r,"default",{enumerable:!0,value:t})}:function(r,t){r.default=t}),Lh=Yt&&Yt.__importStar||function(r){if(r&&r.__esModule)return r;var t={};if(r!=null)for(var n in r)n!=="default"&&Object.prototype.hasOwnProperty.call(r,n)&&Eh(t,r,n);return Dh(t,r),t};Object.defineProperty(Yt,"__esModule",{value:!0});Yt.DeprecatedSubGraphSessionState=void 0;var $h=Lh(M()),Nh=Vo(),ko=class r{constructor(){this.bb=null,this.bb_pos=0}__init(t,n){return this.bb_pos=t,this.bb=n,this}static getRootAsDeprecatedSubGraphSessionState(t,n){return(n||new r).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsDeprecatedSubGraphSessionState(t,n){return t.setPosition(t.position()+$h.SIZE_PREFIX_LENGTH),(n||new r).__init(t.readInt32(t.position())+t.position(),t)}graphId(t){let n=this.bb.__offset(this.bb_pos,4);return n?this.bb.__string(this.bb_pos+n,t):null}sessionState(t){let n=this.bb.__offset(this.bb_pos,6);return n?(t||new Nh.DeprecatedSessionState).__init(this.bb.__indirect(this.bb_pos+n),this.bb):null}static startDeprecatedSubGraphSessionState(t){t.startObject(2)}static addGraphId(t,n){t.addFieldOffset(0,n,0)}static addSessionState(t,n){t.addFieldOffset(1,n,0)}static endDeprecatedSubGraphSessionState(t){let n=t.endObject();return t.requiredField(n,4),n}};Yt.DeprecatedSubGraphSessionState=ko});var Vo=P(Qt=>{"use strict";var Fh=Qt&&Qt.__createBinding||(Object.create?function(r,t,n,e){e===void 0&&(e=n);var o=Object.getOwnPropertyDescriptor(t,n);(!o||("get"in o?!t.__esModule:o.writable||o.configurable))&&(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(r,e,o)}:function(r,t,n,e){e===void 0&&(e=n),r[e]=t[n]}),Ch=Qt&&Qt.__setModuleDefault||(Object.create?function(r,t){Object.defineProperty(r,"default",{enumerable:!0,value:t})}:function(r,t){r.default=t}),Rh=Qt&&Qt.__importStar||function(r){if(r&&r.__esModule)return r;var t={};if(r!=null)for(var n in r)n!=="default"&&Object.prototype.hasOwnProperty.call(r,n)&&Fh(t,r,n);return Ch(t,r),t};Object.defineProperty(Qt,"__esModule",{value:!0});Qt.DeprecatedSessionState=void 0;var Gh=Rh(M()),kh=Ro(),Mh=Mo(),Bo=class r{constructor(){this.bb=null,this.bb_pos=0}__init(t,n){return this.bb_pos=t,this.bb=n,this}static getRootAsDeprecatedSessionState(t,n){return(n||new r).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsDeprecatedSessionState(t,n){return t.setPosition(t.position()+Gh.SIZE_PREFIX_LENGTH),(n||new r).__init(t.readInt32(t.position())+t.position(),t)}kernels(t){let n=this.bb.__offset(this.bb_pos,4);return n?(t||new kh.DeprecatedKernelCreateInfos).__init(this.bb.__indirect(this.bb_pos+n),this.bb):null}subGraphSessionStates(t,n){let e=this.bb.__offset(this.bb_pos,6);return e?(n||new Mh.DeprecatedSubGraphSessionState).__init(this.bb.__indirect(this.bb.__vector(this.bb_pos+e)+t*4),this.bb):null}subGraphSessionStatesLength(){let t=this.bb.__offset(this.bb_pos,6);return t?this.bb.__vector_len(this.bb_pos+t):0}static startDeprecatedSessionState(t){t.startObject(2)}static addKernels(t,n){t.addFieldOffset(0,n,0)}static addSubGraphSessionStates(t,n){t.addFieldOffset(1,n,0)}static createSubGraphSessionStatesVector(t,n){t.startVector(4,n.length,4);for(let e=n.length-1;e>=0;e--)t.addOffset(n[e]);return t.endVector()}static startSubGraphSessionStatesVector(t,n){t.startVector(4,n,4)}static endDeprecatedSessionState(t){return t.endObject()}static createDeprecatedSessionState(t,n,e){return r.startDeprecatedSessionState(t),r.addKernels(t,n),r.addSubGraphSessionStates(t,e),r.endDeprecatedSessionState(t)}};Qt.DeprecatedSessionState=Bo});var Uo=P(te=>{"use strict";var Vh=te&&te.__createBinding||(Object.create?function(r,t,n,e){e===void 0&&(e=n);var o=Object.getOwnPropertyDescriptor(t,n);(!o||("get"in o?!t.__esModule:o.writable||o.configurable))&&(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(r,e,o)}:function(r,t,n,e){e===void 0&&(e=n),r[e]=t[n]}),Bh=te&&te.__setModuleDefault||(Object.create?function(r,t){Object.defineProperty(r,"default",{enumerable:!0,value:t})}:function(r,t){r.default=t}),zh=te&&te.__importStar||function(r){if(r&&r.__esModule)return r;var t={};if(r!=null)for(var n in r)n!=="default"&&Object.prototype.hasOwnProperty.call(r,n)&&Vh(t,r,n);return Bh(t,r),t};Object.defineProperty(te,"__esModule",{value:!0});te.KernelTypeStrArgsEntry=void 0;var Uh=zh(M()),jh=Kr(),zo=class r{constructor(){this.bb=null,this.bb_pos=0}__init(t,n){return this.bb_pos=t,this.bb=n,this}static getRootAsKernelTypeStrArgsEntry(t,n){return(n||new r).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsKernelTypeStrArgsEntry(t,n){return t.setPosition(t.position()+Uh.SIZE_PREFIX_LENGTH),(n||new r).__init(t.readInt32(t.position())+t.position(),t)}kernelTypeStr(t){let n=this.bb.__offset(this.bb_pos,4);return n?this.bb.__string(this.bb_pos+n,t):null}args(t,n){let e=this.bb.__offset(this.bb_pos,6);return e?(n||new jh.ArgTypeAndIndex).__init(this.bb.__indirect(this.bb.__vector(this.bb_pos+e)+t*4),this.bb):null}argsLength(){let t=this.bb.__offset(this.bb_pos,6);return t?this.bb.__vector_len(this.bb_pos+t):0}static startKernelTypeStrArgsEntry(t){t.startObject(2)}static addKernelTypeStr(t,n){t.addFieldOffset(0,n,0)}static addArgs(t,n){t.addFieldOffset(1,n,0)}static createArgsVector(t,n){t.startVector(4,n.length,4);for(let e=n.length-1;e>=0;e--)t.addOffset(n[e]);return t.endVector()}static startArgsVector(t,n){t.startVector(4,n,4)}static endKernelTypeStrArgsEntry(t){let n=t.endObject();return t.requiredField(n,4),n}static createKernelTypeStrArgsEntry(t,n,e){return r.startKernelTypeStrArgsEntry(t),r.addKernelTypeStr(t,n),r.addArgs(t,e),r.endKernelTypeStrArgsEntry(t)}};te.KernelTypeStrArgsEntry=zo});var Wo=P(ee=>{"use strict";var Wh=ee&&ee.__createBinding||(Object.create?function(r,t,n,e){e===void 0&&(e=n);var o=Object.getOwnPropertyDescriptor(t,n);(!o||("get"in o?!t.__esModule:o.writable||o.configurable))&&(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(r,e,o)}:function(r,t,n,e){e===void 0&&(e=n),r[e]=t[n]}),Hh=ee&&ee.__setModuleDefault||(Object.create?function(r,t){Object.defineProperty(r,"default",{enumerable:!0,value:t})}:function(r,t){r.default=t}),qh=ee&&ee.__importStar||function(r){if(r&&r.__esModule)return r;var t={};if(r!=null)for(var n in r)n!=="default"&&Object.prototype.hasOwnProperty.call(r,n)&&Wh(t,r,n);return Hh(t,r),t};Object.defineProperty(ee,"__esModule",{value:!0});ee.OpIdKernelTypeStrArgsEntry=void 0;var Kh=qh(M()),Xh=Uo(),jo=class r{constructor(){this.bb=null,this.bb_pos=0}__init(t,n){return this.bb_pos=t,this.bb=n,this}static getRootAsOpIdKernelTypeStrArgsEntry(t,n){return(n||new r).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsOpIdKernelTypeStrArgsEntry(t,n){return t.setPosition(t.position()+Kh.SIZE_PREFIX_LENGTH),(n||new r).__init(t.readInt32(t.position())+t.position(),t)}opId(t){let n=this.bb.__offset(this.bb_pos,4);return n?this.bb.__string(this.bb_pos+n,t):null}kernelTypeStrArgs(t,n){let e=this.bb.__offset(this.bb_pos,6);return e?(n||new Xh.KernelTypeStrArgsEntry).__init(this.bb.__indirect(this.bb.__vector(this.bb_pos+e)+t*4),this.bb):null}kernelTypeStrArgsLength(){let t=this.bb.__offset(this.bb_pos,6);return t?this.bb.__vector_len(this.bb_pos+t):0}static startOpIdKernelTypeStrArgsEntry(t){t.startObject(2)}static addOpId(t,n){t.addFieldOffset(0,n,0)}static addKernelTypeStrArgs(t,n){t.addFieldOffset(1,n,0)}static createKernelTypeStrArgsVector(t,n){t.startVector(4,n.length,4);for(let e=n.length-1;e>=0;e--)t.addOffset(n[e]);return t.endVector()}static startKernelTypeStrArgsVector(t,n){t.startVector(4,n,4)}static endOpIdKernelTypeStrArgsEntry(t){let n=t.endObject();return t.requiredField(n,4),n}static createOpIdKernelTypeStrArgsEntry(t,n,e){return r.startOpIdKernelTypeStrArgsEntry(t),r.addOpId(t,n),r.addKernelTypeStrArgs(t,e),r.endOpIdKernelTypeStrArgsEntry(t)}};ee.OpIdKernelTypeStrArgsEntry=jo});var qo=P(ne=>{"use strict";var Zh=ne&&ne.__createBinding||(Object.create?function(r,t,n,e){e===void 0&&(e=n);var o=Object.getOwnPropertyDescriptor(t,n);(!o||("get"in o?!t.__esModule:o.writable||o.configurable))&&(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(r,e,o)}:function(r,t,n,e){e===void 0&&(e=n),r[e]=t[n]}),Jh=ne&&ne.__setModuleDefault||(Object.create?function(r,t){Object.defineProperty(r,"default",{enumerable:!0,value:t})}:function(r,t){r.default=t}),Yh=ne&&ne.__importStar||function(r){if(r&&r.__esModule)return r;var t={};if(r!=null)for(var n in r)n!=="default"&&Object.prototype.hasOwnProperty.call(r,n)&&Zh(t,r,n);return Jh(t,r),t};Object.defineProperty(ne,"__esModule",{value:!0});ne.KernelTypeStrResolver=void 0;var Qh=Yh(M()),tm=Wo(),Ho=class r{constructor(){this.bb=null,this.bb_pos=0}__init(t,n){return this.bb_pos=t,this.bb=n,this}static getRootAsKernelTypeStrResolver(t,n){return(n||new r).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsKernelTypeStrResolver(t,n){return t.setPosition(t.position()+Qh.SIZE_PREFIX_LENGTH),(n||new r).__init(t.readInt32(t.position())+t.position(),t)}opKernelTypeStrArgs(t,n){let e=this.bb.__offset(this.bb_pos,4);return e?(n||new tm.OpIdKernelTypeStrArgsEntry).__init(this.bb.__indirect(this.bb.__vector(this.bb_pos+e)+t*4),this.bb):null}opKernelTypeStrArgsLength(){let t=this.bb.__offset(this.bb_pos,4);return t?this.bb.__vector_len(this.bb_pos+t):0}static startKernelTypeStrResolver(t){t.startObject(1)}static addOpKernelTypeStrArgs(t,n){t.addFieldOffset(0,n,0)}static createOpKernelTypeStrArgsVector(t,n){t.startVector(4,n.length,4);for(let e=n.length-1;e>=0;e--)t.addOffset(n[e]);return t.endVector()}static startOpKernelTypeStrArgsVector(t,n){t.startVector(4,n,4)}static endKernelTypeStrResolver(t){return t.endObject()}static createKernelTypeStrResolver(t,n){return r.startKernelTypeStrResolver(t),r.addOpKernelTypeStrArgs(t,n),r.endKernelTypeStrResolver(t)}};ne.KernelTypeStrResolver=Ho});var Xo=P(re=>{"use strict";var em=re&&re.__createBinding||(Object.create?function(r,t,n,e){e===void 0&&(e=n);var o=Object.getOwnPropertyDescriptor(t,n);(!o||("get"in o?!t.__esModule:o.writable||o.configurable))&&(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(r,e,o)}:function(r,t,n,e){e===void 0&&(e=n),r[e]=t[n]}),nm=re&&re.__setModuleDefault||(Object.create?function(r,t){Object.defineProperty(r,"default",{enumerable:!0,value:t})}:function(r,t){r.default=t}),rm=re&&re.__importStar||function(r){if(r&&r.__esModule)return r;var t={};if(r!=null)for(var n in r)n!=="default"&&Object.prototype.hasOwnProperty.call(r,n)&&em(t,r,n);return nm(t,r),t};Object.defineProperty(re,"__esModule",{value:!0});re.OperatorSetId=void 0;var om=rm(M()),Ko=class r{constructor(){this.bb=null,this.bb_pos=0}__init(t,n){return this.bb_pos=t,this.bb=n,this}static getRootAsOperatorSetId(t,n){return(n||new r).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsOperatorSetId(t,n){return t.setPosition(t.position()+om.SIZE_PREFIX_LENGTH),(n||new r).__init(t.readInt32(t.position())+t.position(),t)}domain(t){let n=this.bb.__offset(this.bb_pos,4);return n?this.bb.__string(this.bb_pos+n,t):null}version(){let t=this.bb.__offset(this.bb_pos,6);return t?this.bb.readInt64(this.bb_pos+t):BigInt("0")}static startOperatorSetId(t){t.startObject(2)}static addDomain(t,n){t.addFieldOffset(0,n,0)}static addVersion(t,n){t.addFieldInt64(1,n,BigInt("0"))}static endOperatorSetId(t){return t.endObject()}static createOperatorSetId(t,n,e){return r.startOperatorSetId(t),r.addDomain(t,n),r.addVersion(t,e),r.endOperatorSetId(t)}};re.OperatorSetId=Ko});var Jo=P(oe=>{"use strict";var im=oe&&oe.__createBinding||(Object.create?function(r,t,n,e){e===void 0&&(e=n);var o=Object.getOwnPropertyDescriptor(t,n);(!o||("get"in o?!t.__esModule:o.writable||o.configurable))&&(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(r,e,o)}:function(r,t,n,e){e===void 0&&(e=n),r[e]=t[n]}),am=oe&&oe.__setModuleDefault||(Object.create?function(r,t){Object.defineProperty(r,"default",{enumerable:!0,value:t})}:function(r,t){r.default=t}),sm=oe&&oe.__importStar||function(r){if(r&&r.__esModule)return r;var t={};if(r!=null)for(var n in r)n!=="default"&&Object.prototype.hasOwnProperty.call(r,n)&&im(t,r,n);return am(t,r),t};Object.defineProperty(oe,"__esModule",{value:!0});oe.StringStringEntry=void 0;var um=sm(M()),Zo=class r{constructor(){this.bb=null,this.bb_pos=0}__init(t,n){return this.bb_pos=t,this.bb=n,this}static getRootAsStringStringEntry(t,n){return(n||new r).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsStringStringEntry(t,n){return t.setPosition(t.position()+um.SIZE_PREFIX_LENGTH),(n||new r).__init(t.readInt32(t.position())+t.position(),t)}key(t){let n=this.bb.__offset(this.bb_pos,4);return n?this.bb.__string(this.bb_pos+n,t):null}value(t){let n=this.bb.__offset(this.bb_pos,6);return n?this.bb.__string(this.bb_pos+n,t):null}static startStringStringEntry(t){t.startObject(2)}static addKey(t,n){t.addFieldOffset(0,n,0)}static addValue(t,n){t.addFieldOffset(1,n,0)}static endStringStringEntry(t){return t.endObject()}static createStringStringEntry(t,n,e){return r.startStringStringEntry(t),r.addKey(t,n),r.addValue(t,e),r.endStringStringEntry(t)}};oe.StringStringEntry=Zo});var Qo=P(ie=>{"use strict";var lm=ie&&ie.__createBinding||(Object.create?function(r,t,n,e){e===void 0&&(e=n);var o=Object.getOwnPropertyDescriptor(t,n);(!o||("get"in o?!t.__esModule:o.writable||o.configurable))&&(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(r,e,o)}:function(r,t,n,e){e===void 0&&(e=n),r[e]=t[n]}),cm=ie&&ie.__setModuleDefault||(Object.create?function(r,t){Object.defineProperty(r,"default",{enumerable:!0,value:t})}:function(r,t){r.default=t}),fm=ie&&ie.__importStar||function(r){if(r&&r.__esModule)return r;var t={};if(r!=null)for(var n in r)n!=="default"&&Object.prototype.hasOwnProperty.call(r,n)&&lm(t,r,n);return cm(t,r),t};Object.defineProperty(ie,"__esModule",{value:!0});ie.Model=void 0;var dm=fm(M()),pm=jn(),hm=Xo(),mm=Jo(),Yo=class r{constructor(){this.bb=null,this.bb_pos=0}__init(t,n){return this.bb_pos=t,this.bb=n,this}static getRootAsModel(t,n){return(n||new r).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsModel(t,n){return t.setPosition(t.position()+dm.SIZE_PREFIX_LENGTH),(n||new r).__init(t.readInt32(t.position())+t.position(),t)}irVersion(){let t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt64(this.bb_pos+t):BigInt("0")}opsetImport(t,n){let e=this.bb.__offset(this.bb_pos,6);return e?(n||new hm.OperatorSetId).__init(this.bb.__indirect(this.bb.__vector(this.bb_pos+e)+t*4),this.bb):null}opsetImportLength(){let t=this.bb.__offset(this.bb_pos,6);return t?this.bb.__vector_len(this.bb_pos+t):0}producerName(t){let n=this.bb.__offset(this.bb_pos,8);return n?this.bb.__string(this.bb_pos+n,t):null}producerVersion(t){let n=this.bb.__offset(this.bb_pos,10);return n?this.bb.__string(this.bb_pos+n,t):null}domain(t){let n=this.bb.__offset(this.bb_pos,12);return n?this.bb.__string(this.bb_pos+n,t):null}modelVersion(){let t=this.bb.__offset(this.bb_pos,14);return t?this.bb.readInt64(this.bb_pos+t):BigInt("0")}docString(t){let n=this.bb.__offset(this.bb_pos,16);return n?this.bb.__string(this.bb_pos+n,t):null}graph(t){let n=this.bb.__offset(this.bb_pos,18);return n?(t||new pm.Graph).__init(this.bb.__indirect(this.bb_pos+n),this.bb):null}graphDocString(t){let n=this.bb.__offset(this.bb_pos,20);return n?this.bb.__string(this.bb_pos+n,t):null}metadataProps(t,n){let e=this.bb.__offset(this.bb_pos,22);return e?(n||new mm.StringStringEntry).__init(this.bb.__indirect(this.bb.__vector(this.bb_pos+e)+t*4),this.bb):null}metadataPropsLength(){let t=this.bb.__offset(this.bb_pos,22);return t?this.bb.__vector_len(this.bb_pos+t):0}static startModel(t){t.startObject(10)}static addIrVersion(t,n){t.addFieldInt64(0,n,BigInt("0"))}static addOpsetImport(t,n){t.addFieldOffset(1,n,0)}static createOpsetImportVector(t,n){t.startVector(4,n.length,4);for(let e=n.length-1;e>=0;e--)t.addOffset(n[e]);return t.endVector()}static startOpsetImportVector(t,n){t.startVector(4,n,4)}static addProducerName(t,n){t.addFieldOffset(2,n,0)}static addProducerVersion(t,n){t.addFieldOffset(3,n,0)}static addDomain(t,n){t.addFieldOffset(4,n,0)}static addModelVersion(t,n){t.addFieldInt64(5,n,BigInt("0"))}static addDocString(t,n){t.addFieldOffset(6,n,0)}static addGraph(t,n){t.addFieldOffset(7,n,0)}static addGraphDocString(t,n){t.addFieldOffset(8,n,0)}static addMetadataProps(t,n){t.addFieldOffset(9,n,0)}static createMetadataPropsVector(t,n){t.startVector(4,n.length,4);for(let e=n.length-1;e>=0;e--)t.addOffset(n[e]);return t.endVector()}static startMetadataPropsVector(t,n){t.startVector(4,n,4)}static endModel(t){return t.endObject()}};ie.Model=Yo});var xs=P(ae=>{"use strict";var bm=ae&&ae.__createBinding||(Object.create?function(r,t,n,e){e===void 0&&(e=n);var o=Object.getOwnPropertyDescriptor(t,n);(!o||("get"in o?!t.__esModule:o.writable||o.configurable))&&(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(r,e,o)}:function(r,t,n,e){e===void 0&&(e=n),r[e]=t[n]}),gm=ae&&ae.__setModuleDefault||(Object.create?function(r,t){Object.defineProperty(r,"default",{enumerable:!0,value:t})}:function(r,t){r.default=t}),ym=ae&&ae.__importStar||function(r){if(r&&r.__esModule)return r;var t={};if(r!=null)for(var n in r)n!=="default"&&Object.prototype.hasOwnProperty.call(r,n)&&bm(t,r,n);return gm(t,r),t};Object.defineProperty(ae,"__esModule",{value:!0});ae.InferenceSession=void 0;var Tm=ym(M()),_m=qo(),xm=Qo(),ti=class r{constructor(){this.bb=null,this.bb_pos=0}__init(t,n){return this.bb_pos=t,this.bb=n,this}static getRootAsInferenceSession(t,n){return(n||new r).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsInferenceSession(t,n){return t.setPosition(t.position()+Tm.SIZE_PREFIX_LENGTH),(n||new r).__init(t.readInt32(t.position())+t.position(),t)}static bufferHasIdentifier(t){return t.__has_identifier("ORTM")}ortVersion(t){let n=this.bb.__offset(this.bb_pos,4);return n?this.bb.__string(this.bb_pos+n,t):null}model(t){let n=this.bb.__offset(this.bb_pos,6);return n?(t||new xm.Model).__init(this.bb.__indirect(this.bb_pos+n),this.bb):null}kernelTypeStrResolver(t){let n=this.bb.__offset(this.bb_pos,10);return n?(t||new _m.KernelTypeStrResolver).__init(this.bb.__indirect(this.bb_pos+n),this.bb):null}static startInferenceSession(t){t.startObject(4)}static addOrtVersion(t,n){t.addFieldOffset(0,n,0)}static addModel(t,n){t.addFieldOffset(1,n,0)}static addKernelTypeStrResolver(t,n){t.addFieldOffset(3,n,0)}static endInferenceSession(t){return t.endObject()}static finishInferenceSessionBuffer(t,n){t.finish(n,"ORTM")}static finishSizePrefixedInferenceSessionBuffer(t,n){t.finish(n,"ORTM",!0)}};ae.InferenceSession=ti});var vm,wm,Wn,gt,Im,Om,Pm,Sm,Am,Em,Dm,Lm,ei,ni,$m,Nm,Fm,Cm,ri,Rm,Gm,km,Mm,Vm,Bm,zm,Um,jm,Wm,Hm,qm,Km,rn,oi,Xm,ii,Zm,vs=y(()=>{"use strict";vm=E(Br()),wm=E(Kr()),Wn=E(Qr()),gt=E(Xr()),Im=E(Ro()),Om=E(_s()),Pm=E(Vo()),Sm=E(Mo()),Am=E(Io()),Em=E(vo()),Dm=E(_o()),Lm=E(eo()),ei=E(jn()),ni=E(xs()),$m=E(Uo()),Nm=E(qo()),Fm=E(go()),Cm=E(Qo()),ri=E(Yr()),Rm=E(ro()),Gm=E(Zr()),km=E(io()),Mm=E(Wo()),Vm=E(Xo()),Bm=E(so()),zm=E(lo()),Um=E(fo()),jm=E(To()),Wm=E(Po()),Hm=E(mo()),qm=E(Jo()),Km=E(en()),rn=E(tn()),oi=E(Ao()),Xm=E(nn()),ii=E(Eo()),Zm=E($o())});var on=y(()=>{"use strict";vs()});var Is=P((Z_,ws)=>{"use strict";ws.exports=Jm;function Jm(r,t){for(var n=new Array(arguments.length-1),e=0,o=2,i=!0;o<arguments.length;)n[e++]=arguments[o++];return new Promise(function(s,u){n[e]=function(c){if(i)if(i=!1,c)u(c);else{for(var d=new Array(arguments.length-1),p=0;p<d.length;)d[p++]=arguments[p];s.apply(null,d)}};try{r.apply(t||null,n)}catch(l){i&&(i=!1,u(l))}})}});var As=P(Ss=>{"use strict";var qn=Ss;qn.length=function(t){var n=t.length;if(!n)return 0;for(var e=0;--n%4>1&&t.charAt(n)==="=";)++e;return Math.ceil(t.length*3)/4-e};var ze=new Array(64),Ps=new Array(123);for(It=0;It<64;)Ps[ze[It]=It<26?It+65:It<52?It+71:It<62?It-4:It-59|43]=It++;var It;qn.encode=function(t,n,e){for(var o=null,i=[],a=0,s=0,u;n<e;){var l=t[n++];switch(s){case 0:i[a++]=ze[l>>2],u=(l&3)<<4,s=1;break;case 1:i[a++]=ze[u|l>>4],u=(l&15)<<2,s=2;break;case 2:i[a++]=ze[u|l>>6],i[a++]=ze[l&63],s=0;break}a>8191&&((o||(o=[])).push(String.fromCharCode.apply(String,i)),a=0)}return s&&(i[a++]=ze[u],i[a++]=61,s===1&&(i[a++]=61)),o?(a&&o.push(String.fromCharCode.apply(String,i.slice(0,a))),o.join("")):String.fromCharCode.apply(String,i.slice(0,a))};var Os="invalid encoding";qn.decode=function(t,n,e){for(var o=e,i=0,a,s=0;s<t.length;){var u=t.charCodeAt(s++);if(u===61&&i>1)break;if((u=Ps[u])===void 0)throw Error(Os);switch(i){case 0:a=u,i=1;break;case 1:n[e++]=a<<2|(u&48)>>4,a=u,i=2;break;case 2:n[e++]=(a&15)<<4|(u&60)>>2,a=u,i=3;break;case 3:n[e++]=(a&3)<<6|u,i=0;break}}if(i===1)throw Error(Os);return e-o};qn.test=function(t){return/^(?:[A-Za-z0-9+/]{4})*(?:[A-Za-z0-9+/]{2}==|[A-Za-z0-9+/]{3}=)?$/.test(t)}});var Ds=P((Y_,Es)=>{"use strict";Es.exports=Kn;function Kn(){this._listeners={}}Kn.prototype.on=function(t,n,e){return(this._listeners[t]||(this._listeners[t]=[])).push({fn:n,ctx:e||this}),this};Kn.prototype.off=function(t,n){if(t===void 0)this._listeners={};else if(n===void 0)this._listeners[t]=[];else for(var e=this._listeners[t],o=0;o<e.length;)e[o].fn===n?e.splice(o,1):++o;return this};Kn.prototype.emit=function(t){var n=this._listeners[t];if(n){for(var e=[],o=1;o<arguments.length;)e.push(arguments[o++]);for(o=0;o<n.length;)n[o].fn.apply(n[o++].ctx,e)}return this}});var Gs=P((Q_,Rs)=>{"use strict";Rs.exports=Ls(Ls);function Ls(r){return typeof Float32Array<"u"?function(){var t=new Float32Array([-0]),n=new Uint8Array(t.buffer),e=n[3]===128;function o(u,l,c){t[0]=u,l[c]=n[0],l[c+1]=n[1],l[c+2]=n[2],l[c+3]=n[3]}function i(u,l,c){t[0]=u,l[c]=n[3],l[c+1]=n[2],l[c+2]=n[1],l[c+3]=n[0]}r.writeFloatLE=e?o:i,r.writeFloatBE=e?i:o;function a(u,l){return n[0]=u[l],n[1]=u[l+1],n[2]=u[l+2],n[3]=u[l+3],t[0]}function s(u,l){return n[3]=u[l],n[2]=u[l+1],n[1]=u[l+2],n[0]=u[l+3],t[0]}r.readFloatLE=e?a:s,r.readFloatBE=e?s:a}():function(){function t(e,o,i,a){var s=o<0?1:0;if(s&&(o=-o),o===0)e(1/o>0?0:2147483648,i,a);else if(isNaN(o))e(2143289344,i,a);else if(o>34028234663852886e22)e((s<<31|2139095040)>>>0,i,a);else if(o<11754943508222875e-54)e((s<<31|Math.round(o/1401298464324817e-60))>>>0,i,a);else{var u=Math.floor(Math.log(o)/Math.LN2),l=Math.round(o*Math.pow(2,-u)*8388608)&8388607;e((s<<31|u+127<<23|l)>>>0,i,a)}}r.writeFloatLE=t.bind(null,$s),r.writeFloatBE=t.bind(null,Ns);function n(e,o,i){var a=e(o,i),s=(a>>31)*2+1,u=a>>>23&255,l=a&8388607;return u===255?l?NaN:s*(1/0):u===0?s*1401298464324817e-60*l:s*Math.pow(2,u-150)*(l+8388608)}r.readFloatLE=n.bind(null,Fs),r.readFloatBE=n.bind(null,Cs)}(),typeof Float64Array<"u"?function(){var t=new Float64Array([-0]),n=new Uint8Array(t.buffer),e=n[7]===128;function o(u,l,c){t[0]=u,l[c]=n[0],l[c+1]=n[1],l[c+2]=n[2],l[c+3]=n[3],l[c+4]=n[4],l[c+5]=n[5],l[c+6]=n[6],l[c+7]=n[7]}function i(u,l,c){t[0]=u,l[c]=n[7],l[c+1]=n[6],l[c+2]=n[5],l[c+3]=n[4],l[c+4]=n[3],l[c+5]=n[2],l[c+6]=n[1],l[c+7]=n[0]}r.writeDoubleLE=e?o:i,r.writeDoubleBE=e?i:o;function a(u,l){return n[0]=u[l],n[1]=u[l+1],n[2]=u[l+2],n[3]=u[l+3],n[4]=u[l+4],n[5]=u[l+5],n[6]=u[l+6],n[7]=u[l+7],t[0]}function s(u,l){return n[7]=u[l],n[6]=u[l+1],n[5]=u[l+2],n[4]=u[l+3],n[3]=u[l+4],n[2]=u[l+5],n[1]=u[l+6],n[0]=u[l+7],t[0]}r.readDoubleLE=e?a:s,r.readDoubleBE=e?s:a}():function(){function t(e,o,i,a,s,u){var l=a<0?1:0;if(l&&(a=-a),a===0)e(0,s,u+o),e(1/a>0?0:2147483648,s,u+i);else if(isNaN(a))e(0,s,u+o),e(2146959360,s,u+i);else if(a>17976931348623157e292)e(0,s,u+o),e((l<<31|2146435072)>>>0,s,u+i);else{var c;if(a<22250738585072014e-324)c=a/5e-324,e(c>>>0,s,u+o),e((l<<31|c/4294967296)>>>0,s,u+i);else{var d=Math.floor(Math.log(a)/Math.LN2);d===1024&&(d=1023),c=a*Math.pow(2,-d),e(c*4503599627370496>>>0,s,u+o),e((l<<31|d+1023<<20|c*1048576&1048575)>>>0,s,u+i)}}}r.writeDoubleLE=t.bind(null,$s,0,4),r.writeDoubleBE=t.bind(null,Ns,4,0);function n(e,o,i,a,s){var u=e(a,s+o),l=e(a,s+i),c=(l>>31)*2+1,d=l>>>20&2047,p=4294967296*(l&1048575)+u;return d===2047?p?NaN:c*(1/0):d===0?c*5e-324*p:c*Math.pow(2,d-1075)*(p+4503599627370496)}r.readDoubleLE=n.bind(null,Fs,0,4),r.readDoubleBE=n.bind(null,Cs,4,0)}(),r}function $s(r,t,n){t[n]=r&255,t[n+1]=r>>>8&255,t[n+2]=r>>>16&255,t[n+3]=r>>>24}function Ns(r,t,n){t[n]=r>>>24,t[n+1]=r>>>16&255,t[n+2]=r>>>8&255,t[n+3]=r&255}function Fs(r,t){return(r[t]|r[t+1]<<8|r[t+2]<<16|r[t+3]<<24)>>>0}function Cs(r,t){return(r[t]<<24|r[t+1]<<16|r[t+2]<<8|r[t+3])>>>0}});var ks=P((exports,module)=>{"use strict";module.exports=inquire;function inquire(moduleName){try{var mod=eval("quire".replace(/^/,"re"))(moduleName);if(mod&&(mod.length||Object.keys(mod).length))return mod}catch(r){}return null}});var Vs=P(Ms=>{"use strict";var ai=Ms;ai.length=function(t){for(var n=0,e=0,o=0;o<t.length;++o)e=t.charCodeAt(o),e<128?n+=1:e<2048?n+=2:(e&64512)===55296&&(t.charCodeAt(o+1)&64512)===56320?(++o,n+=4):n+=3;return n};ai.read=function(t,n,e){var o=e-n;if(o<1)return"";for(var i=null,a=[],s=0,u;n<e;)u=t[n++],u<128?a[s++]=u:u>191&&u<224?a[s++]=(u&31)<<6|t[n++]&63:u>239&&u<365?(u=((u&7)<<18|(t[n++]&63)<<12|(t[n++]&63)<<6|t[n++]&63)-65536,a[s++]=55296+(u>>10),a[s++]=56320+(u&1023)):a[s++]=(u&15)<<12|(t[n++]&63)<<6|t[n++]&63,s>8191&&((i||(i=[])).push(String.fromCharCode.apply(String,a)),s=0);return i?(s&&i.push(String.fromCharCode.apply(String,a.slice(0,s))),i.join("")):String.fromCharCode.apply(String,a.slice(0,s))};ai.write=function(t,n,e){for(var o=e,i,a,s=0;s<t.length;++s)i=t.charCodeAt(s),i<128?n[e++]=i:i<2048?(n[e++]=i>>6|192,n[e++]=i&63|128):(i&64512)===55296&&((a=t.charCodeAt(s+1))&64512)===56320?(i=65536+((i&1023)<<10)+(a&1023),++s,n[e++]=i>>18|240,n[e++]=i>>12&63|128,n[e++]=i>>6&63|128,n[e++]=i&63|128):(n[e++]=i>>12|224,n[e++]=i>>6&63|128,n[e++]=i&63|128);return e-o}});var zs=P((ex,Bs)=>{"use strict";Bs.exports=Ym;function Ym(r,t,n){var e=n||8192,o=e>>>1,i=null,a=e;return function(u){if(u<1||u>o)return r(u);a+u>e&&(i=r(e),a=0);var l=t.call(i,a,a+=u);return a&7&&(a=(a|7)+1),l}}});var js=P((nx,Us)=>{"use strict";Us.exports=Q;var an=_e();function Q(r,t){this.lo=r>>>0,this.hi=t>>>0}var Ee=Q.zero=new Q(0,0);Ee.toNumber=function(){return 0};Ee.zzEncode=Ee.zzDecode=function(){return this};Ee.length=function(){return 1};var Qm=Q.zeroHash="\0\0\0\0\0\0\0\0";Q.fromNumber=function(t){if(t===0)return Ee;var n=t<0;n&&(t=-t);var e=t>>>0,o=(t-e)/4294967296>>>0;return n&&(o=~o>>>0,e=~e>>>0,++e>4294967295&&(e=0,++o>4294967295&&(o=0))),new Q(e,o)};Q.from=function(t){if(typeof t=="number")return Q.fromNumber(t);if(an.isString(t))if(an.Long)t=an.Long.fromString(t);else return Q.fromNumber(parseInt(t,10));return t.low||t.high?new Q(t.low>>>0,t.high>>>0):Ee};Q.prototype.toNumber=function(t){if(!t&&this.hi>>>31){var n=~this.lo+1>>>0,e=~this.hi>>>0;return n||(e=e+1>>>0),-(n+e*4294967296)}return this.lo+this.hi*4294967296};Q.prototype.toLong=function(t){return an.Long?new an.Long(this.lo|0,this.hi|0,!!t):{low:this.lo|0,high:this.hi|0,unsigned:!!t}};var Te=String.prototype.charCodeAt;Q.fromHash=function(t){return t===Qm?Ee:new Q((Te.call(t,0)|Te.call(t,1)<<8|Te.call(t,2)<<16|Te.call(t,3)<<24)>>>0,(Te.call(t,4)|Te.call(t,5)<<8|Te.call(t,6)<<16|Te.call(t,7)<<24)>>>0)};Q.prototype.toHash=function(){return String.fromCharCode(this.lo&255,this.lo>>>8&255,this.lo>>>16&255,this.lo>>>24,this.hi&255,this.hi>>>8&255,this.hi>>>16&255,this.hi>>>24)};Q.prototype.zzEncode=function(){var t=this.hi>>31;return this.hi=((this.hi<<1|this.lo>>>31)^t)>>>0,this.lo=(this.lo<<1^t)>>>0,this};Q.prototype.zzDecode=function(){var t=-(this.lo&1);return this.lo=((this.lo>>>1|this.hi<<31)^t)>>>0,this.hi=(this.hi>>>1^t)>>>0,this};Q.prototype.length=function(){var t=this.lo,n=(this.lo>>>28|this.hi<<4)>>>0,e=this.hi>>>24;return e===0?n===0?t<16384?t<128?1:2:t<2097152?3:4:n<16384?n<128?5:6:n<2097152?7:8:e<128?9:10}});var _e=P(si=>{"use strict";var S=si;S.asPromise=Is();S.base64=As();S.EventEmitter=Ds();S.float=Gs();S.inquire=ks();S.utf8=Vs();S.pool=zs();S.LongBits=js();S.isNode=!!(typeof global<"u"&&global&&global.process&&global.process.versions&&global.process.versions.node);S.global=S.isNode&&global||typeof window<"u"&&window||typeof self<"u"&&self||si;S.emptyArray=Object.freeze?Object.freeze([]):[];S.emptyObject=Object.freeze?Object.freeze({}):{};S.isInteger=Number.isInteger||function(t){return typeof t=="number"&&isFinite(t)&&Math.floor(t)===t};S.isString=function(t){return typeof t=="string"||t instanceof String};S.isObject=function(t){return t&&typeof t=="object"};S.isset=S.isSet=function(t,n){var e=t[n];return e!=null&&t.hasOwnProperty(n)?typeof e!="object"||(Array.isArray(e)?e.length:Object.keys(e).length)>0:!1};S.Buffer=function(){try{var r=S.inquire("buffer").Buffer;return r.prototype.utf8Write?r:null}catch{return null}}();S._Buffer_from=null;S._Buffer_allocUnsafe=null;S.newBuffer=function(t){return typeof t=="number"?S.Buffer?S._Buffer_allocUnsafe(t):new S.Array(t):S.Buffer?S._Buffer_from(t):typeof Uint8Array>"u"?t:new Uint8Array(t)};S.Array=typeof Uint8Array<"u"?Uint8Array:Array;S.Long=S.global.dcodeIO&&S.global.dcodeIO.Long||S.global.Long||S.inquire("long");S.key2Re=/^true|false|0|1$/;S.key32Re=/^-?(?:0|[1-9][0-9]*)$/;S.key64Re=/^(?:[\\x00-\\xff]{8}|-?(?:0|[1-9][0-9]*))$/;S.longToHash=function(t){return t?S.LongBits.from(t).toHash():S.LongBits.zeroHash};S.longFromHash=function(t,n){var e=S.LongBits.fromHash(t);return S.Long?S.Long.fromBits(e.lo,e.hi,n):e.toNumber(!!n)};function Ws(r,t,n){for(var e=Object.keys(t),o=0;o<e.length;++o)(r[e[o]]===void 0||!n)&&(r[e[o]]=t[e[o]]);return r}S.merge=Ws;S.lcFirst=function(t){return t.charAt(0).toLowerCase()+t.substring(1)};function Hs(r){function t(n,e){if(!(this instanceof t))return new t(n,e);Object.defineProperty(this,"message",{get:function(){return n}}),Error.captureStackTrace?Error.captureStackTrace(this,t):Object.defineProperty(this,"stack",{value:new Error().stack||""}),e&&Ws(this,e)}return t.prototype=Object.create(Error.prototype,{constructor:{value:t,writable:!0,enumerable:!1,configurable:!0},name:{get:function(){return r},set:void 0,enumerable:!1,configurable:!0},toString:{value:function(){return this.name+": "+this.message},writable:!0,enumerable:!1,configurable:!0}}),t}S.newError=Hs;S.ProtocolError=Hs("ProtocolError");S.oneOfGetter=function(t){for(var n={},e=0;e<t.length;++e)n[t[e]]=1;return function(){for(var o=Object.keys(this),i=o.length-1;i>-1;--i)if(n[o[i]]===1&&this[o[i]]!==void 0&&this[o[i]]!==null)return o[i]}};S.oneOfSetter=function(t){return function(n){for(var e=0;e<t.length;++e)t[e]!==n&&delete this[t[e]]}};S.toJSONOptions={longs:String,enums:String,bytes:String,json:!0};S._configure=function(){var r=S.Buffer;if(!r){S._Buffer_from=S._Buffer_allocUnsafe=null;return}S._Buffer_from=r.from!==Uint8Array.from&&r.from||function(n,e){return new r(n,e)},S._Buffer_allocUnsafe=r.allocUnsafe||function(n){return new r(n)}}});var hi=P((ox,Zs)=>{"use strict";Zs.exports=R;var yt=_e(),ui,Xn=yt.LongBits,qs=yt.base64,Ks=yt.utf8;function sn(r,t,n){this.fn=r,this.len=t,this.next=void 0,this.val=n}function ci(){}function tb(r){this.head=r.head,this.tail=r.tail,this.len=r.len,this.next=r.states}function R(){this.len=0,this.head=new sn(ci,0,0),this.tail=this.head,this.states=null}var Xs=function(){return yt.Buffer?function(){return(R.create=function(){return new ui})()}:function(){return new R}};R.create=Xs();R.alloc=function(t){return new yt.Array(t)};yt.Array!==Array&&(R.alloc=yt.pool(R.alloc,yt.Array.prototype.subarray));R.prototype._push=function(t,n,e){return this.tail=this.tail.next=new sn(t,n,e),this.len+=n,this};function fi(r,t,n){t[n]=r&255}function eb(r,t,n){for(;r>127;)t[n++]=r&127|128,r>>>=7;t[n]=r}function di(r,t){this.len=r,this.next=void 0,this.val=t}di.prototype=Object.create(sn.prototype);di.prototype.fn=eb;R.prototype.uint32=function(t){return this.len+=(this.tail=this.tail.next=new di((t=t>>>0)<128?1:t<16384?2:t<2097152?3:t<268435456?4:5,t)).len,this};R.prototype.int32=function(t){return t<0?this._push(pi,10,Xn.fromNumber(t)):this.uint32(t)};R.prototype.sint32=function(t){return this.uint32((t<<1^t>>31)>>>0)};function pi(r,t,n){for(;r.hi;)t[n++]=r.lo&127|128,r.lo=(r.lo>>>7|r.hi<<25)>>>0,r.hi>>>=7;for(;r.lo>127;)t[n++]=r.lo&127|128,r.lo=r.lo>>>7;t[n++]=r.lo}R.prototype.uint64=function(t){var n=Xn.from(t);return this._push(pi,n.length(),n)};R.prototype.int64=R.prototype.uint64;R.prototype.sint64=function(t){var n=Xn.from(t).zzEncode();return this._push(pi,n.length(),n)};R.prototype.bool=function(t){return this._push(fi,1,t?1:0)};function li(r,t,n){t[n]=r&255,t[n+1]=r>>>8&255,t[n+2]=r>>>16&255,t[n+3]=r>>>24}R.prototype.fixed32=function(t){return this._push(li,4,t>>>0)};R.prototype.sfixed32=R.prototype.fixed32;R.prototype.fixed64=function(t){var n=Xn.from(t);return this._push(li,4,n.lo)._push(li,4,n.hi)};R.prototype.sfixed64=R.prototype.fixed64;R.prototype.float=function(t){return this._push(yt.float.writeFloatLE,4,t)};R.prototype.double=function(t){return this._push(yt.float.writeDoubleLE,8,t)};var nb=yt.Array.prototype.set?function(t,n,e){n.set(t,e)}:function(t,n,e){for(var o=0;o<t.length;++o)n[e+o]=t[o]};R.prototype.bytes=function(t){var n=t.length>>>0;if(!n)return this._push(fi,1,0);if(yt.isString(t)){var e=R.alloc(n=qs.length(t));qs.decode(t,e,0),t=e}return this.uint32(n)._push(nb,n,t)};R.prototype.string=function(t){var n=Ks.length(t);return n?this.uint32(n)._push(Ks.write,n,t):this._push(fi,1,0)};R.prototype.fork=function(){return this.states=new tb(this),this.head=this.tail=new sn(ci,0,0),this.len=0,this};R.prototype.reset=function(){return this.states?(this.head=this.states.head,this.tail=this.states.tail,this.len=this.states.len,this.states=this.states.next):(this.head=this.tail=new sn(ci,0,0),this.len=0),this};R.prototype.ldelim=function(){var t=this.head,n=this.tail,e=this.len;return this.reset().uint32(e),e&&(this.tail.next=t.next,this.tail=n,this.len+=e),this};R.prototype.finish=function(){for(var t=this.head.next,n=this.constructor.alloc(this.len),e=0;t;)t.fn(t.val,n,e),e+=t.len,t=t.next;return n};R._configure=function(r){ui=r,R.create=Xs(),ui._configure()}});var Qs=P((ix,Ys)=>{"use strict";Ys.exports=se;var Js=hi();(se.prototype=Object.create(Js.prototype)).constructor=se;var xe=_e();function se(){Js.call(this)}se._configure=function(){se.alloc=xe._Buffer_allocUnsafe,se.writeBytesBuffer=xe.Buffer&&xe.Buffer.prototype instanceof Uint8Array&&xe.Buffer.prototype.set.name==="set"?function(t,n,e){n.set(t,e)}:function(t,n,e){if(t.copy)t.copy(n,e,0,t.length);else for(var o=0;o<t.length;)n[e++]=t[o++]}};se.prototype.bytes=function(t){xe.isString(t)&&(t=xe._Buffer_from(t,"base64"));var n=t.length>>>0;return this.uint32(n),n&&this._push(se.writeBytesBuffer,n,t),this};function rb(r,t,n){r.length<40?xe.utf8.write(r,t,n):t.utf8Write?t.utf8Write(r,n):t.write(r,n)}se.prototype.string=function(t){var n=xe.Buffer.byteLength(t);return this.uint32(n),n&&this._push(rb,n,t),this};se._configure()});var gi=P((ax,ou)=>{"use strict";ou.exports=X;var Ot=_e(),bi,nu=Ot.LongBits,ob=Ot.utf8;function Pt(r,t){return RangeError("index out of range: "+r.pos+" + "+(t||1)+" > "+r.len)}function X(r){this.buf=r,this.pos=0,this.len=r.length}var tu=typeof Uint8Array<"u"?function(t){if(t instanceof Uint8Array||Array.isArray(t))return new X(t);throw Error("illegal buffer")}:function(t){if(Array.isArray(t))return new X(t);throw Error("illegal buffer")},ru=function(){return Ot.Buffer?function(n){return(X.create=function(o){return Ot.Buffer.isBuffer(o)?new bi(o):tu(o)})(n)}:tu};X.create=ru();X.prototype._slice=Ot.Array.prototype.subarray||Ot.Array.prototype.slice;X.prototype.uint32=function(){var t=4294967295;return function(){if(t=(this.buf[this.pos]&127)>>>0,this.buf[this.pos++]<128||(t=(t|(this.buf[this.pos]&127)<<7)>>>0,this.buf[this.pos++]<128)||(t=(t|(this.buf[this.pos]&127)<<14)>>>0,this.buf[this.pos++]<128)||(t=(t|(this.buf[this.pos]&127)<<21)>>>0,this.buf[this.pos++]<128)||(t=(t|(this.buf[this.pos]&15)<<28)>>>0,this.buf[this.pos++]<128))return t;if((this.pos+=5)>this.len)throw this.pos=this.len,Pt(this,10);return t}}();X.prototype.int32=function(){return this.uint32()|0};X.prototype.sint32=function(){var t=this.uint32();return t>>>1^-(t&1)|0};function mi(){var r=new nu(0,0),t=0;if(this.len-this.pos>4){for(;t<4;++t)if(r.lo=(r.lo|(this.buf[this.pos]&127)<<t*7)>>>0,this.buf[this.pos++]<128)return r;if(r.lo=(r.lo|(this.buf[this.pos]&127)<<28)>>>0,r.hi=(r.hi|(this.buf[this.pos]&127)>>4)>>>0,this.buf[this.pos++]<128)return r;t=0}else{for(;t<3;++t){if(this.pos>=this.len)throw Pt(this);if(r.lo=(r.lo|(this.buf[this.pos]&127)<<t*7)>>>0,this.buf[this.pos++]<128)return r}return r.lo=(r.lo|(this.buf[this.pos++]&127)<<t*7)>>>0,r}if(this.len-this.pos>4){for(;t<5;++t)if(r.hi=(r.hi|(this.buf[this.pos]&127)<<t*7+3)>>>0,this.buf[this.pos++]<128)return r}else for(;t<5;++t){if(this.pos>=this.len)throw Pt(this);if(r.hi=(r.hi|(this.buf[this.pos]&127)<<t*7+3)>>>0,this.buf[this.pos++]<128)return r}throw Error("invalid varint encoding")}X.prototype.bool=function(){return this.uint32()!==0};function Zn(r,t){return(r[t-4]|r[t-3]<<8|r[t-2]<<16|r[t-1]<<24)>>>0}X.prototype.fixed32=function(){if(this.pos+4>this.len)throw Pt(this,4);return Zn(this.buf,this.pos+=4)};X.prototype.sfixed32=function(){if(this.pos+4>this.len)throw Pt(this,4);return Zn(this.buf,this.pos+=4)|0};function eu(){if(this.pos+8>this.len)throw Pt(this,8);return new nu(Zn(this.buf,this.pos+=4),Zn(this.buf,this.pos+=4))}X.prototype.float=function(){if(this.pos+4>this.len)throw Pt(this,4);var t=Ot.float.readFloatLE(this.buf,this.pos);return this.pos+=4,t};X.prototype.double=function(){if(this.pos+8>this.len)throw Pt(this,4);var t=Ot.float.readDoubleLE(this.buf,this.pos);return this.pos+=8,t};X.prototype.bytes=function(){var t=this.uint32(),n=this.pos,e=this.pos+t;if(e>this.len)throw Pt(this,t);if(this.pos+=t,Array.isArray(this.buf))return this.buf.slice(n,e);if(n===e){var o=Ot.Buffer;return o?o.alloc(0):new this.buf.constructor(0)}return this._slice.call(this.buf,n,e)};X.prototype.string=function(){var t=this.bytes();return ob.read(t,0,t.length)};X.prototype.skip=function(t){if(typeof t=="number"){if(this.pos+t>this.len)throw Pt(this,t);this.pos+=t}else do if(this.pos>=this.len)throw Pt(this);while(this.buf[this.pos++]&128);return this};X.prototype.skipType=function(r){switch(r){case 0:this.skip();break;case 1:this.skip(8);break;case 2:this.skip(this.uint32());break;case 3:for(;(r=this.uint32()&7)!==4;)this.skipType(r);break;case 5:this.skip(4);break;default:throw Error("invalid wire type "+r+" at offset "+this.pos)}return this};X._configure=function(r){bi=r,X.create=ru(),bi._configure();var t=Ot.Long?"toLong":"toNumber";Ot.merge(X.prototype,{int64:function(){return mi.call(this)[t](!1)},uint64:function(){return mi.call(this)[t](!0)},sint64:function(){return mi.call(this).zzDecode()[t](!1)},fixed64:function(){return eu.call(this)[t](!0)},sfixed64:function(){return eu.call(this)[t](!1)}})}});var uu=P((sx,su)=>{"use strict";su.exports=De;var au=gi();(De.prototype=Object.create(au.prototype)).constructor=De;var iu=_e();function De(r){au.call(this,r)}De._configure=function(){iu.Buffer&&(De.prototype._slice=iu.Buffer.prototype.slice)};De.prototype.string=function(){var t=this.uint32();return this.buf.utf8Slice?this.buf.utf8Slice(this.pos,this.pos=Math.min(this.pos+t,this.len)):this.buf.toString("utf-8",this.pos,this.pos=Math.min(this.pos+t,this.len))};De._configure()});var cu=P((ux,lu)=>{"use strict";lu.exports=un;var yi=_e();(un.prototype=Object.create(yi.EventEmitter.prototype)).constructor=un;function un(r,t,n){if(typeof r!="function")throw TypeError("rpcImpl must be a function");yi.EventEmitter.call(this),this.rpcImpl=r,this.requestDelimited=!!t,this.responseDelimited=!!n}un.prototype.rpcCall=function r(t,n,e,o,i){if(!o)throw TypeError("request must be specified");var a=this;if(!i)return yi.asPromise(r,a,t,n,e,o);if(!a.rpcImpl){setTimeout(function(){i(Error("already ended"))},0);return}try{return a.rpcImpl(t,n[a.requestDelimited?"encodeDelimited":"encode"](o).finish(),function(u,l){if(u)return a.emit("error",u,t),i(u);if(l===null){a.end(!0);return}if(!(l instanceof e))try{l=e[a.responseDelimited?"decodeDelimited":"decode"](l)}catch(c){return a.emit("error",c,t),i(c)}return a.emit("data",l,t),i(null,l)})}catch(s){a.emit("error",s,t),setTimeout(function(){i(s)},0);return}};un.prototype.end=function(t){return this.rpcImpl&&(t||this.rpcImpl(null,null,null),this.rpcImpl=null,this.emit("end").off()),this}});var du=P(fu=>{"use strict";var ib=fu;ib.Service=cu()});var hu=P((cx,pu)=>{"use strict";pu.exports={}});var gu=P(bu=>{"use strict";var lt=bu;lt.build="minimal";lt.Writer=hi();lt.BufferWriter=Qs();lt.Reader=gi();lt.BufferReader=uu();lt.util=_e();lt.rpc=du();lt.roots=hu();lt.configure=mu;function mu(){lt.util._configure(),lt.Writer._configure(lt.BufferWriter),lt.Reader._configure(lt.BufferReader)}mu()});var Tu=P((dx,yu)=>{"use strict";yu.exports=gu()});var Ue=P((px,_u)=>{"use strict";var j=Tu(),_=j.Reader,Z=j.Writer,h=j.util,f=j.roots.default||(j.roots.default={});f.onnx=function(){var r={};return r.Version=function(){var t={},n=Object.create(t);return n[t[0]="_START_VERSION"]=0,n[t[1]="IR_VERSION_2017_10_10"]=1,n[t[2]="IR_VERSION_2017_10_30"]=2,n[t[3]="IR_VERSION_2017_11_3"]=3,n[t[4]="IR_VERSION_2019_1_22"]=4,n[t[5]="IR_VERSION_2019_3_18"]=5,n[t[6]="IR_VERSION_2019_9_19"]=6,n[t[7]="IR_VERSION_2020_5_8"]=7,n[t[8]="IR_VERSION_2021_7_30"]=8,n[t[9]="IR_VERSION"]=9,n}(),r.AttributeProto=function(){function t(n){if(this.floats=[],this.ints=[],this.strings=[],this.tensors=[],this.graphs=[],this.sparseTensors=[],this.typeProtos=[],n)for(var e=Object.keys(n),o=0;o<e.length;++o)n[e[o]]!=null&&(this[e[o]]=n[e[o]])}return t.prototype.name="",t.prototype.refAttrName="",t.prototype.docString="",t.prototype.type=0,t.prototype.f=0,t.prototype.i=h.Long?h.Long.fromBits(0,0,!1):0,t.prototype.s=h.newBuffer([]),t.prototype.t=null,t.prototype.g=null,t.prototype.sparseTensor=null,t.prototype.tp=null,t.prototype.floats=h.emptyArray,t.prototype.ints=h.emptyArray,t.prototype.strings=h.emptyArray,t.prototype.tensors=h.emptyArray,t.prototype.graphs=h.emptyArray,t.prototype.sparseTensors=h.emptyArray,t.prototype.typeProtos=h.emptyArray,t.create=function(e){return new t(e)},t.encode=function(e,o){if(o||(o=Z.create()),e.name!=null&&Object.hasOwnProperty.call(e,"name")&&o.uint32(10).string(e.name),e.f!=null&&Object.hasOwnProperty.call(e,"f")&&o.uint32(21).float(e.f),e.i!=null&&Object.hasOwnProperty.call(e,"i")&&o.uint32(24).int64(e.i),e.s!=null&&Object.hasOwnProperty.call(e,"s")&&o.uint32(34).bytes(e.s),e.t!=null&&Object.hasOwnProperty.call(e,"t")&&f.onnx.TensorProto.encode(e.t,o.uint32(42).fork()).ldelim(),e.g!=null&&Object.hasOwnProperty.call(e,"g")&&f.onnx.GraphProto.encode(e.g,o.uint32(50).fork()).ldelim(),e.floats!=null&&e.floats.length){o.uint32(58).fork();for(var i=0;i<e.floats.length;++i)o.float(e.floats[i]);o.ldelim()}if(e.ints!=null&&e.ints.length){o.uint32(66).fork();for(var i=0;i<e.ints.length;++i)o.int64(e.ints[i]);o.ldelim()}if(e.strings!=null&&e.strings.length)for(var i=0;i<e.strings.length;++i)o.uint32(74).bytes(e.strings[i]);if(e.tensors!=null&&e.tensors.length)for(var i=0;i<e.tensors.length;++i)f.onnx.TensorProto.encode(e.tensors[i],o.uint32(82).fork()).ldelim();if(e.graphs!=null&&e.graphs.length)for(var i=0;i<e.graphs.length;++i)f.onnx.GraphProto.encode(e.graphs[i],o.uint32(90).fork()).ldelim();if(e.docString!=null&&Object.hasOwnProperty.call(e,"docString")&&o.uint32(106).string(e.docString),e.tp!=null&&Object.hasOwnProperty.call(e,"tp")&&f.onnx.TypeProto.encode(e.tp,o.uint32(114).fork()).ldelim(),e.typeProtos!=null&&e.typeProtos.length)for(var i=0;i<e.typeProtos.length;++i)f.onnx.TypeProto.encode(e.typeProtos[i],o.uint32(122).fork()).ldelim();if(e.type!=null&&Object.hasOwnProperty.call(e,"type")&&o.uint32(160).int32(e.type),e.refAttrName!=null&&Object.hasOwnProperty.call(e,"refAttrName")&&o.uint32(170).string(e.refAttrName),e.sparseTensor!=null&&Object.hasOwnProperty.call(e,"sparseTensor")&&f.onnx.SparseTensorProto.encode(e.sparseTensor,o.uint32(178).fork()).ldelim(),e.sparseTensors!=null&&e.sparseTensors.length)for(var i=0;i<e.sparseTensors.length;++i)f.onnx.SparseTensorProto.encode(e.sparseTensors[i],o.uint32(186).fork()).ldelim();return o},t.encodeDelimited=function(e,o){return this.encode(e,o).ldelim()},t.decode=function(e,o){e instanceof _||(e=_.create(e));for(var i=o===void 0?e.len:e.pos+o,a=new f.onnx.AttributeProto;e.pos<i;){var s=e.uint32();switch(s>>>3){case 1:{a.name=e.string();break}case 21:{a.refAttrName=e.string();break}case 13:{a.docString=e.string();break}case 20:{a.type=e.int32();break}case 2:{a.f=e.float();break}case 3:{a.i=e.int64();break}case 4:{a.s=e.bytes();break}case 5:{a.t=f.onnx.TensorProto.decode(e,e.uint32());break}case 6:{a.g=f.onnx.GraphProto.decode(e,e.uint32());break}case 22:{a.sparseTensor=f.onnx.SparseTensorProto.decode(e,e.uint32());break}case 14:{a.tp=f.onnx.TypeProto.decode(e,e.uint32());break}case 7:{if(a.floats&&a.floats.length||(a.floats=[]),(s&7)===2)for(var u=e.uint32()+e.pos;e.pos<u;)a.floats.push(e.float());else a.floats.push(e.float());break}case 8:{if(a.ints&&a.ints.length||(a.ints=[]),(s&7)===2)for(var u=e.uint32()+e.pos;e.pos<u;)a.ints.push(e.int64());else a.ints.push(e.int64());break}case 9:{a.strings&&a.strings.length||(a.strings=[]),a.strings.push(e.bytes());break}case 10:{a.tensors&&a.tensors.length||(a.tensors=[]),a.tensors.push(f.onnx.TensorProto.decode(e,e.uint32()));break}case 11:{a.graphs&&a.graphs.length||(a.graphs=[]),a.graphs.push(f.onnx.GraphProto.decode(e,e.uint32()));break}case 23:{a.sparseTensors&&a.sparseTensors.length||(a.sparseTensors=[]),a.sparseTensors.push(f.onnx.SparseTensorProto.decode(e,e.uint32()));break}case 15:{a.typeProtos&&a.typeProtos.length||(a.typeProtos=[]),a.typeProtos.push(f.onnx.TypeProto.decode(e,e.uint32()));break}default:e.skipType(s&7);break}}return a},t.decodeDelimited=function(e){return e instanceof _||(e=new _(e)),this.decode(e,e.uint32())},t.verify=function(e){if(typeof e!="object"||e===null)return"object expected";if(e.name!=null&&e.hasOwnProperty("name")&&!h.isString(e.name))return"name: string expected";if(e.refAttrName!=null&&e.hasOwnProperty("refAttrName")&&!h.isString(e.refAttrName))return"refAttrName: string expected";if(e.docString!=null&&e.hasOwnProperty("docString")&&!h.isString(e.docString))return"docString: string expected";if(e.type!=null&&e.hasOwnProperty("type"))switch(e.type){default:return"type: enum value expected";case 0:case 1:case 2:case 3:case 4:case 5:case 11:case 13:case 6:case 7:case 8:case 9:case 10:case 12:case 14:break}if(e.f!=null&&e.hasOwnProperty("f")&&typeof e.f!="number")return"f: number expected";if(e.i!=null&&e.hasOwnProperty("i")&&!h.isInteger(e.i)&&!(e.i&&h.isInteger(e.i.low)&&h.isInteger(e.i.high)))return"i: integer|Long expected";if(e.s!=null&&e.hasOwnProperty("s")&&!(e.s&&typeof e.s.length=="number"||h.isString(e.s)))return"s: buffer expected";if(e.t!=null&&e.hasOwnProperty("t")){var o=f.onnx.TensorProto.verify(e.t);if(o)return"t."+o}if(e.g!=null&&e.hasOwnProperty("g")){var o=f.onnx.GraphProto.verify(e.g);if(o)return"g."+o}if(e.sparseTensor!=null&&e.hasOwnProperty("sparseTensor")){var o=f.onnx.SparseTensorProto.verify(e.sparseTensor);if(o)return"sparseTensor."+o}if(e.tp!=null&&e.hasOwnProperty("tp")){var o=f.onnx.TypeProto.verify(e.tp);if(o)return"tp."+o}if(e.floats!=null&&e.hasOwnProperty("floats")){if(!Array.isArray(e.floats))return"floats: array expected";for(var i=0;i<e.floats.length;++i)if(typeof e.floats[i]!="number")return"floats: number[] expected"}if(e.ints!=null&&e.hasOwnProperty("ints")){if(!Array.isArray(e.ints))return"ints: array expected";for(var i=0;i<e.ints.length;++i)if(!h.isInteger(e.ints[i])&&!(e.ints[i]&&h.isInteger(e.ints[i].low)&&h.isInteger(e.ints[i].high)))return"ints: integer|Long[] expected"}if(e.strings!=null&&e.hasOwnProperty("strings")){if(!Array.isArray(e.strings))return"strings: array expected";for(var i=0;i<e.strings.length;++i)if(!(e.strings[i]&&typeof e.strings[i].length=="number"||h.isString(e.strings[i])))return"strings: buffer[] expected"}if(e.tensors!=null&&e.hasOwnProperty("tensors")){if(!Array.isArray(e.tensors))return"tensors: array expected";for(var i=0;i<e.tensors.length;++i){var o=f.onnx.TensorProto.verify(e.tensors[i]);if(o)return"tensors."+o}}if(e.graphs!=null&&e.hasOwnProperty("graphs")){if(!Array.isArray(e.graphs))return"graphs: array expected";for(var i=0;i<e.graphs.length;++i){var o=f.onnx.GraphProto.verify(e.graphs[i]);if(o)return"graphs."+o}}if(e.sparseTensors!=null&&e.hasOwnProperty("sparseTensors")){if(!Array.isArray(e.sparseTensors))return"sparseTensors: array expected";for(var i=0;i<e.sparseTensors.length;++i){var o=f.onnx.SparseTensorProto.verify(e.sparseTensors[i]);if(o)return"sparseTensors."+o}}if(e.typeProtos!=null&&e.hasOwnProperty("typeProtos")){if(!Array.isArray(e.typeProtos))return"typeProtos: array expected";for(var i=0;i<e.typeProtos.length;++i){var o=f.onnx.TypeProto.verify(e.typeProtos[i]);if(o)return"typeProtos."+o}}return null},t.fromObject=function(e){if(e instanceof f.onnx.AttributeProto)return e;var o=new f.onnx.AttributeProto;switch(e.name!=null&&(o.name=String(e.name)),e.refAttrName!=null&&(o.refAttrName=String(e.refAttrName)),e.docString!=null&&(o.docString=String(e.docString)),e.type){default:if(typeof e.type=="number"){o.type=e.type;break}break;case"UNDEFINED":case 0:o.type=0;break;case"FLOAT":case 1:o.type=1;break;case"INT":case 2:o.type=2;break;case"STRING":case 3:o.type=3;break;case"TENSOR":case 4:o.type=4;break;case"GRAPH":case 5:o.type=5;break;case"SPARSE_TENSOR":case 11:o.type=11;break;case"TYPE_PROTO":case 13:o.type=13;break;case"FLOATS":case 6:o.type=6;break;case"INTS":case 7:o.type=7;break;case"STRINGS":case 8:o.type=8;break;case"TENSORS":case 9:o.type=9;break;case"GRAPHS":case 10:o.type=10;break;case"SPARSE_TENSORS":case 12:o.type=12;break;case"TYPE_PROTOS":case 14:o.type=14;break}if(e.f!=null&&(o.f=Number(e.f)),e.i!=null&&(h.Long?(o.i=h.Long.fromValue(e.i)).unsigned=!1:typeof e.i=="string"?o.i=parseInt(e.i,10):typeof e.i=="number"?o.i=e.i:typeof e.i=="object"&&(o.i=new h.LongBits(e.i.low>>>0,e.i.high>>>0).toNumber())),e.s!=null&&(typeof e.s=="string"?h.base64.decode(e.s,o.s=h.newBuffer(h.base64.length(e.s)),0):e.s.length>=0&&(o.s=e.s)),e.t!=null){if(typeof e.t!="object")throw TypeError(".onnx.AttributeProto.t: object expected");o.t=f.onnx.TensorProto.fromObject(e.t)}if(e.g!=null){if(typeof e.g!="object")throw TypeError(".onnx.AttributeProto.g: object expected");o.g=f.onnx.GraphProto.fromObject(e.g)}if(e.sparseTensor!=null){if(typeof e.sparseTensor!="object")throw TypeError(".onnx.AttributeProto.sparseTensor: object expected");o.sparseTensor=f.onnx.SparseTensorProto.fromObject(e.sparseTensor)}if(e.tp!=null){if(typeof e.tp!="object")throw TypeError(".onnx.AttributeProto.tp: object expected");o.tp=f.onnx.TypeProto.fromObject(e.tp)}if(e.floats){if(!Array.isArray(e.floats))throw TypeError(".onnx.AttributeProto.floats: array expected");o.floats=[];for(var i=0;i<e.floats.length;++i)o.floats[i]=Number(e.floats[i])}if(e.ints){if(!Array.isArray(e.ints))throw TypeError(".onnx.AttributeProto.ints: array expected");o.ints=[];for(var i=0;i<e.ints.length;++i)h.Long?(o.ints[i]=h.Long.fromValue(e.ints[i])).unsigned=!1:typeof e.ints[i]=="string"?o.ints[i]=parseInt(e.ints[i],10):typeof e.ints[i]=="number"?o.ints[i]=e.ints[i]:typeof e.ints[i]=="object"&&(o.ints[i]=new h.LongBits(e.ints[i].low>>>0,e.ints[i].high>>>0).toNumber())}if(e.strings){if(!Array.isArray(e.strings))throw TypeError(".onnx.AttributeProto.strings: array expected");o.strings=[];for(var i=0;i<e.strings.length;++i)typeof e.strings[i]=="string"?h.base64.decode(e.strings[i],o.strings[i]=h.newBuffer(h.base64.length(e.strings[i])),0):e.strings[i].length>=0&&(o.strings[i]=e.strings[i])}if(e.tensors){if(!Array.isArray(e.tensors))throw TypeError(".onnx.AttributeProto.tensors: array expected");o.tensors=[];for(var i=0;i<e.tensors.length;++i){if(typeof e.tensors[i]!="object")throw TypeError(".onnx.AttributeProto.tensors: object expected");o.tensors[i]=f.onnx.TensorProto.fromObject(e.tensors[i])}}if(e.graphs){if(!Array.isArray(e.graphs))throw TypeError(".onnx.AttributeProto.graphs: array expected");o.graphs=[];for(var i=0;i<e.graphs.length;++i){if(typeof e.graphs[i]!="object")throw TypeError(".onnx.AttributeProto.graphs: object expected");o.graphs[i]=f.onnx.GraphProto.fromObject(e.graphs[i])}}if(e.sparseTensors){if(!Array.isArray(e.sparseTensors))throw TypeError(".onnx.AttributeProto.sparseTensors: array expected");o.sparseTensors=[];for(var i=0;i<e.sparseTensors.length;++i){if(typeof e.sparseTensors[i]!="object")throw TypeError(".onnx.AttributeProto.sparseTensors: object expected");o.sparseTensors[i]=f.onnx.SparseTensorProto.fromObject(e.sparseTensors[i])}}if(e.typeProtos){if(!Array.isArray(e.typeProtos))throw TypeError(".onnx.AttributeProto.typeProtos: array expected");o.typeProtos=[];for(var i=0;i<e.typeProtos.length;++i){if(typeof e.typeProtos[i]!="object")throw TypeError(".onnx.AttributeProto.typeProtos: object expected");o.typeProtos[i]=f.onnx.TypeProto.fromObject(e.typeProtos[i])}}return o},t.toObject=function(e,o){o||(o={});var i={};if((o.arrays||o.defaults)&&(i.floats=[],i.ints=[],i.strings=[],i.tensors=[],i.graphs=[],i.typeProtos=[],i.sparseTensors=[]),o.defaults){if(i.name="",i.f=0,h.Long){var a=new h.Long(0,0,!1);i.i=o.longs===String?a.toString():o.longs===Number?a.toNumber():a}else i.i=o.longs===String?"0":0;o.bytes===String?i.s="":(i.s=[],o.bytes!==Array&&(i.s=h.newBuffer(i.s))),i.t=null,i.g=null,i.docString="",i.tp=null,i.type=o.enums===String?"UNDEFINED":0,i.refAttrName="",i.sparseTensor=null}if(e.name!=null&&e.hasOwnProperty("name")&&(i.name=e.name),e.f!=null&&e.hasOwnProperty("f")&&(i.f=o.json&&!isFinite(e.f)?String(e.f):e.f),e.i!=null&&e.hasOwnProperty("i")&&(typeof e.i=="number"?i.i=o.longs===String?String(e.i):e.i:i.i=o.longs===String?h.Long.prototype.toString.call(e.i):o.longs===Number?new h.LongBits(e.i.low>>>0,e.i.high>>>0).toNumber():e.i),e.s!=null&&e.hasOwnProperty("s")&&(i.s=o.bytes===String?h.base64.encode(e.s,0,e.s.length):o.bytes===Array?Array.prototype.slice.call(e.s):e.s),e.t!=null&&e.hasOwnProperty("t")&&(i.t=f.onnx.TensorProto.toObject(e.t,o)),e.g!=null&&e.hasOwnProperty("g")&&(i.g=f.onnx.GraphProto.toObject(e.g,o)),e.floats&&e.floats.length){i.floats=[];for(var s=0;s<e.floats.length;++s)i.floats[s]=o.json&&!isFinite(e.floats[s])?String(e.floats[s]):e.floats[s]}if(e.ints&&e.ints.length){i.ints=[];for(var s=0;s<e.ints.length;++s)typeof e.ints[s]=="number"?i.ints[s]=o.longs===String?String(e.ints[s]):e.ints[s]:i.ints[s]=o.longs===String?h.Long.prototype.toString.call(e.ints[s]):o.longs===Number?new h.LongBits(e.ints[s].low>>>0,e.ints[s].high>>>0).toNumber():e.ints[s]}if(e.strings&&e.strings.length){i.strings=[];for(var s=0;s<e.strings.length;++s)i.strings[s]=o.bytes===String?h.base64.encode(e.strings[s],0,e.strings[s].length):o.bytes===Array?Array.prototype.slice.call(e.strings[s]):e.strings[s]}if(e.tensors&&e.tensors.length){i.tensors=[];for(var s=0;s<e.tensors.length;++s)i.tensors[s]=f.onnx.TensorProto.toObject(e.tensors[s],o)}if(e.graphs&&e.graphs.length){i.graphs=[];for(var s=0;s<e.graphs.length;++s)i.graphs[s]=f.onnx.GraphProto.toObject(e.graphs[s],o)}if(e.docString!=null&&e.hasOwnProperty("docString")&&(i.docString=e.docString),e.tp!=null&&e.hasOwnProperty("tp")&&(i.tp=f.onnx.TypeProto.toObject(e.tp,o)),e.typeProtos&&e.typeProtos.length){i.typeProtos=[];for(var s=0;s<e.typeProtos.length;++s)i.typeProtos[s]=f.onnx.TypeProto.toObject(e.typeProtos[s],o)}if(e.type!=null&&e.hasOwnProperty("type")&&(i.type=o.enums===String?f.onnx.AttributeProto.AttributeType[e.type]===void 0?e.type:f.onnx.AttributeProto.AttributeType[e.type]:e.type),e.refAttrName!=null&&e.hasOwnProperty("refAttrName")&&(i.refAttrName=e.refAttrName),e.sparseTensor!=null&&e.hasOwnProperty("sparseTensor")&&(i.sparseTensor=f.onnx.SparseTensorProto.toObject(e.sparseTensor,o)),e.sparseTensors&&e.sparseTensors.length){i.sparseTensors=[];for(var s=0;s<e.sparseTensors.length;++s)i.sparseTensors[s]=f.onnx.SparseTensorProto.toObject(e.sparseTensors[s],o)}return i},t.prototype.toJSON=function(){return this.constructor.toObject(this,j.util.toJSONOptions)},t.getTypeUrl=function(e){return e===void 0&&(e="type.googleapis.com"),e+"/onnx.AttributeProto"},t.AttributeType=function(){var n={},e=Object.create(n);return e[n[0]="UNDEFINED"]=0,e[n[1]="FLOAT"]=1,e[n[2]="INT"]=2,e[n[3]="STRING"]=3,e[n[4]="TENSOR"]=4,e[n[5]="GRAPH"]=5,e[n[11]="SPARSE_TENSOR"]=11,e[n[13]="TYPE_PROTO"]=13,e[n[6]="FLOATS"]=6,e[n[7]="INTS"]=7,e[n[8]="STRINGS"]=8,e[n[9]="TENSORS"]=9,e[n[10]="GRAPHS"]=10,e[n[12]="SPARSE_TENSORS"]=12,e[n[14]="TYPE_PROTOS"]=14,e}(),t}(),r.ValueInfoProto=function(){function t(n){if(n)for(var e=Object.keys(n),o=0;o<e.length;++o)n[e[o]]!=null&&(this[e[o]]=n[e[o]])}return t.prototype.name="",t.prototype.type=null,t.prototype.docString="",t.create=function(e){return new t(e)},t.encode=function(e,o){return o||(o=Z.create()),e.name!=null&&Object.hasOwnProperty.call(e,"name")&&o.uint32(10).string(e.name),e.type!=null&&Object.hasOwnProperty.call(e,"type")&&f.onnx.TypeProto.encode(e.type,o.uint32(18).fork()).ldelim(),e.docString!=null&&Object.hasOwnProperty.call(e,"docString")&&o.uint32(26).string(e.docString),o},t.encodeDelimited=function(e,o){return this.encode(e,o).ldelim()},t.decode=function(e,o){e instanceof _||(e=_.create(e));for(var i=o===void 0?e.len:e.pos+o,a=new f.onnx.ValueInfoProto;e.pos<i;){var s=e.uint32();switch(s>>>3){case 1:{a.name=e.string();break}case 2:{a.type=f.onnx.TypeProto.decode(e,e.uint32());break}case 3:{a.docString=e.string();break}default:e.skipType(s&7);break}}return a},t.decodeDelimited=function(e){return e instanceof _||(e=new _(e)),this.decode(e,e.uint32())},t.verify=function(e){if(typeof e!="object"||e===null)return"object expected";if(e.name!=null&&e.hasOwnProperty("name")&&!h.isString(e.name))return"name: string expected";if(e.type!=null&&e.hasOwnProperty("type")){var o=f.onnx.TypeProto.verify(e.type);if(o)return"type."+o}return e.docString!=null&&e.hasOwnProperty("docString")&&!h.isString(e.docString)?"docString: string expected":null},t.fromObject=function(e){if(e instanceof f.onnx.ValueInfoProto)return e;var o=new f.onnx.ValueInfoProto;if(e.name!=null&&(o.name=String(e.name)),e.type!=null){if(typeof e.type!="object")throw TypeError(".onnx.ValueInfoProto.type: object expected");o.type=f.onnx.TypeProto.fromObject(e.type)}return e.docString!=null&&(o.docString=String(e.docString)),o},t.toObject=function(e,o){o||(o={});var i={};return o.defaults&&(i.name="",i.type=null,i.docString=""),e.name!=null&&e.hasOwnProperty("name")&&(i.name=e.name),e.type!=null&&e.hasOwnProperty("type")&&(i.type=f.onnx.TypeProto.toObject(e.type,o)),e.docString!=null&&e.hasOwnProperty("docString")&&(i.docString=e.docString),i},t.prototype.toJSON=function(){return this.constructor.toObject(this,j.util.toJSONOptions)},t.getTypeUrl=function(e){return e===void 0&&(e="type.googleapis.com"),e+"/onnx.ValueInfoProto"},t}(),r.NodeProto=function(){function t(n){if(this.input=[],this.output=[],this.attribute=[],n)for(var e=Object.keys(n),o=0;o<e.length;++o)n[e[o]]!=null&&(this[e[o]]=n[e[o]])}return t.prototype.input=h.emptyArray,t.prototype.output=h.emptyArray,t.prototype.name="",t.prototype.opType="",t.prototype.domain="",t.prototype.attribute=h.emptyArray,t.prototype.docString="",t.create=function(e){return new t(e)},t.encode=function(e,o){if(o||(o=Z.create()),e.input!=null&&e.input.length)for(var i=0;i<e.input.length;++i)o.uint32(10).string(e.input[i]);if(e.output!=null&&e.output.length)for(var i=0;i<e.output.length;++i)o.uint32(18).string(e.output[i]);if(e.name!=null&&Object.hasOwnProperty.call(e,"name")&&o.uint32(26).string(e.name),e.opType!=null&&Object.hasOwnProperty.call(e,"opType")&&o.uint32(34).string(e.opType),e.attribute!=null&&e.attribute.length)for(var i=0;i<e.attribute.length;++i)f.onnx.AttributeProto.encode(e.attribute[i],o.uint32(42).fork()).ldelim();return e.docString!=null&&Object.hasOwnProperty.call(e,"docString")&&o.uint32(50).string(e.docString),e.domain!=null&&Object.hasOwnProperty.call(e,"domain")&&o.uint32(58).string(e.domain),o},t.encodeDelimited=function(e,o){return this.encode(e,o).ldelim()},t.decode=function(e,o){e instanceof _||(e=_.create(e));for(var i=o===void 0?e.len:e.pos+o,a=new f.onnx.NodeProto;e.pos<i;){var s=e.uint32();switch(s>>>3){case 1:{a.input&&a.input.length||(a.input=[]),a.input.push(e.string());break}case 2:{a.output&&a.output.length||(a.output=[]),a.output.push(e.string());break}case 3:{a.name=e.string();break}case 4:{a.opType=e.string();break}case 7:{a.domain=e.string();break}case 5:{a.attribute&&a.attribute.length||(a.attribute=[]),a.attribute.push(f.onnx.AttributeProto.decode(e,e.uint32()));break}case 6:{a.docString=e.string();break}default:e.skipType(s&7);break}}return a},t.decodeDelimited=function(e){return e instanceof _||(e=new _(e)),this.decode(e,e.uint32())},t.verify=function(e){if(typeof e!="object"||e===null)return"object expected";if(e.input!=null&&e.hasOwnProperty("input")){if(!Array.isArray(e.input))return"input: array expected";for(var o=0;o<e.input.length;++o)if(!h.isString(e.input[o]))return"input: string[] expected"}if(e.output!=null&&e.hasOwnProperty("output")){if(!Array.isArray(e.output))return"output: array expected";for(var o=0;o<e.output.length;++o)if(!h.isString(e.output[o]))return"output: string[] expected"}if(e.name!=null&&e.hasOwnProperty("name")&&!h.isString(e.name))return"name: string expected";if(e.opType!=null&&e.hasOwnProperty("opType")&&!h.isString(e.opType))return"opType: string expected";if(e.domain!=null&&e.hasOwnProperty("domain")&&!h.isString(e.domain))return"domain: string expected";if(e.attribute!=null&&e.hasOwnProperty("attribute")){if(!Array.isArray(e.attribute))return"attribute: array expected";for(var o=0;o<e.attribute.length;++o){var i=f.onnx.AttributeProto.verify(e.attribute[o]);if(i)return"attribute."+i}}return e.docString!=null&&e.hasOwnProperty("docString")&&!h.isString(e.docString)?"docString: string expected":null},t.fromObject=function(e){if(e instanceof f.onnx.NodeProto)return e;var o=new f.onnx.NodeProto;if(e.input){if(!Array.isArray(e.input))throw TypeError(".onnx.NodeProto.input: array expected");o.input=[];for(var i=0;i<e.input.length;++i)o.input[i]=String(e.input[i])}if(e.output){if(!Array.isArray(e.output))throw TypeError(".onnx.NodeProto.output: array expected");o.output=[];for(var i=0;i<e.output.length;++i)o.output[i]=String(e.output[i])}if(e.name!=null&&(o.name=String(e.name)),e.opType!=null&&(o.opType=String(e.opType)),e.domain!=null&&(o.domain=String(e.domain)),e.attribute){if(!Array.isArray(e.attribute))throw TypeError(".onnx.NodeProto.attribute: array expected");o.attribute=[];for(var i=0;i<e.attribute.length;++i){if(typeof e.attribute[i]!="object")throw TypeError(".onnx.NodeProto.attribute: object expected");o.attribute[i]=f.onnx.AttributeProto.fromObject(e.attribute[i])}}return e.docString!=null&&(o.docString=String(e.docString)),o},t.toObject=function(e,o){o||(o={});var i={};if((o.arrays||o.defaults)&&(i.input=[],i.output=[],i.attribute=[]),o.defaults&&(i.name="",i.opType="",i.docString="",i.domain=""),e.input&&e.input.length){i.input=[];for(var a=0;a<e.input.length;++a)i.input[a]=e.input[a]}if(e.output&&e.output.length){i.output=[];for(var a=0;a<e.output.length;++a)i.output[a]=e.output[a]}if(e.name!=null&&e.hasOwnProperty("name")&&(i.name=e.name),e.opType!=null&&e.hasOwnProperty("opType")&&(i.opType=e.opType),e.attribute&&e.attribute.length){i.attribute=[];for(var a=0;a<e.attribute.length;++a)i.attribute[a]=f.onnx.AttributeProto.toObject(e.attribute[a],o)}return e.docString!=null&&e.hasOwnProperty("docString")&&(i.docString=e.docString),e.domain!=null&&e.hasOwnProperty("domain")&&(i.domain=e.domain),i},t.prototype.toJSON=function(){return this.constructor.toObject(this,j.util.toJSONOptions)},t.getTypeUrl=function(e){return e===void 0&&(e="type.googleapis.com"),e+"/onnx.NodeProto"},t}(),r.TrainingInfoProto=function(){function t(n){if(this.initializationBinding=[],this.updateBinding=[],n)for(var e=Object.keys(n),o=0;o<e.length;++o)n[e[o]]!=null&&(this[e[o]]=n[e[o]])}return t.prototype.initialization=null,t.prototype.algorithm=null,t.prototype.initializationBinding=h.emptyArray,t.prototype.updateBinding=h.emptyArray,t.create=function(e){return new t(e)},t.encode=function(e,o){if(o||(o=Z.create()),e.initialization!=null&&Object.hasOwnProperty.call(e,"initialization")&&f.onnx.GraphProto.encode(e.initialization,o.uint32(10).fork()).ldelim(),e.algorithm!=null&&Object.hasOwnProperty.call(e,"algorithm")&&f.onnx.GraphProto.encode(e.algorithm,o.uint32(18).fork()).ldelim(),e.initializationBinding!=null&&e.initializationBinding.length)for(var i=0;i<e.initializationBinding.length;++i)f.onnx.StringStringEntryProto.encode(e.initializationBinding[i],o.uint32(26).fork()).ldelim();if(e.updateBinding!=null&&e.updateBinding.length)for(var i=0;i<e.updateBinding.length;++i)f.onnx.StringStringEntryProto.encode(e.updateBinding[i],o.uint32(34).fork()).ldelim();return o},t.encodeDelimited=function(e,o){return this.encode(e,o).ldelim()},t.decode=function(e,o){e instanceof _||(e=_.create(e));for(var i=o===void 0?e.len:e.pos+o,a=new f.onnx.TrainingInfoProto;e.pos<i;){var s=e.uint32();switch(s>>>3){case 1:{a.initialization=f.onnx.GraphProto.decode(e,e.uint32());break}case 2:{a.algorithm=f.onnx.GraphProto.decode(e,e.uint32());break}case 3:{a.initializationBinding&&a.initializationBinding.length||(a.initializationBinding=[]),a.initializationBinding.push(f.onnx.StringStringEntryProto.decode(e,e.uint32()));break}case 4:{a.updateBinding&&a.updateBinding.length||(a.updateBinding=[]),a.updateBinding.push(f.onnx.StringStringEntryProto.decode(e,e.uint32()));break}default:e.skipType(s&7);break}}return a},t.decodeDelimited=function(e){return e instanceof _||(e=new _(e)),this.decode(e,e.uint32())},t.verify=function(e){if(typeof e!="object"||e===null)return"object expected";if(e.initialization!=null&&e.hasOwnProperty("initialization")){var o=f.onnx.GraphProto.verify(e.initialization);if(o)return"initialization."+o}if(e.algorithm!=null&&e.hasOwnProperty("algorithm")){var o=f.onnx.GraphProto.verify(e.algorithm);if(o)return"algorithm."+o}if(e.initializationBinding!=null&&e.hasOwnProperty("initializationBinding")){if(!Array.isArray(e.initializationBinding))return"initializationBinding: array expected";for(var i=0;i<e.initializationBinding.length;++i){var o=f.onnx.StringStringEntryProto.verify(e.initializationBinding[i]);if(o)return"initializationBinding."+o}}if(e.updateBinding!=null&&e.hasOwnProperty("updateBinding")){if(!Array.isArray(e.updateBinding))return"updateBinding: array expected";for(var i=0;i<e.updateBinding.length;++i){var o=f.onnx.StringStringEntryProto.verify(e.updateBinding[i]);if(o)return"updateBinding."+o}}return null},t.fromObject=function(e){if(e instanceof f.onnx.TrainingInfoProto)return e;var o=new f.onnx.TrainingInfoProto;if(e.initialization!=null){if(typeof e.initialization!="object")throw TypeError(".onnx.TrainingInfoProto.initialization: object expected");o.initialization=f.onnx.GraphProto.fromObject(e.initialization)}if(e.algorithm!=null){if(typeof e.algorithm!="object")throw TypeError(".onnx.TrainingInfoProto.algorithm: object expected");o.algorithm=f.onnx.GraphProto.fromObject(e.algorithm)}if(e.initializationBinding){if(!Array.isArray(e.initializationBinding))throw TypeError(".onnx.TrainingInfoProto.initializationBinding: array expected");o.initializationBinding=[];for(var i=0;i<e.initializationBinding.length;++i){if(typeof e.initializationBinding[i]!="object")throw TypeError(".onnx.TrainingInfoProto.initializationBinding: object expected");o.initializationBinding[i]=f.onnx.StringStringEntryProto.fromObject(e.initializationBinding[i])}}if(e.updateBinding){if(!Array.isArray(e.updateBinding))throw TypeError(".onnx.TrainingInfoProto.updateBinding: array expected");o.updateBinding=[];for(var i=0;i<e.updateBinding.length;++i){if(typeof e.updateBinding[i]!="object")throw TypeError(".onnx.TrainingInfoProto.updateBinding: object expected");o.updateBinding[i]=f.onnx.StringStringEntryProto.fromObject(e.updateBinding[i])}}return o},t.toObject=function(e,o){o||(o={});var i={};if((o.arrays||o.defaults)&&(i.initializationBinding=[],i.updateBinding=[]),o.defaults&&(i.initialization=null,i.algorithm=null),e.initialization!=null&&e.hasOwnProperty("initialization")&&(i.initialization=f.onnx.GraphProto.toObject(e.initialization,o)),e.algorithm!=null&&e.hasOwnProperty("algorithm")&&(i.algorithm=f.onnx.GraphProto.toObject(e.algorithm,o)),e.initializationBinding&&e.initializationBinding.length){i.initializationBinding=[];for(var a=0;a<e.initializationBinding.length;++a)i.initializationBinding[a]=f.onnx.StringStringEntryProto.toObject(e.initializationBinding[a],o)}if(e.updateBinding&&e.updateBinding.length){i.updateBinding=[];for(var a=0;a<e.updateBinding.length;++a)i.updateBinding[a]=f.onnx.StringStringEntryProto.toObject(e.updateBinding[a],o)}return i},t.prototype.toJSON=function(){return this.constructor.toObject(this,j.util.toJSONOptions)},t.getTypeUrl=function(e){return e===void 0&&(e="type.googleapis.com"),e+"/onnx.TrainingInfoProto"},t}(),r.ModelProto=function(){function t(n){if(this.opsetImport=[],this.metadataProps=[],this.trainingInfo=[],this.functions=[],n)for(var e=Object.keys(n),o=0;o<e.length;++o)n[e[o]]!=null&&(this[e[o]]=n[e[o]])}return t.prototype.irVersion=h.Long?h.Long.fromBits(0,0,!1):0,t.prototype.opsetImport=h.emptyArray,t.prototype.producerName="",t.prototype.producerVersion="",t.prototype.domain="",t.prototype.modelVersion=h.Long?h.Long.fromBits(0,0,!1):0,t.prototype.docString="",t.prototype.graph=null,t.prototype.metadataProps=h.emptyArray,t.prototype.trainingInfo=h.emptyArray,t.prototype.functions=h.emptyArray,t.create=function(e){return new t(e)},t.encode=function(e,o){if(o||(o=Z.create()),e.irVersion!=null&&Object.hasOwnProperty.call(e,"irVersion")&&o.uint32(8).int64(e.irVersion),e.producerName!=null&&Object.hasOwnProperty.call(e,"producerName")&&o.uint32(18).string(e.producerName),e.producerVersion!=null&&Object.hasOwnProperty.call(e,"producerVersion")&&o.uint32(26).string(e.producerVersion),e.domain!=null&&Object.hasOwnProperty.call(e,"domain")&&o.uint32(34).string(e.domain),e.modelVersion!=null&&Object.hasOwnProperty.call(e,"modelVersion")&&o.uint32(40).int64(e.modelVersion),e.docString!=null&&Object.hasOwnProperty.call(e,"docString")&&o.uint32(50).string(e.docString),e.graph!=null&&Object.hasOwnProperty.call(e,"graph")&&f.onnx.GraphProto.encode(e.graph,o.uint32(58).fork()).ldelim(),e.opsetImport!=null&&e.opsetImport.length)for(var i=0;i<e.opsetImport.length;++i)f.onnx.OperatorSetIdProto.encode(e.opsetImport[i],o.uint32(66).fork()).ldelim();if(e.metadataProps!=null&&e.metadataProps.length)for(var i=0;i<e.metadataProps.length;++i)f.onnx.StringStringEntryProto.encode(e.metadataProps[i],o.uint32(114).fork()).ldelim();if(e.trainingInfo!=null&&e.trainingInfo.length)for(var i=0;i<e.trainingInfo.length;++i)f.onnx.TrainingInfoProto.encode(e.trainingInfo[i],o.uint32(162).fork()).ldelim();if(e.functions!=null&&e.functions.length)for(var i=0;i<e.functions.length;++i)f.onnx.FunctionProto.encode(e.functions[i],o.uint32(202).fork()).ldelim();return o},t.encodeDelimited=function(e,o){return this.encode(e,o).ldelim()},t.decode=function(e,o){e instanceof _||(e=_.create(e));for(var i=o===void 0?e.len:e.pos+o,a=new f.onnx.ModelProto;e.pos<i;){var s=e.uint32();switch(s>>>3){case 1:{a.irVersion=e.int64();break}case 8:{a.opsetImport&&a.opsetImport.length||(a.opsetImport=[]),a.opsetImport.push(f.onnx.OperatorSetIdProto.decode(e,e.uint32()));break}case 2:{a.producerName=e.string();break}case 3:{a.producerVersion=e.string();break}case 4:{a.domain=e.string();break}case 5:{a.modelVersion=e.int64();break}case 6:{a.docString=e.string();break}case 7:{a.graph=f.onnx.GraphProto.decode(e,e.uint32());break}case 14:{a.metadataProps&&a.metadataProps.length||(a.metadataProps=[]),a.metadataProps.push(f.onnx.StringStringEntryProto.decode(e,e.uint32()));break}case 20:{a.trainingInfo&&a.trainingInfo.length||(a.trainingInfo=[]),a.trainingInfo.push(f.onnx.TrainingInfoProto.decode(e,e.uint32()));break}case 25:{a.functions&&a.functions.length||(a.functions=[]),a.functions.push(f.onnx.FunctionProto.decode(e,e.uint32()));break}default:e.skipType(s&7);break}}return a},t.decodeDelimited=function(e){return e instanceof _||(e=new _(e)),this.decode(e,e.uint32())},t.verify=function(e){if(typeof e!="object"||e===null)return"object expected";if(e.irVersion!=null&&e.hasOwnProperty("irVersion")&&!h.isInteger(e.irVersion)&&!(e.irVersion&&h.isInteger(e.irVersion.low)&&h.isInteger(e.irVersion.high)))return"irVersion: integer|Long expected";if(e.opsetImport!=null&&e.hasOwnProperty("opsetImport")){if(!Array.isArray(e.opsetImport))return"opsetImport: array expected";for(var o=0;o<e.opsetImport.length;++o){var i=f.onnx.OperatorSetIdProto.verify(e.opsetImport[o]);if(i)return"opsetImport."+i}}if(e.producerName!=null&&e.hasOwnProperty("producerName")&&!h.isString(e.producerName))return"producerName: string expected";if(e.producerVersion!=null&&e.hasOwnProperty("producerVersion")&&!h.isString(e.producerVersion))return"producerVersion: string expected";if(e.domain!=null&&e.hasOwnProperty("domain")&&!h.isString(e.domain))return"domain: string expected";if(e.modelVersion!=null&&e.hasOwnProperty("modelVersion")&&!h.isInteger(e.modelVersion)&&!(e.modelVersion&&h.isInteger(e.modelVersion.low)&&h.isInteger(e.modelVersion.high)))return"modelVersion: integer|Long expected";if(e.docString!=null&&e.hasOwnProperty("docString")&&!h.isString(e.docString))return"docString: string expected";if(e.graph!=null&&e.hasOwnProperty("graph")){var i=f.onnx.GraphProto.verify(e.graph);if(i)return"graph."+i}if(e.metadataProps!=null&&e.hasOwnProperty("metadataProps")){if(!Array.isArray(e.metadataProps))return"metadataProps: array expected";for(var o=0;o<e.metadataProps.length;++o){var i=f.onnx.StringStringEntryProto.verify(e.metadataProps[o]);if(i)return"metadataProps."+i}}if(e.trainingInfo!=null&&e.hasOwnProperty("trainingInfo")){if(!Array.isArray(e.trainingInfo))return"trainingInfo: array expected";for(var o=0;o<e.trainingInfo.length;++o){var i=f.onnx.TrainingInfoProto.verify(e.trainingInfo[o]);if(i)return"trainingInfo."+i}}if(e.functions!=null&&e.hasOwnProperty("functions")){if(!Array.isArray(e.functions))return"functions: array expected";for(var o=0;o<e.functions.length;++o){var i=f.onnx.FunctionProto.verify(e.functions[o]);if(i)return"functions."+i}}return null},t.fromObject=function(e){if(e instanceof f.onnx.ModelProto)return e;var o=new f.onnx.ModelProto;if(e.irVersion!=null&&(h.Long?(o.irVersion=h.Long.fromValue(e.irVersion)).unsigned=!1:typeof e.irVersion=="string"?o.irVersion=parseInt(e.irVersion,10):typeof e.irVersion=="number"?o.irVersion=e.irVersion:typeof e.irVersion=="object"&&(o.irVersion=new h.LongBits(e.irVersion.low>>>0,e.irVersion.high>>>0).toNumber())),e.opsetImport){if(!Array.isArray(e.opsetImport))throw TypeError(".onnx.ModelProto.opsetImport: array expected");o.opsetImport=[];for(var i=0;i<e.opsetImport.length;++i){if(typeof e.opsetImport[i]!="object")throw TypeError(".onnx.ModelProto.opsetImport: object expected");o.opsetImport[i]=f.onnx.OperatorSetIdProto.fromObject(e.opsetImport[i])}}if(e.producerName!=null&&(o.producerName=String(e.producerName)),e.producerVersion!=null&&(o.producerVersion=String(e.producerVersion)),e.domain!=null&&(o.domain=String(e.domain)),e.modelVersion!=null&&(h.Long?(o.modelVersion=h.Long.fromValue(e.modelVersion)).unsigned=!1:typeof e.modelVersion=="string"?o.modelVersion=parseInt(e.modelVersion,10):typeof e.modelVersion=="number"?o.modelVersion=e.modelVersion:typeof e.modelVersion=="object"&&(o.modelVersion=new h.LongBits(e.modelVersion.low>>>0,e.modelVersion.high>>>0).toNumber())),e.docString!=null&&(o.docString=String(e.docString)),e.graph!=null){if(typeof e.graph!="object")throw TypeError(".onnx.ModelProto.graph: object expected");o.graph=f.onnx.GraphProto.fromObject(e.graph)}if(e.metadataProps){if(!Array.isArray(e.metadataProps))throw TypeError(".onnx.ModelProto.metadataProps: array expected");o.metadataProps=[];for(var i=0;i<e.metadataProps.length;++i){if(typeof e.metadataProps[i]!="object")throw TypeError(".onnx.ModelProto.metadataProps: object expected");o.metadataProps[i]=f.onnx.StringStringEntryProto.fromObject(e.metadataProps[i])}}if(e.trainingInfo){if(!Array.isArray(e.trainingInfo))throw TypeError(".onnx.ModelProto.trainingInfo: array expected");o.trainingInfo=[];for(var i=0;i<e.trainingInfo.length;++i){if(typeof e.trainingInfo[i]!="object")throw TypeError(".onnx.ModelProto.trainingInfo: object expected");o.trainingInfo[i]=f.onnx.TrainingInfoProto.fromObject(e.trainingInfo[i])}}if(e.functions){if(!Array.isArray(e.functions))throw TypeError(".onnx.ModelProto.functions: array expected");o.functions=[];for(var i=0;i<e.functions.length;++i){if(typeof e.functions[i]!="object")throw TypeError(".onnx.ModelProto.functions: object expected");o.functions[i]=f.onnx.FunctionProto.fromObject(e.functions[i])}}return o},t.toObject=function(e,o){o||(o={});var i={};if((o.arrays||o.defaults)&&(i.opsetImport=[],i.metadataProps=[],i.trainingInfo=[],i.functions=[]),o.defaults){if(h.Long){var a=new h.Long(0,0,!1);i.irVersion=o.longs===String?a.toString():o.longs===Number?a.toNumber():a}else i.irVersion=o.longs===String?"0":0;if(i.producerName="",i.producerVersion="",i.domain="",h.Long){var a=new h.Long(0,0,!1);i.modelVersion=o.longs===String?a.toString():o.longs===Number?a.toNumber():a}else i.modelVersion=o.longs===String?"0":0;i.docString="",i.graph=null}if(e.irVersion!=null&&e.hasOwnProperty("irVersion")&&(typeof e.irVersion=="number"?i.irVersion=o.longs===String?String(e.irVersion):e.irVersion:i.irVersion=o.longs===String?h.Long.prototype.toString.call(e.irVersion):o.longs===Number?new h.LongBits(e.irVersion.low>>>0,e.irVersion.high>>>0).toNumber():e.irVersion),e.producerName!=null&&e.hasOwnProperty("producerName")&&(i.producerName=e.producerName),e.producerVersion!=null&&e.hasOwnProperty("producerVersion")&&(i.producerVersion=e.producerVersion),e.domain!=null&&e.hasOwnProperty("domain")&&(i.domain=e.domain),e.modelVersion!=null&&e.hasOwnProperty("modelVersion")&&(typeof e.modelVersion=="number"?i.modelVersion=o.longs===String?String(e.modelVersion):e.modelVersion:i.modelVersion=o.longs===String?h.Long.prototype.toString.call(e.modelVersion):o.longs===Number?new h.LongBits(e.modelVersion.low>>>0,e.modelVersion.high>>>0).toNumber():e.modelVersion),e.docString!=null&&e.hasOwnProperty("docString")&&(i.docString=e.docString),e.graph!=null&&e.hasOwnProperty("graph")&&(i.graph=f.onnx.GraphProto.toObject(e.graph,o)),e.opsetImport&&e.opsetImport.length){i.opsetImport=[];for(var s=0;s<e.opsetImport.length;++s)i.opsetImport[s]=f.onnx.OperatorSetIdProto.toObject(e.opsetImport[s],o)}if(e.metadataProps&&e.metadataProps.length){i.metadataProps=[];for(var s=0;s<e.metadataProps.length;++s)i.metadataProps[s]=f.onnx.StringStringEntryProto.toObject(e.metadataProps[s],o)}if(e.trainingInfo&&e.trainingInfo.length){i.trainingInfo=[];for(var s=0;s<e.trainingInfo.length;++s)i.trainingInfo[s]=f.onnx.TrainingInfoProto.toObject(e.trainingInfo[s],o)}if(e.functions&&e.functions.length){i.functions=[];for(var s=0;s<e.functions.length;++s)i.functions[s]=f.onnx.FunctionProto.toObject(e.functions[s],o)}return i},t.prototype.toJSON=function(){return this.constructor.toObject(this,j.util.toJSONOptions)},t.getTypeUrl=function(e){return e===void 0&&(e="type.googleapis.com"),e+"/onnx.ModelProto"},t}(),r.StringStringEntryProto=function(){function t(n){if(n)for(var e=Object.keys(n),o=0;o<e.length;++o)n[e[o]]!=null&&(this[e[o]]=n[e[o]])}return t.prototype.key="",t.prototype.value="",t.create=function(e){return new t(e)},t.encode=function(e,o){return o||(o=Z.create()),e.key!=null&&Object.hasOwnProperty.call(e,"key")&&o.uint32(10).string(e.key),e.value!=null&&Object.hasOwnProperty.call(e,"value")&&o.uint32(18).string(e.value),o},t.encodeDelimited=function(e,o){return this.encode(e,o).ldelim()},t.decode=function(e,o){e instanceof _||(e=_.create(e));for(var i=o===void 0?e.len:e.pos+o,a=new f.onnx.StringStringEntryProto;e.pos<i;){var s=e.uint32();switch(s>>>3){case 1:{a.key=e.string();break}case 2:{a.value=e.string();break}default:e.skipType(s&7);break}}return a},t.decodeDelimited=function(e){return e instanceof _||(e=new _(e)),this.decode(e,e.uint32())},t.verify=function(e){return typeof e!="object"||e===null?"object expected":e.key!=null&&e.hasOwnProperty("key")&&!h.isString(e.key)?"key: string expected":e.value!=null&&e.hasOwnProperty("value")&&!h.isString(e.value)?"value: string expected":null},t.fromObject=function(e){if(e instanceof f.onnx.StringStringEntryProto)return e;var o=new f.onnx.StringStringEntryProto;return e.key!=null&&(o.key=String(e.key)),e.value!=null&&(o.value=String(e.value)),o},t.toObject=function(e,o){o||(o={});var i={};return o.defaults&&(i.key="",i.value=""),e.key!=null&&e.hasOwnProperty("key")&&(i.key=e.key),e.value!=null&&e.hasOwnProperty("value")&&(i.value=e.value),i},t.prototype.toJSON=function(){return this.constructor.toObject(this,j.util.toJSONOptions)},t.getTypeUrl=function(e){return e===void 0&&(e="type.googleapis.com"),e+"/onnx.StringStringEntryProto"},t}(),r.TensorAnnotation=function(){function t(n){if(this.quantParameterTensorNames=[],n)for(var e=Object.keys(n),o=0;o<e.length;++o)n[e[o]]!=null&&(this[e[o]]=n[e[o]])}return t.prototype.tensorName="",t.prototype.quantParameterTensorNames=h.emptyArray,t.create=function(e){return new t(e)},t.encode=function(e,o){if(o||(o=Z.create()),e.tensorName!=null&&Object.hasOwnProperty.call(e,"tensorName")&&o.uint32(10).string(e.tensorName),e.quantParameterTensorNames!=null&&e.quantParameterTensorNames.length)for(var i=0;i<e.quantParameterTensorNames.length;++i)f.onnx.StringStringEntryProto.encode(e.quantParameterTensorNames[i],o.uint32(18).fork()).ldelim();return o},t.encodeDelimited=function(e,o){return this.encode(e,o).ldelim()},t.decode=function(e,o){e instanceof _||(e=_.create(e));for(var i=o===void 0?e.len:e.pos+o,a=new f.onnx.TensorAnnotation;e.pos<i;){var s=e.uint32();switch(s>>>3){case 1:{a.tensorName=e.string();break}case 2:{a.quantParameterTensorNames&&a.quantParameterTensorNames.length||(a.quantParameterTensorNames=[]),a.quantParameterTensorNames.push(f.onnx.StringStringEntryProto.decode(e,e.uint32()));break}default:e.skipType(s&7);break}}return a},t.decodeDelimited=function(e){return e instanceof _||(e=new _(e)),this.decode(e,e.uint32())},t.verify=function(e){if(typeof e!="object"||e===null)return"object expected";if(e.tensorName!=null&&e.hasOwnProperty("tensorName")&&!h.isString(e.tensorName))return"tensorName: string expected";if(e.quantParameterTensorNames!=null&&e.hasOwnProperty("quantParameterTensorNames")){if(!Array.isArray(e.quantParameterTensorNames))return"quantParameterTensorNames: array expected";for(var o=0;o<e.quantParameterTensorNames.length;++o){var i=f.onnx.StringStringEntryProto.verify(e.quantParameterTensorNames[o]);if(i)return"quantParameterTensorNames."+i}}return null},t.fromObject=function(e){if(e instanceof f.onnx.TensorAnnotation)return e;var o=new f.onnx.TensorAnnotation;if(e.tensorName!=null&&(o.tensorName=String(e.tensorName)),e.quantParameterTensorNames){if(!Array.isArray(e.quantParameterTensorNames))throw TypeError(".onnx.TensorAnnotation.quantParameterTensorNames: array expected");o.quantParameterTensorNames=[];for(var i=0;i<e.quantParameterTensorNames.length;++i){if(typeof e.quantParameterTensorNames[i]!="object")throw TypeError(".onnx.TensorAnnotation.quantParameterTensorNames: object expected");o.quantParameterTensorNames[i]=f.onnx.StringStringEntryProto.fromObject(e.quantParameterTensorNames[i])}}return o},t.toObject=function(e,o){o||(o={});var i={};if((o.arrays||o.defaults)&&(i.quantParameterTensorNames=[]),o.defaults&&(i.tensorName=""),e.tensorName!=null&&e.hasOwnProperty("tensorName")&&(i.tensorName=e.tensorName),e.quantParameterTensorNames&&e.quantParameterTensorNames.length){i.quantParameterTensorNames=[];for(var a=0;a<e.quantParameterTensorNames.length;++a)i.quantParameterTensorNames[a]=f.onnx.StringStringEntryProto.toObject(e.quantParameterTensorNames[a],o)}return i},t.prototype.toJSON=function(){return this.constructor.toObject(this,j.util.toJSONOptions)},t.getTypeUrl=function(e){return e===void 0&&(e="type.googleapis.com"),e+"/onnx.TensorAnnotation"},t}(),r.GraphProto=function(){function t(n){if(this.node=[],this.initializer=[],this.sparseInitializer=[],this.input=[],this.output=[],this.valueInfo=[],this.quantizationAnnotation=[],n)for(var e=Object.keys(n),o=0;o<e.length;++o)n[e[o]]!=null&&(this[e[o]]=n[e[o]])}return t.prototype.node=h.emptyArray,t.prototype.name="",t.prototype.initializer=h.emptyArray,t.prototype.sparseInitializer=h.emptyArray,t.prototype.docString="",t.prototype.input=h.emptyArray,t.prototype.output=h.emptyArray,t.prototype.valueInfo=h.emptyArray,t.prototype.quantizationAnnotation=h.emptyArray,t.create=function(e){return new t(e)},t.encode=function(e,o){if(o||(o=Z.create()),e.node!=null&&e.node.length)for(var i=0;i<e.node.length;++i)f.onnx.NodeProto.encode(e.node[i],o.uint32(10).fork()).ldelim();if(e.name!=null&&Object.hasOwnProperty.call(e,"name")&&o.uint32(18).string(e.name),e.initializer!=null&&e.initializer.length)for(var i=0;i<e.initializer.length;++i)f.onnx.TensorProto.encode(e.initializer[i],o.uint32(42).fork()).ldelim();if(e.docString!=null&&Object.hasOwnProperty.call(e,"docString")&&o.uint32(82).string(e.docString),e.input!=null&&e.input.length)for(var i=0;i<e.input.length;++i)f.onnx.ValueInfoProto.encode(e.input[i],o.uint32(90).fork()).ldelim();if(e.output!=null&&e.output.length)for(var i=0;i<e.output.length;++i)f.onnx.ValueInfoProto.encode(e.output[i],o.uint32(98).fork()).ldelim();if(e.valueInfo!=null&&e.valueInfo.length)for(var i=0;i<e.valueInfo.length;++i)f.onnx.ValueInfoProto.encode(e.valueInfo[i],o.uint32(106).fork()).ldelim();if(e.quantizationAnnotation!=null&&e.quantizationAnnotation.length)for(var i=0;i<e.quantizationAnnotation.length;++i)f.onnx.TensorAnnotation.encode(e.quantizationAnnotation[i],o.uint32(114).fork()).ldelim();if(e.sparseInitializer!=null&&e.sparseInitializer.length)for(var i=0;i<e.sparseInitializer.length;++i)f.onnx.SparseTensorProto.encode(e.sparseInitializer[i],o.uint32(122).fork()).ldelim();return o},t.encodeDelimited=function(e,o){return this.encode(e,o).ldelim()},t.decode=function(e,o){e instanceof _||(e=_.create(e));for(var i=o===void 0?e.len:e.pos+o,a=new f.onnx.GraphProto;e.pos<i;){var s=e.uint32();switch(s>>>3){case 1:{a.node&&a.node.length||(a.node=[]),a.node.push(f.onnx.NodeProto.decode(e,e.uint32()));break}case 2:{a.name=e.string();break}case 5:{a.initializer&&a.initializer.length||(a.initializer=[]),a.initializer.push(f.onnx.TensorProto.decode(e,e.uint32()));break}case 15:{a.sparseInitializer&&a.sparseInitializer.length||(a.sparseInitializer=[]),a.sparseInitializer.push(f.onnx.SparseTensorProto.decode(e,e.uint32()));break}case 10:{a.docString=e.string();break}case 11:{a.input&&a.input.length||(a.input=[]),a.input.push(f.onnx.ValueInfoProto.decode(e,e.uint32()));break}case 12:{a.output&&a.output.length||(a.output=[]),a.output.push(f.onnx.ValueInfoProto.decode(e,e.uint32()));break}case 13:{a.valueInfo&&a.valueInfo.length||(a.valueInfo=[]),a.valueInfo.push(f.onnx.ValueInfoProto.decode(e,e.uint32()));break}case 14:{a.quantizationAnnotation&&a.quantizationAnnotation.length||(a.quantizationAnnotation=[]),a.quantizationAnnotation.push(f.onnx.TensorAnnotation.decode(e,e.uint32()));break}default:e.skipType(s&7);break}}return a},t.decodeDelimited=function(e){return e instanceof _||(e=new _(e)),this.decode(e,e.uint32())},t.verify=function(e){if(typeof e!="object"||e===null)return"object expected";if(e.node!=null&&e.hasOwnProperty("node")){if(!Array.isArray(e.node))return"node: array expected";for(var o=0;o<e.node.length;++o){var i=f.onnx.NodeProto.verify(e.node[o]);if(i)return"node."+i}}if(e.name!=null&&e.hasOwnProperty("name")&&!h.isString(e.name))return"name: string expected";if(e.initializer!=null&&e.hasOwnProperty("initializer")){if(!Array.isArray(e.initializer))return"initializer: array expected";for(var o=0;o<e.initializer.length;++o){var i=f.onnx.TensorProto.verify(e.initializer[o]);if(i)return"initializer."+i}}if(e.sparseInitializer!=null&&e.hasOwnProperty("sparseInitializer")){if(!Array.isArray(e.sparseInitializer))return"sparseInitializer: array expected";for(var o=0;o<e.sparseInitializer.length;++o){var i=f.onnx.SparseTensorProto.verify(e.sparseInitializer[o]);if(i)return"sparseInitializer."+i}}if(e.docString!=null&&e.hasOwnProperty("docString")&&!h.isString(e.docString))return"docString: string expected";if(e.input!=null&&e.hasOwnProperty("input")){if(!Array.isArray(e.input))return"input: array expected";for(var o=0;o<e.input.length;++o){var i=f.onnx.ValueInfoProto.verify(e.input[o]);if(i)return"input."+i}}if(e.output!=null&&e.hasOwnProperty("output")){if(!Array.isArray(e.output))return"output: array expected";for(var o=0;o<e.output.length;++o){var i=f.onnx.ValueInfoProto.verify(e.output[o]);if(i)return"output."+i}}if(e.valueInfo!=null&&e.hasOwnProperty("valueInfo")){if(!Array.isArray(e.valueInfo))return"valueInfo: array expected";for(var o=0;o<e.valueInfo.length;++o){var i=f.onnx.ValueInfoProto.verify(e.valueInfo[o]);if(i)return"valueInfo."+i}}if(e.quantizationAnnotation!=null&&e.hasOwnProperty("quantizationAnnotation")){if(!Array.isArray(e.quantizationAnnotation))return"quantizationAnnotation: array expected";for(var o=0;o<e.quantizationAnnotation.length;++o){var i=f.onnx.TensorAnnotation.verify(e.quantizationAnnotation[o]);if(i)return"quantizationAnnotation."+i}}return null},t.fromObject=function(e){if(e instanceof f.onnx.GraphProto)return e;var o=new f.onnx.GraphProto;if(e.node){if(!Array.isArray(e.node))throw TypeError(".onnx.GraphProto.node: array expected");o.node=[];for(var i=0;i<e.node.length;++i){if(typeof e.node[i]!="object")throw TypeError(".onnx.GraphProto.node: object expected");o.node[i]=f.onnx.NodeProto.fromObject(e.node[i])}}if(e.name!=null&&(o.name=String(e.name)),e.initializer){if(!Array.isArray(e.initializer))throw TypeError(".onnx.GraphProto.initializer: array expected");o.initializer=[];for(var i=0;i<e.initializer.length;++i){if(typeof e.initializer[i]!="object")throw TypeError(".onnx.GraphProto.initializer: object expected");o.initializer[i]=f.onnx.TensorProto.fromObject(e.initializer[i])}}if(e.sparseInitializer){if(!Array.isArray(e.sparseInitializer))throw TypeError(".onnx.GraphProto.sparseInitializer: array expected");o.sparseInitializer=[];for(var i=0;i<e.sparseInitializer.length;++i){if(typeof e.sparseInitializer[i]!="object")throw TypeError(".onnx.GraphProto.sparseInitializer: object expected");o.sparseInitializer[i]=f.onnx.SparseTensorProto.fromObject(e.sparseInitializer[i])}}if(e.docString!=null&&(o.docString=String(e.docString)),e.input){if(!Array.isArray(e.input))throw TypeError(".onnx.GraphProto.input: array expected");o.input=[];for(var i=0;i<e.input.length;++i){if(typeof e.input[i]!="object")throw TypeError(".onnx.GraphProto.input: object expected");o.input[i]=f.onnx.ValueInfoProto.fromObject(e.input[i])}}if(e.output){if(!Array.isArray(e.output))throw TypeError(".onnx.GraphProto.output: array expected");o.output=[];for(var i=0;i<e.output.length;++i){if(typeof e.output[i]!="object")throw TypeError(".onnx.GraphProto.output: object expected");o.output[i]=f.onnx.ValueInfoProto.fromObject(e.output[i])}}if(e.valueInfo){if(!Array.isArray(e.valueInfo))throw TypeError(".onnx.GraphProto.valueInfo: array expected");o.valueInfo=[];for(var i=0;i<e.valueInfo.length;++i){if(typeof e.valueInfo[i]!="object")throw TypeError(".onnx.GraphProto.valueInfo: object expected");o.valueInfo[i]=f.onnx.ValueInfoProto.fromObject(e.valueInfo[i])}}if(e.quantizationAnnotation){if(!Array.isArray(e.quantizationAnnotation))throw TypeError(".onnx.GraphProto.quantizationAnnotation: array expected");o.quantizationAnnotation=[];for(var i=0;i<e.quantizationAnnotation.length;++i){if(typeof e.quantizationAnnotation[i]!="object")throw TypeError(".onnx.GraphProto.quantizationAnnotation: object expected");o.quantizationAnnotation[i]=f.onnx.TensorAnnotation.fromObject(e.quantizationAnnotation[i])}}return o},t.toObject=function(e,o){o||(o={});var i={};if((o.arrays||o.defaults)&&(i.node=[],i.initializer=[],i.input=[],i.output=[],i.valueInfo=[],i.quantizationAnnotation=[],i.sparseInitializer=[]),o.defaults&&(i.name="",i.docString=""),e.node&&e.node.length){i.node=[];for(var a=0;a<e.node.length;++a)i.node[a]=f.onnx.NodeProto.toObject(e.node[a],o)}if(e.name!=null&&e.hasOwnProperty("name")&&(i.name=e.name),e.initializer&&e.initializer.length){i.initializer=[];for(var a=0;a<e.initializer.length;++a)i.initializer[a]=f.onnx.TensorProto.toObject(e.initializer[a],o)}if(e.docString!=null&&e.hasOwnProperty("docString")&&(i.docString=e.docString),e.input&&e.input.length){i.input=[];for(var a=0;a<e.input.length;++a)i.input[a]=f.onnx.ValueInfoProto.toObject(e.input[a],o)}if(e.output&&e.output.length){i.output=[];for(var a=0;a<e.output.length;++a)i.output[a]=f.onnx.ValueInfoProto.toObject(e.output[a],o)}if(e.valueInfo&&e.valueInfo.length){i.valueInfo=[];for(var a=0;a<e.valueInfo.length;++a)i.valueInfo[a]=f.onnx.ValueInfoProto.toObject(e.valueInfo[a],o)}if(e.quantizationAnnotation&&e.quantizationAnnotation.length){i.quantizationAnnotation=[];for(var a=0;a<e.quantizationAnnotation.length;++a)i.quantizationAnnotation[a]=f.onnx.TensorAnnotation.toObject(e.quantizationAnnotation[a],o)}if(e.sparseInitializer&&e.sparseInitializer.length){i.sparseInitializer=[];for(var a=0;a<e.sparseInitializer.length;++a)i.sparseInitializer[a]=f.onnx.SparseTensorProto.toObject(e.sparseInitializer[a],o)}return i},t.prototype.toJSON=function(){return this.constructor.toObject(this,j.util.toJSONOptions)},t.getTypeUrl=function(e){return e===void 0&&(e="type.googleapis.com"),e+"/onnx.GraphProto"},t}(),r.TensorProto=function(){function t(n){if(this.dims=[],this.floatData=[],this.int32Data=[],this.stringData=[],this.int64Data=[],this.externalData=[],this.doubleData=[],this.uint64Data=[],n)for(var e=Object.keys(n),o=0;o<e.length;++o)n[e[o]]!=null&&(this[e[o]]=n[e[o]])}return t.prototype.dims=h.emptyArray,t.prototype.dataType=0,t.prototype.segment=null,t.prototype.floatData=h.emptyArray,t.prototype.int32Data=h.emptyArray,t.prototype.stringData=h.emptyArray,t.prototype.int64Data=h.emptyArray,t.prototype.name="",t.prototype.docString="",t.prototype.rawData=h.newBuffer([]),t.prototype.externalData=h.emptyArray,t.prototype.dataLocation=0,t.prototype.doubleData=h.emptyArray,t.prototype.uint64Data=h.emptyArray,t.create=function(e){return new t(e)},t.encode=function(e,o){if(o||(o=Z.create()),e.dims!=null&&e.dims.length){o.uint32(10).fork();for(var i=0;i<e.dims.length;++i)o.int64(e.dims[i]);o.ldelim()}if(e.dataType!=null&&Object.hasOwnProperty.call(e,"dataType")&&o.uint32(16).int32(e.dataType),e.segment!=null&&Object.hasOwnProperty.call(e,"segment")&&f.onnx.TensorProto.Segment.encode(e.segment,o.uint32(26).fork()).ldelim(),e.floatData!=null&&e.floatData.length){o.uint32(34).fork();for(var i=0;i<e.floatData.length;++i)o.float(e.floatData[i]);o.ldelim()}if(e.int32Data!=null&&e.int32Data.length){o.uint32(42).fork();for(var i=0;i<e.int32Data.length;++i)o.int32(e.int32Data[i]);o.ldelim()}if(e.stringData!=null&&e.stringData.length)for(var i=0;i<e.stringData.length;++i)o.uint32(50).bytes(e.stringData[i]);if(e.int64Data!=null&&e.int64Data.length){o.uint32(58).fork();for(var i=0;i<e.int64Data.length;++i)o.int64(e.int64Data[i]);o.ldelim()}if(e.name!=null&&Object.hasOwnProperty.call(e,"name")&&o.uint32(66).string(e.name),e.rawData!=null&&Object.hasOwnProperty.call(e,"rawData")&&o.uint32(74).bytes(e.rawData),e.doubleData!=null&&e.doubleData.length){o.uint32(82).fork();for(var i=0;i<e.doubleData.length;++i)o.double(e.doubleData[i]);o.ldelim()}if(e.uint64Data!=null&&e.uint64Data.length){o.uint32(90).fork();for(var i=0;i<e.uint64Data.length;++i)o.uint64(e.uint64Data[i]);o.ldelim()}if(e.docString!=null&&Object.hasOwnProperty.call(e,"docString")&&o.uint32(98).string(e.docString),e.externalData!=null&&e.externalData.length)for(var i=0;i<e.externalData.length;++i)f.onnx.StringStringEntryProto.encode(e.externalData[i],o.uint32(106).fork()).ldelim();return e.dataLocation!=null&&Object.hasOwnProperty.call(e,"dataLocation")&&o.uint32(112).int32(e.dataLocation),o},t.encodeDelimited=function(e,o){return this.encode(e,o).ldelim()},t.decode=function(e,o){e instanceof _||(e=_.create(e));for(var i=o===void 0?e.len:e.pos+o,a=new f.onnx.TensorProto;e.pos<i;){var s=e.uint32();switch(s>>>3){case 1:{if(a.dims&&a.dims.length||(a.dims=[]),(s&7)===2)for(var u=e.uint32()+e.pos;e.pos<u;)a.dims.push(e.int64());else a.dims.push(e.int64());break}case 2:{a.dataType=e.int32();break}case 3:{a.segment=f.onnx.TensorProto.Segment.decode(e,e.uint32());break}case 4:{if(a.floatData&&a.floatData.length||(a.floatData=[]),(s&7)===2)for(var u=e.uint32()+e.pos;e.pos<u;)a.floatData.push(e.float());else a.floatData.push(e.float());break}case 5:{if(a.int32Data&&a.int32Data.length||(a.int32Data=[]),(s&7)===2)for(var u=e.uint32()+e.pos;e.pos<u;)a.int32Data.push(e.int32());else a.int32Data.push(e.int32());break}case 6:{a.stringData&&a.stringData.length||(a.stringData=[]),a.stringData.push(e.bytes());break}case 7:{if(a.int64Data&&a.int64Data.length||(a.int64Data=[]),(s&7)===2)for(var u=e.uint32()+e.pos;e.pos<u;)a.int64Data.push(e.int64());else a.int64Data.push(e.int64());break}case 8:{a.name=e.string();break}case 12:{a.docString=e.string();break}case 9:{a.rawData=e.bytes();break}case 13:{a.externalData&&a.externalData.length||(a.externalData=[]),a.externalData.push(f.onnx.StringStringEntryProto.decode(e,e.uint32()));break}case 14:{a.dataLocation=e.int32();break}case 10:{if(a.doubleData&&a.doubleData.length||(a.doubleData=[]),(s&7)===2)for(var u=e.uint32()+e.pos;e.pos<u;)a.doubleData.push(e.double());else a.doubleData.push(e.double());break}case 11:{if(a.uint64Data&&a.uint64Data.length||(a.uint64Data=[]),(s&7)===2)for(var u=e.uint32()+e.pos;e.pos<u;)a.uint64Data.push(e.uint64());else a.uint64Data.push(e.uint64());break}default:e.skipType(s&7);break}}return a},t.decodeDelimited=function(e){return e instanceof _||(e=new _(e)),this.decode(e,e.uint32())},t.verify=function(e){if(typeof e!="object"||e===null)return"object expected";if(e.dims!=null&&e.hasOwnProperty("dims")){if(!Array.isArray(e.dims))return"dims: array expected";for(var o=0;o<e.dims.length;++o)if(!h.isInteger(e.dims[o])&&!(e.dims[o]&&h.isInteger(e.dims[o].low)&&h.isInteger(e.dims[o].high)))return"dims: integer|Long[] expected"}if(e.dataType!=null&&e.hasOwnProperty("dataType")&&!h.isInteger(e.dataType))return"dataType: integer expected";if(e.segment!=null&&e.hasOwnProperty("segment")){var i=f.onnx.TensorProto.Segment.verify(e.segment);if(i)return"segment."+i}if(e.floatData!=null&&e.hasOwnProperty("floatData")){if(!Array.isArray(e.floatData))return"floatData: array expected";for(var o=0;o<e.floatData.length;++o)if(typeof e.floatData[o]!="number")return"floatData: number[] expected"}if(e.int32Data!=null&&e.hasOwnProperty("int32Data")){if(!Array.isArray(e.int32Data))return"int32Data: array expected";for(var o=0;o<e.int32Data.length;++o)if(!h.isInteger(e.int32Data[o]))return"int32Data: integer[] expected"}if(e.stringData!=null&&e.hasOwnProperty("stringData")){if(!Array.isArray(e.stringData))return"stringData: array expected";for(var o=0;o<e.stringData.length;++o)if(!(e.stringData[o]&&typeof e.stringData[o].length=="number"||h.isString(e.stringData[o])))return"stringData: buffer[] expected"}if(e.int64Data!=null&&e.hasOwnProperty("int64Data")){if(!Array.isArray(e.int64Data))return"int64Data: array expected";for(var o=0;o<e.int64Data.length;++o)if(!h.isInteger(e.int64Data[o])&&!(e.int64Data[o]&&h.isInteger(e.int64Data[o].low)&&h.isInteger(e.int64Data[o].high)))return"int64Data: integer|Long[] expected"}if(e.name!=null&&e.hasOwnProperty("name")&&!h.isString(e.name))return"name: string expected";if(e.docString!=null&&e.hasOwnProperty("docString")&&!h.isString(e.docString))return"docString: string expected";if(e.rawData!=null&&e.hasOwnProperty("rawData")&&!(e.rawData&&typeof e.rawData.length=="number"||h.isString(e.rawData)))return"rawData: buffer expected";if(e.externalData!=null&&e.hasOwnProperty("externalData")){if(!Array.isArray(e.externalData))return"externalData: array expected";for(var o=0;o<e.externalData.length;++o){var i=f.onnx.StringStringEntryProto.verify(e.externalData[o]);if(i)return"externalData."+i}}if(e.dataLocation!=null&&e.hasOwnProperty("dataLocation"))switch(e.dataLocation){default:return"dataLocation: enum value expected";case 0:case 1:break}if(e.doubleData!=null&&e.hasOwnProperty("doubleData")){if(!Array.isArray(e.doubleData))return"doubleData: array expected";for(var o=0;o<e.doubleData.length;++o)if(typeof e.doubleData[o]!="number")return"doubleData: number[] expected"}if(e.uint64Data!=null&&e.hasOwnProperty("uint64Data")){if(!Array.isArray(e.uint64Data))return"uint64Data: array expected";for(var o=0;o<e.uint64Data.length;++o)if(!h.isInteger(e.uint64Data[o])&&!(e.uint64Data[o]&&h.isInteger(e.uint64Data[o].low)&&h.isInteger(e.uint64Data[o].high)))return"uint64Data: integer|Long[] expected"}return null},t.fromObject=function(e){if(e instanceof f.onnx.TensorProto)return e;var o=new f.onnx.TensorProto;if(e.dims){if(!Array.isArray(e.dims))throw TypeError(".onnx.TensorProto.dims: array expected");o.dims=[];for(var i=0;i<e.dims.length;++i)h.Long?(o.dims[i]=h.Long.fromValue(e.dims[i])).unsigned=!1:typeof e.dims[i]=="string"?o.dims[i]=parseInt(e.dims[i],10):typeof e.dims[i]=="number"?o.dims[i]=e.dims[i]:typeof e.dims[i]=="object"&&(o.dims[i]=new h.LongBits(e.dims[i].low>>>0,e.dims[i].high>>>0).toNumber())}if(e.dataType!=null&&(o.dataType=e.dataType|0),e.segment!=null){if(typeof e.segment!="object")throw TypeError(".onnx.TensorProto.segment: object expected");o.segment=f.onnx.TensorProto.Segment.fromObject(e.segment)}if(e.floatData){if(!Array.isArray(e.floatData))throw TypeError(".onnx.TensorProto.floatData: array expected");o.floatData=[];for(var i=0;i<e.floatData.length;++i)o.floatData[i]=Number(e.floatData[i])}if(e.int32Data){if(!Array.isArray(e.int32Data))throw TypeError(".onnx.TensorProto.int32Data: array expected");o.int32Data=[];for(var i=0;i<e.int32Data.length;++i)o.int32Data[i]=e.int32Data[i]|0}if(e.stringData){if(!Array.isArray(e.stringData))throw TypeError(".onnx.TensorProto.stringData: array expected");o.stringData=[];for(var i=0;i<e.stringData.length;++i)typeof e.stringData[i]=="string"?h.base64.decode(e.stringData[i],o.stringData[i]=h.newBuffer(h.base64.length(e.stringData[i])),0):e.stringData[i].length>=0&&(o.stringData[i]=e.stringData[i])}if(e.int64Data){if(!Array.isArray(e.int64Data))throw TypeError(".onnx.TensorProto.int64Data: array expected");o.int64Data=[];for(var i=0;i<e.int64Data.length;++i)h.Long?(o.int64Data[i]=h.Long.fromValue(e.int64Data[i])).unsigned=!1:typeof e.int64Data[i]=="string"?o.int64Data[i]=parseInt(e.int64Data[i],10):typeof e.int64Data[i]=="number"?o.int64Data[i]=e.int64Data[i]:typeof e.int64Data[i]=="object"&&(o.int64Data[i]=new h.LongBits(e.int64Data[i].low>>>0,e.int64Data[i].high>>>0).toNumber())}if(e.name!=null&&(o.name=String(e.name)),e.docString!=null&&(o.docString=String(e.docString)),e.rawData!=null&&(typeof e.rawData=="string"?h.base64.decode(e.rawData,o.rawData=h.newBuffer(h.base64.length(e.rawData)),0):e.rawData.length>=0&&(o.rawData=e.rawData)),e.externalData){if(!Array.isArray(e.externalData))throw TypeError(".onnx.TensorProto.externalData: array expected");o.externalData=[];for(var i=0;i<e.externalData.length;++i){if(typeof e.externalData[i]!="object")throw TypeError(".onnx.TensorProto.externalData: object expected");o.externalData[i]=f.onnx.StringStringEntryProto.fromObject(e.externalData[i])}}switch(e.dataLocation){default:if(typeof e.dataLocation=="number"){o.dataLocation=e.dataLocation;break}break;case"DEFAULT":case 0:o.dataLocation=0;break;case"EXTERNAL":case 1:o.dataLocation=1;break}if(e.doubleData){if(!Array.isArray(e.doubleData))throw TypeError(".onnx.TensorProto.doubleData: array expected");o.doubleData=[];for(var i=0;i<e.doubleData.length;++i)o.doubleData[i]=Number(e.doubleData[i])}if(e.uint64Data){if(!Array.isArray(e.uint64Data))throw TypeError(".onnx.TensorProto.uint64Data: array expected");o.uint64Data=[];for(var i=0;i<e.uint64Data.length;++i)h.Long?(o.uint64Data[i]=h.Long.fromValue(e.uint64Data[i])).unsigned=!0:typeof e.uint64Data[i]=="string"?o.uint64Data[i]=parseInt(e.uint64Data[i],10):typeof e.uint64Data[i]=="number"?o.uint64Data[i]=e.uint64Data[i]:typeof e.uint64Data[i]=="object"&&(o.uint64Data[i]=new h.LongBits(e.uint64Data[i].low>>>0,e.uint64Data[i].high>>>0).toNumber(!0))}return o},t.toObject=function(e,o){o||(o={});var i={};if((o.arrays||o.defaults)&&(i.dims=[],i.floatData=[],i.int32Data=[],i.stringData=[],i.int64Data=[],i.doubleData=[],i.uint64Data=[],i.externalData=[]),o.defaults&&(i.dataType=0,i.segment=null,i.name="",o.bytes===String?i.rawData="":(i.rawData=[],o.bytes!==Array&&(i.rawData=h.newBuffer(i.rawData))),i.docString="",i.dataLocation=o.enums===String?"DEFAULT":0),e.dims&&e.dims.length){i.dims=[];for(var a=0;a<e.dims.length;++a)typeof e.dims[a]=="number"?i.dims[a]=o.longs===String?String(e.dims[a]):e.dims[a]:i.dims[a]=o.longs===String?h.Long.prototype.toString.call(e.dims[a]):o.longs===Number?new h.LongBits(e.dims[a].low>>>0,e.dims[a].high>>>0).toNumber():e.dims[a]}if(e.dataType!=null&&e.hasOwnProperty("dataType")&&(i.dataType=e.dataType),e.segment!=null&&e.hasOwnProperty("segment")&&(i.segment=f.onnx.TensorProto.Segment.toObject(e.segment,o)),e.floatData&&e.floatData.length){i.floatData=[];for(var a=0;a<e.floatData.length;++a)i.floatData[a]=o.json&&!isFinite(e.floatData[a])?String(e.floatData[a]):e.floatData[a]}if(e.int32Data&&e.int32Data.length){i.int32Data=[];for(var a=0;a<e.int32Data.length;++a)i.int32Data[a]=e.int32Data[a]}if(e.stringData&&e.stringData.length){i.stringData=[];for(var a=0;a<e.stringData.length;++a)i.stringData[a]=o.bytes===String?h.base64.encode(e.stringData[a],0,e.stringData[a].length):o.bytes===Array?Array.prototype.slice.call(e.stringData[a]):e.stringData[a]}if(e.int64Data&&e.int64Data.length){i.int64Data=[];for(var a=0;a<e.int64Data.length;++a)typeof e.int64Data[a]=="number"?i.int64Data[a]=o.longs===String?String(e.int64Data[a]):e.int64Data[a]:i.int64Data[a]=o.longs===String?h.Long.prototype.toString.call(e.int64Data[a]):o.longs===Number?new h.LongBits(e.int64Data[a].low>>>0,e.int64Data[a].high>>>0).toNumber():e.int64Data[a]}if(e.name!=null&&e.hasOwnProperty("name")&&(i.name=e.name),e.rawData!=null&&e.hasOwnProperty("rawData")&&(i.rawData=o.bytes===String?h.base64.encode(e.rawData,0,e.rawData.length):o.bytes===Array?Array.prototype.slice.call(e.rawData):e.rawData),e.doubleData&&e.doubleData.length){i.doubleData=[];for(var a=0;a<e.doubleData.length;++a)i.doubleData[a]=o.json&&!isFinite(e.doubleData[a])?String(e.doubleData[a]):e.doubleData[a]}if(e.uint64Data&&e.uint64Data.length){i.uint64Data=[];for(var a=0;a<e.uint64Data.length;++a)typeof e.uint64Data[a]=="number"?i.uint64Data[a]=o.longs===String?String(e.uint64Data[a]):e.uint64Data[a]:i.uint64Data[a]=o.longs===String?h.Long.prototype.toString.call(e.uint64Data[a]):o.longs===Number?new h.LongBits(e.uint64Data[a].low>>>0,e.uint64Data[a].high>>>0).toNumber(!0):e.uint64Data[a]}if(e.docString!=null&&e.hasOwnProperty("docString")&&(i.docString=e.docString),e.externalData&&e.externalData.length){i.externalData=[];for(var a=0;a<e.externalData.length;++a)i.externalData[a]=f.onnx.StringStringEntryProto.toObject(e.externalData[a],o)}return e.dataLocation!=null&&e.hasOwnProperty("dataLocation")&&(i.dataLocation=o.enums===String?f.onnx.TensorProto.DataLocation[e.dataLocation]===void 0?e.dataLocation:f.onnx.TensorProto.DataLocation[e.dataLocation]:e.dataLocation),i},t.prototype.toJSON=function(){return this.constructor.toObject(this,j.util.toJSONOptions)},t.getTypeUrl=function(e){return e===void 0&&(e="type.googleapis.com"),e+"/onnx.TensorProto"},t.DataType=function(){var n={},e=Object.create(n);return e[n[0]="UNDEFINED"]=0,e[n[1]="FLOAT"]=1,e[n[2]="UINT8"]=2,e[n[3]="INT8"]=3,e[n[4]="UINT16"]=4,e[n[5]="INT16"]=5,e[n[6]="INT32"]=6,e[n[7]="INT64"]=7,e[n[8]="STRING"]=8,e[n[9]="BOOL"]=9,e[n[10]="FLOAT16"]=10,e[n[11]="DOUBLE"]=11,e[n[12]="UINT32"]=12,e[n[13]="UINT64"]=13,e[n[14]="COMPLEX64"]=14,e[n[15]="COMPLEX128"]=15,e[n[16]="BFLOAT16"]=16,e[n[17]="FLOAT8E4M3FN"]=17,e[n[18]="FLOAT8E4M3FNUZ"]=18,e[n[19]="FLOAT8E5M2"]=19,e[n[20]="FLOAT8E5M2FNUZ"]=20,e}(),t.Segment=function(){function n(e){if(e)for(var o=Object.keys(e),i=0;i<o.length;++i)e[o[i]]!=null&&(this[o[i]]=e[o[i]])}return n.prototype.begin=h.Long?h.Long.fromBits(0,0,!1):0,n.prototype.end=h.Long?h.Long.fromBits(0,0,!1):0,n.create=function(o){return new n(o)},n.encode=function(o,i){return i||(i=Z.create()),o.begin!=null&&Object.hasOwnProperty.call(o,"begin")&&i.uint32(8).int64(o.begin),o.end!=null&&Object.hasOwnProperty.call(o,"end")&&i.uint32(16).int64(o.end),i},n.encodeDelimited=function(o,i){return this.encode(o,i).ldelim()},n.decode=function(o,i){o instanceof _||(o=_.create(o));for(var a=i===void 0?o.len:o.pos+i,s=new f.onnx.TensorProto.Segment;o.pos<a;){var u=o.uint32();switch(u>>>3){case 1:{s.begin=o.int64();break}case 2:{s.end=o.int64();break}default:o.skipType(u&7);break}}return s},n.decodeDelimited=function(o){return o instanceof _||(o=new _(o)),this.decode(o,o.uint32())},n.verify=function(o){return typeof o!="object"||o===null?"object expected":o.begin!=null&&o.hasOwnProperty("begin")&&!h.isInteger(o.begin)&&!(o.begin&&h.isInteger(o.begin.low)&&h.isInteger(o.begin.high))?"begin: integer|Long expected":o.end!=null&&o.hasOwnProperty("end")&&!h.isInteger(o.end)&&!(o.end&&h.isInteger(o.end.low)&&h.isInteger(o.end.high))?"end: integer|Long expected":null},n.fromObject=function(o){if(o instanceof f.onnx.TensorProto.Segment)return o;var i=new f.onnx.TensorProto.Segment;return o.begin!=null&&(h.Long?(i.begin=h.Long.fromValue(o.begin)).unsigned=!1:typeof o.begin=="string"?i.begin=parseInt(o.begin,10):typeof o.begin=="number"?i.begin=o.begin:typeof o.begin=="object"&&(i.begin=new h.LongBits(o.begin.low>>>0,o.begin.high>>>0).toNumber())),o.end!=null&&(h.Long?(i.end=h.Long.fromValue(o.end)).unsigned=!1:typeof o.end=="string"?i.end=parseInt(o.end,10):typeof o.end=="number"?i.end=o.end:typeof o.end=="object"&&(i.end=new h.LongBits(o.end.low>>>0,o.end.high>>>0).toNumber())),i},n.toObject=function(o,i){i||(i={});var a={};if(i.defaults){if(h.Long){var s=new h.Long(0,0,!1);a.begin=i.longs===String?s.toString():i.longs===Number?s.toNumber():s}else a.begin=i.longs===String?"0":0;if(h.Long){var s=new h.Long(0,0,!1);a.end=i.longs===String?s.toString():i.longs===Number?s.toNumber():s}else a.end=i.longs===String?"0":0}return o.begin!=null&&o.hasOwnProperty("begin")&&(typeof o.begin=="number"?a.begin=i.longs===String?String(o.begin):o.begin:a.begin=i.longs===String?h.Long.prototype.toString.call(o.begin):i.longs===Number?new h.LongBits(o.begin.low>>>0,o.begin.high>>>0).toNumber():o.begin),o.end!=null&&o.hasOwnProperty("end")&&(typeof o.end=="number"?a.end=i.longs===String?String(o.end):o.end:a.end=i.longs===String?h.Long.prototype.toString.call(o.end):i.longs===Number?new h.LongBits(o.end.low>>>0,o.end.high>>>0).toNumber():o.end),a},n.prototype.toJSON=function(){return this.constructor.toObject(this,j.util.toJSONOptions)},n.getTypeUrl=function(o){return o===void 0&&(o="type.googleapis.com"),o+"/onnx.TensorProto.Segment"},n}(),t.DataLocation=function(){var n={},e=Object.create(n);return e[n[0]="DEFAULT"]=0,e[n[1]="EXTERNAL"]=1,e}(),t}(),r.SparseTensorProto=function(){function t(n){if(this.dims=[],n)for(var e=Object.keys(n),o=0;o<e.length;++o)n[e[o]]!=null&&(this[e[o]]=n[e[o]])}return t.prototype.values=null,t.prototype.indices=null,t.prototype.dims=h.emptyArray,t.create=function(e){return new t(e)},t.encode=function(e,o){if(o||(o=Z.create()),e.values!=null&&Object.hasOwnProperty.call(e,"values")&&f.onnx.TensorProto.encode(e.values,o.uint32(10).fork()).ldelim(),e.indices!=null&&Object.hasOwnProperty.call(e,"indices")&&f.onnx.TensorProto.encode(e.indices,o.uint32(18).fork()).ldelim(),e.dims!=null&&e.dims.length){o.uint32(26).fork();for(var i=0;i<e.dims.length;++i)o.int64(e.dims[i]);o.ldelim()}return o},t.encodeDelimited=function(e,o){return this.encode(e,o).ldelim()},t.decode=function(e,o){e instanceof _||(e=_.create(e));for(var i=o===void 0?e.len:e.pos+o,a=new f.onnx.SparseTensorProto;e.pos<i;){var s=e.uint32();switch(s>>>3){case 1:{a.values=f.onnx.TensorProto.decode(e,e.uint32());break}case 2:{a.indices=f.onnx.TensorProto.decode(e,e.uint32());break}case 3:{if(a.dims&&a.dims.length||(a.dims=[]),(s&7)===2)for(var u=e.uint32()+e.pos;e.pos<u;)a.dims.push(e.int64());else a.dims.push(e.int64());break}default:e.skipType(s&7);break}}return a},t.decodeDelimited=function(e){return e instanceof _||(e=new _(e)),this.decode(e,e.uint32())},t.verify=function(e){if(typeof e!="object"||e===null)return"object expected";if(e.values!=null&&e.hasOwnProperty("values")){var o=f.onnx.TensorProto.verify(e.values);if(o)return"values."+o}if(e.indices!=null&&e.hasOwnProperty("indices")){var o=f.onnx.TensorProto.verify(e.indices);if(o)return"indices."+o}if(e.dims!=null&&e.hasOwnProperty("dims")){if(!Array.isArray(e.dims))return"dims: array expected";for(var i=0;i<e.dims.length;++i)if(!h.isInteger(e.dims[i])&&!(e.dims[i]&&h.isInteger(e.dims[i].low)&&h.isInteger(e.dims[i].high)))return"dims: integer|Long[] expected"}return null},t.fromObject=function(e){if(e instanceof f.onnx.SparseTensorProto)return e;var o=new f.onnx.SparseTensorProto;if(e.values!=null){if(typeof e.values!="object")throw TypeError(".onnx.SparseTensorProto.values: object expected");o.values=f.onnx.TensorProto.fromObject(e.values)}if(e.indices!=null){if(typeof e.indices!="object")throw TypeError(".onnx.SparseTensorProto.indices: object expected");o.indices=f.onnx.TensorProto.fromObject(e.indices)}if(e.dims){if(!Array.isArray(e.dims))throw TypeError(".onnx.SparseTensorProto.dims: array expected");o.dims=[];for(var i=0;i<e.dims.length;++i)h.Long?(o.dims[i]=h.Long.fromValue(e.dims[i])).unsigned=!1:typeof e.dims[i]=="string"?o.dims[i]=parseInt(e.dims[i],10):typeof e.dims[i]=="number"?o.dims[i]=e.dims[i]:typeof e.dims[i]=="object"&&(o.dims[i]=new h.LongBits(e.dims[i].low>>>0,e.dims[i].high>>>0).toNumber())}return o},t.toObject=function(e,o){o||(o={});var i={};if((o.arrays||o.defaults)&&(i.dims=[]),o.defaults&&(i.values=null,i.indices=null),e.values!=null&&e.hasOwnProperty("values")&&(i.values=f.onnx.TensorProto.toObject(e.values,o)),e.indices!=null&&e.hasOwnProperty("indices")&&(i.indices=f.onnx.TensorProto.toObject(e.indices,o)),e.dims&&e.dims.length){i.dims=[];for(var a=0;a<e.dims.length;++a)typeof e.dims[a]=="number"?i.dims[a]=o.longs===String?String(e.dims[a]):e.dims[a]:i.dims[a]=o.longs===String?h.Long.prototype.toString.call(e.dims[a]):o.longs===Number?new h.LongBits(e.dims[a].low>>>0,e.dims[a].high>>>0).toNumber():e.dims[a]}return i},t.prototype.toJSON=function(){return this.constructor.toObject(this,j.util.toJSONOptions)},t.getTypeUrl=function(e){return e===void 0&&(e="type.googleapis.com"),e+"/onnx.SparseTensorProto"},t}(),r.TensorShapeProto=function(){function t(n){if(this.dim=[],n)for(var e=Object.keys(n),o=0;o<e.length;++o)n[e[o]]!=null&&(this[e[o]]=n[e[o]])}return t.prototype.dim=h.emptyArray,t.create=function(e){return new t(e)},t.encode=function(e,o){if(o||(o=Z.create()),e.dim!=null&&e.dim.length)for(var i=0;i<e.dim.length;++i)f.onnx.TensorShapeProto.Dimension.encode(e.dim[i],o.uint32(10).fork()).ldelim();return o},t.encodeDelimited=function(e,o){return this.encode(e,o).ldelim()},t.decode=function(e,o){e instanceof _||(e=_.create(e));for(var i=o===void 0?e.len:e.pos+o,a=new f.onnx.TensorShapeProto;e.pos<i;){var s=e.uint32();switch(s>>>3){case 1:{a.dim&&a.dim.length||(a.dim=[]),a.dim.push(f.onnx.TensorShapeProto.Dimension.decode(e,e.uint32()));break}default:e.skipType(s&7);break}}return a},t.decodeDelimited=function(e){return e instanceof _||(e=new _(e)),this.decode(e,e.uint32())},t.verify=function(e){if(typeof e!="object"||e===null)return"object expected";if(e.dim!=null&&e.hasOwnProperty("dim")){if(!Array.isArray(e.dim))return"dim: array expected";for(var o=0;o<e.dim.length;++o){var i=f.onnx.TensorShapeProto.Dimension.verify(e.dim[o]);if(i)return"dim."+i}}return null},t.fromObject=function(e){if(e instanceof f.onnx.TensorShapeProto)return e;var o=new f.onnx.TensorShapeProto;if(e.dim){if(!Array.isArray(e.dim))throw TypeError(".onnx.TensorShapeProto.dim: array expected");o.dim=[];for(var i=0;i<e.dim.length;++i){if(typeof e.dim[i]!="object")throw TypeError(".onnx.TensorShapeProto.dim: object expected");o.dim[i]=f.onnx.TensorShapeProto.Dimension.fromObject(e.dim[i])}}return o},t.toObject=function(e,o){o||(o={});var i={};if((o.arrays||o.defaults)&&(i.dim=[]),e.dim&&e.dim.length){i.dim=[];for(var a=0;a<e.dim.length;++a)i.dim[a]=f.onnx.TensorShapeProto.Dimension.toObject(e.dim[a],o)}return i},t.prototype.toJSON=function(){return this.constructor.toObject(this,j.util.toJSONOptions)},t.getTypeUrl=function(e){return e===void 0&&(e="type.googleapis.com"),e+"/onnx.TensorShapeProto"},t.Dimension=function(){function n(o){if(o)for(var i=Object.keys(o),a=0;a<i.length;++a)o[i[a]]!=null&&(this[i[a]]=o[i[a]])}n.prototype.dimValue=null,n.prototype.dimParam=null,n.prototype.denotation="";var e;return Object.defineProperty(n.prototype,"value",{get:h.oneOfGetter(e=["dimValue","dimParam"]),set:h.oneOfSetter(e)}),n.create=function(i){return new n(i)},n.encode=function(i,a){return a||(a=Z.create()),i.dimValue!=null&&Object.hasOwnProperty.call(i,"dimValue")&&a.uint32(8).int64(i.dimValue),i.dimParam!=null&&Object.hasOwnProperty.call(i,"dimParam")&&a.uint32(18).string(i.dimParam),i.denotation!=null&&Object.hasOwnProperty.call(i,"denotation")&&a.uint32(26).string(i.denotation),a},n.encodeDelimited=function(i,a){return this.encode(i,a).ldelim()},n.decode=function(i,a){i instanceof _||(i=_.create(i));for(var s=a===void 0?i.len:i.pos+a,u=new f.onnx.TensorShapeProto.Dimension;i.pos<s;){var l=i.uint32();switch(l>>>3){case 1:{u.dimValue=i.int64();break}case 2:{u.dimParam=i.string();break}case 3:{u.denotation=i.string();break}default:i.skipType(l&7);break}}return u},n.decodeDelimited=function(i){return i instanceof _||(i=new _(i)),this.decode(i,i.uint32())},n.verify=function(i){if(typeof i!="object"||i===null)return"object expected";var a={};if(i.dimValue!=null&&i.hasOwnProperty("dimValue")&&(a.value=1,!h.isInteger(i.dimValue)&&!(i.dimValue&&h.isInteger(i.dimValue.low)&&h.isInteger(i.dimValue.high))))return"dimValue: integer|Long expected";if(i.dimParam!=null&&i.hasOwnProperty("dimParam")){if(a.value===1)return"value: multiple values";if(a.value=1,!h.isString(i.dimParam))return"dimParam: string expected"}return i.denotation!=null&&i.hasOwnProperty("denotation")&&!h.isString(i.denotation)?"denotation: string expected":null},n.fromObject=function(i){if(i instanceof f.onnx.TensorShapeProto.Dimension)return i;var a=new f.onnx.TensorShapeProto.Dimension;return i.dimValue!=null&&(h.Long?(a.dimValue=h.Long.fromValue(i.dimValue)).unsigned=!1:typeof i.dimValue=="string"?a.dimValue=parseInt(i.dimValue,10):typeof i.dimValue=="number"?a.dimValue=i.dimValue:typeof i.dimValue=="object"&&(a.dimValue=new h.LongBits(i.dimValue.low>>>0,i.dimValue.high>>>0).toNumber())),i.dimParam!=null&&(a.dimParam=String(i.dimParam)),i.denotation!=null&&(a.denotation=String(i.denotation)),a},n.toObject=function(i,a){a||(a={});var s={};return a.defaults&&(s.denotation=""),i.dimValue!=null&&i.hasOwnProperty("dimValue")&&(typeof i.dimValue=="number"?s.dimValue=a.longs===String?String(i.dimValue):i.dimValue:s.dimValue=a.longs===String?h.Long.prototype.toString.call(i.dimValue):a.longs===Number?new h.LongBits(i.dimValue.low>>>0,i.dimValue.high>>>0).toNumber():i.dimValue,a.oneofs&&(s.value="dimValue")),i.dimParam!=null&&i.hasOwnProperty("dimParam")&&(s.dimParam=i.dimParam,a.oneofs&&(s.value="dimParam")),i.denotation!=null&&i.hasOwnProperty("denotation")&&(s.denotation=i.denotation),s},n.prototype.toJSON=function(){return this.constructor.toObject(this,j.util.toJSONOptions)},n.getTypeUrl=function(i){return i===void 0&&(i="type.googleapis.com"),i+"/onnx.TensorShapeProto.Dimension"},n}(),t}(),r.TypeProto=function(){function t(e){if(e)for(var o=Object.keys(e),i=0;i<o.length;++i)e[o[i]]!=null&&(this[o[i]]=e[o[i]])}t.prototype.tensorType=null,t.prototype.sequenceType=null,t.prototype.mapType=null,t.prototype.optionalType=null,t.prototype.sparseTensorType=null,t.prototype.denotation="";var n;return Object.defineProperty(t.prototype,"value",{get:h.oneOfGetter(n=["tensorType","sequenceType","mapType","optionalType","sparseTensorType"]),set:h.oneOfSetter(n)}),t.create=function(o){return new t(o)},t.encode=function(o,i){return i||(i=Z.create()),o.tensorType!=null&&Object.hasOwnProperty.call(o,"tensorType")&&f.onnx.TypeProto.Tensor.encode(o.tensorType,i.uint32(10).fork()).ldelim(),o.sequenceType!=null&&Object.hasOwnProperty.call(o,"sequenceType")&&f.onnx.TypeProto.Sequence.encode(o.sequenceType,i.uint32(34).fork()).ldelim(),o.mapType!=null&&Object.hasOwnProperty.call(o,"mapType")&&f.onnx.TypeProto.Map.encode(o.mapType,i.uint32(42).fork()).ldelim(),o.denotation!=null&&Object.hasOwnProperty.call(o,"denotation")&&i.uint32(50).string(o.denotation),o.sparseTensorType!=null&&Object.hasOwnProperty.call(o,"sparseTensorType")&&f.onnx.TypeProto.SparseTensor.encode(o.sparseTensorType,i.uint32(66).fork()).ldelim(),o.optionalType!=null&&Object.hasOwnProperty.call(o,"optionalType")&&f.onnx.TypeProto.Optional.encode(o.optionalType,i.uint32(74).fork()).ldelim(),i},t.encodeDelimited=function(o,i){return this.encode(o,i).ldelim()},t.decode=function(o,i){o instanceof _||(o=_.create(o));for(var a=i===void 0?o.len:o.pos+i,s=new f.onnx.TypeProto;o.pos<a;){var u=o.uint32();switch(u>>>3){case 1:{s.tensorType=f.onnx.TypeProto.Tensor.decode(o,o.uint32());break}case 4:{s.sequenceType=f.onnx.TypeProto.Sequence.decode(o,o.uint32());break}case 5:{s.mapType=f.onnx.TypeProto.Map.decode(o,o.uint32());break}case 9:{s.optionalType=f.onnx.TypeProto.Optional.decode(o,o.uint32());break}case 8:{s.sparseTensorType=f.onnx.TypeProto.SparseTensor.decode(o,o.uint32());break}case 6:{s.denotation=o.string();break}default:o.skipType(u&7);break}}return s},t.decodeDelimited=function(o){return o instanceof _||(o=new _(o)),this.decode(o,o.uint32())},t.verify=function(o){if(typeof o!="object"||o===null)return"object expected";var i={};if(o.tensorType!=null&&o.hasOwnProperty("tensorType")){i.value=1;{var a=f.onnx.TypeProto.Tensor.verify(o.tensorType);if(a)return"tensorType."+a}}if(o.sequenceType!=null&&o.hasOwnProperty("sequenceType")){if(i.value===1)return"value: multiple values";i.value=1;{var a=f.onnx.TypeProto.Sequence.verify(o.sequenceType);if(a)return"sequenceType."+a}}if(o.mapType!=null&&o.hasOwnProperty("mapType")){if(i.value===1)return"value: multiple values";i.value=1;{var a=f.onnx.TypeProto.Map.verify(o.mapType);if(a)return"mapType."+a}}if(o.optionalType!=null&&o.hasOwnProperty("optionalType")){if(i.value===1)return"value: multiple values";i.value=1;{var a=f.onnx.TypeProto.Optional.verify(o.optionalType);if(a)return"optionalType."+a}}if(o.sparseTensorType!=null&&o.hasOwnProperty("sparseTensorType")){if(i.value===1)return"value: multiple values";i.value=1;{var a=f.onnx.TypeProto.SparseTensor.verify(o.sparseTensorType);if(a)return"sparseTensorType."+a}}return o.denotation!=null&&o.hasOwnProperty("denotation")&&!h.isString(o.denotation)?"denotation: string expected":null},t.fromObject=function(o){if(o instanceof f.onnx.TypeProto)return o;var i=new f.onnx.TypeProto;if(o.tensorType!=null){if(typeof o.tensorType!="object")throw TypeError(".onnx.TypeProto.tensorType: object expected");i.tensorType=f.onnx.TypeProto.Tensor.fromObject(o.tensorType)}if(o.sequenceType!=null){if(typeof o.sequenceType!="object")throw TypeError(".onnx.TypeProto.sequenceType: object expected");i.sequenceType=f.onnx.TypeProto.Sequence.fromObject(o.sequenceType)}if(o.mapType!=null){if(typeof o.mapType!="object")throw TypeError(".onnx.TypeProto.mapType: object expected");i.mapType=f.onnx.TypeProto.Map.fromObject(o.mapType)}if(o.optionalType!=null){if(typeof o.optionalType!="object")throw TypeError(".onnx.TypeProto.optionalType: object expected");i.optionalType=f.onnx.TypeProto.Optional.fromObject(o.optionalType)}if(o.sparseTensorType!=null){if(typeof o.sparseTensorType!="object")throw TypeError(".onnx.TypeProto.sparseTensorType: object expected");i.sparseTensorType=f.onnx.TypeProto.SparseTensor.fromObject(o.sparseTensorType)}return o.denotation!=null&&(i.denotation=String(o.denotation)),i},t.toObject=function(o,i){i||(i={});var a={};return i.defaults&&(a.denotation=""),o.tensorType!=null&&o.hasOwnProperty("tensorType")&&(a.tensorType=f.onnx.TypeProto.Tensor.toObject(o.tensorType,i),i.oneofs&&(a.value="tensorType")),o.sequenceType!=null&&o.hasOwnProperty("sequenceType")&&(a.sequenceType=f.onnx.TypeProto.Sequence.toObject(o.sequenceType,i),i.oneofs&&(a.value="sequenceType")),o.mapType!=null&&o.hasOwnProperty("mapType")&&(a.mapType=f.onnx.TypeProto.Map.toObject(o.mapType,i),i.oneofs&&(a.value="mapType")),o.denotation!=null&&o.hasOwnProperty("denotation")&&(a.denotation=o.denotation),o.sparseTensorType!=null&&o.hasOwnProperty("sparseTensorType")&&(a.sparseTensorType=f.onnx.TypeProto.SparseTensor.toObject(o.sparseTensorType,i),i.oneofs&&(a.value="sparseTensorType")),o.optionalType!=null&&o.hasOwnProperty("optionalType")&&(a.optionalType=f.onnx.TypeProto.Optional.toObject(o.optionalType,i),i.oneofs&&(a.value="optionalType")),a},t.prototype.toJSON=function(){return this.constructor.toObject(this,j.util.toJSONOptions)},t.getTypeUrl=function(o){return o===void 0&&(o="type.googleapis.com"),o+"/onnx.TypeProto"},t.Tensor=function(){function e(o){if(o)for(var i=Object.keys(o),a=0;a<i.length;++a)o[i[a]]!=null&&(this[i[a]]=o[i[a]])}return e.prototype.elemType=0,e.prototype.shape=null,e.create=function(i){return new e(i)},e.encode=function(i,a){return a||(a=Z.create()),i.elemType!=null&&Object.hasOwnProperty.call(i,"elemType")&&a.uint32(8).int32(i.elemType),i.shape!=null&&Object.hasOwnProperty.call(i,"shape")&&f.onnx.TensorShapeProto.encode(i.shape,a.uint32(18).fork()).ldelim(),a},e.encodeDelimited=function(i,a){return this.encode(i,a).ldelim()},e.decode=function(i,a){i instanceof _||(i=_.create(i));for(var s=a===void 0?i.len:i.pos+a,u=new f.onnx.TypeProto.Tensor;i.pos<s;){var l=i.uint32();switch(l>>>3){case 1:{u.elemType=i.int32();break}case 2:{u.shape=f.onnx.TensorShapeProto.decode(i,i.uint32());break}default:i.skipType(l&7);break}}return u},e.decodeDelimited=function(i){return i instanceof _||(i=new _(i)),this.decode(i,i.uint32())},e.verify=function(i){if(typeof i!="object"||i===null)return"object expected";if(i.elemType!=null&&i.hasOwnProperty("elemType")&&!h.isInteger(i.elemType))return"elemType: integer expected";if(i.shape!=null&&i.hasOwnProperty("shape")){var a=f.onnx.TensorShapeProto.verify(i.shape);if(a)return"shape."+a}return null},e.fromObject=function(i){if(i instanceof f.onnx.TypeProto.Tensor)return i;var a=new f.onnx.TypeProto.Tensor;if(i.elemType!=null&&(a.elemType=i.elemType|0),i.shape!=null){if(typeof i.shape!="object")throw TypeError(".onnx.TypeProto.Tensor.shape: object expected");a.shape=f.onnx.TensorShapeProto.fromObject(i.shape)}return a},e.toObject=function(i,a){a||(a={});var s={};return a.defaults&&(s.elemType=0,s.shape=null),i.elemType!=null&&i.hasOwnProperty("elemType")&&(s.elemType=i.elemType),i.shape!=null&&i.hasOwnProperty("shape")&&(s.shape=f.onnx.TensorShapeProto.toObject(i.shape,a)),s},e.prototype.toJSON=function(){return this.constructor.toObject(this,j.util.toJSONOptions)},e.getTypeUrl=function(i){return i===void 0&&(i="type.googleapis.com"),i+"/onnx.TypeProto.Tensor"},e}(),t.Sequence=function(){function e(o){if(o)for(var i=Object.keys(o),a=0;a<i.length;++a)o[i[a]]!=null&&(this[i[a]]=o[i[a]])}return e.prototype.elemType=null,e.create=function(i){return new e(i)},e.encode=function(i,a){return a||(a=Z.create()),i.elemType!=null&&Object.hasOwnProperty.call(i,"elemType")&&f.onnx.TypeProto.encode(i.elemType,a.uint32(10).fork()).ldelim(),a},e.encodeDelimited=function(i,a){return this.encode(i,a).ldelim()},e.decode=function(i,a){i instanceof _||(i=_.create(i));for(var s=a===void 0?i.len:i.pos+a,u=new f.onnx.TypeProto.Sequence;i.pos<s;){var l=i.uint32();switch(l>>>3){case 1:{u.elemType=f.onnx.TypeProto.decode(i,i.uint32());break}default:i.skipType(l&7);break}}return u},e.decodeDelimited=function(i){return i instanceof _||(i=new _(i)),this.decode(i,i.uint32())},e.verify=function(i){if(typeof i!="object"||i===null)return"object expected";if(i.elemType!=null&&i.hasOwnProperty("elemType")){var a=f.onnx.TypeProto.verify(i.elemType);if(a)return"elemType."+a}return null},e.fromObject=function(i){if(i instanceof f.onnx.TypeProto.Sequence)return i;var a=new f.onnx.TypeProto.Sequence;if(i.elemType!=null){if(typeof i.elemType!="object")throw TypeError(".onnx.TypeProto.Sequence.elemType: object expected");a.elemType=f.onnx.TypeProto.fromObject(i.elemType)}return a},e.toObject=function(i,a){a||(a={});var s={};return a.defaults&&(s.elemType=null),i.elemType!=null&&i.hasOwnProperty("elemType")&&(s.elemType=f.onnx.TypeProto.toObject(i.elemType,a)),s},e.prototype.toJSON=function(){return this.constructor.toObject(this,j.util.toJSONOptions)},e.getTypeUrl=function(i){return i===void 0&&(i="type.googleapis.com"),i+"/onnx.TypeProto.Sequence"},e}(),t.Map=function(){function e(o){if(o)for(var i=Object.keys(o),a=0;a<i.length;++a)o[i[a]]!=null&&(this[i[a]]=o[i[a]])}return e.prototype.keyType=0,e.prototype.valueType=null,e.create=function(i){return new e(i)},e.encode=function(i,a){return a||(a=Z.create()),i.keyType!=null&&Object.hasOwnProperty.call(i,"keyType")&&a.uint32(8).int32(i.keyType),i.valueType!=null&&Object.hasOwnProperty.call(i,"valueType")&&f.onnx.TypeProto.encode(i.valueType,a.uint32(18).fork()).ldelim(),a},e.encodeDelimited=function(i,a){return this.encode(i,a).ldelim()},e.decode=function(i,a){i instanceof _||(i=_.create(i));for(var s=a===void 0?i.len:i.pos+a,u=new f.onnx.TypeProto.Map;i.pos<s;){var l=i.uint32();switch(l>>>3){case 1:{u.keyType=i.int32();break}case 2:{u.valueType=f.onnx.TypeProto.decode(i,i.uint32());break}default:i.skipType(l&7);break}}return u},e.decodeDelimited=function(i){return i instanceof _||(i=new _(i)),this.decode(i,i.uint32())},e.verify=function(i){if(typeof i!="object"||i===null)return"object expected";if(i.keyType!=null&&i.hasOwnProperty("keyType")&&!h.isInteger(i.keyType))return"keyType: integer expected";if(i.valueType!=null&&i.hasOwnProperty("valueType")){var a=f.onnx.TypeProto.verify(i.valueType);if(a)return"valueType."+a}return null},e.fromObject=function(i){if(i instanceof f.onnx.TypeProto.Map)return i;var a=new f.onnx.TypeProto.Map;if(i.keyType!=null&&(a.keyType=i.keyType|0),i.valueType!=null){if(typeof i.valueType!="object")throw TypeError(".onnx.TypeProto.Map.valueType: object expected");a.valueType=f.onnx.TypeProto.fromObject(i.valueType)}return a},e.toObject=function(i,a){a||(a={});var s={};return a.defaults&&(s.keyType=0,s.valueType=null),i.keyType!=null&&i.hasOwnProperty("keyType")&&(s.keyType=i.keyType),i.valueType!=null&&i.hasOwnProperty("valueType")&&(s.valueType=f.onnx.TypeProto.toObject(i.valueType,a)),s},e.prototype.toJSON=function(){return this.constructor.toObject(this,j.util.toJSONOptions)},e.getTypeUrl=function(i){return i===void 0&&(i="type.googleapis.com"),i+"/onnx.TypeProto.Map"},e}(),t.Optional=function(){function e(o){if(o)for(var i=Object.keys(o),a=0;a<i.length;++a)o[i[a]]!=null&&(this[i[a]]=o[i[a]])}return e.prototype.elemType=null,e.create=function(i){return new e(i)},e.encode=function(i,a){return a||(a=Z.create()),i.elemType!=null&&Object.hasOwnProperty.call(i,"elemType")&&f.onnx.TypeProto.encode(i.elemType,a.uint32(10).fork()).ldelim(),a},e.encodeDelimited=function(i,a){return this.encode(i,a).ldelim()},e.decode=function(i,a){i instanceof _||(i=_.create(i));for(var s=a===void 0?i.len:i.pos+a,u=new f.onnx.TypeProto.Optional;i.pos<s;){var l=i.uint32();switch(l>>>3){case 1:{u.elemType=f.onnx.TypeProto.decode(i,i.uint32());break}default:i.skipType(l&7);break}}return u},e.decodeDelimited=function(i){return i instanceof _||(i=new _(i)),this.decode(i,i.uint32())},e.verify=function(i){if(typeof i!="object"||i===null)return"object expected";if(i.elemType!=null&&i.hasOwnProperty("elemType")){var a=f.onnx.TypeProto.verify(i.elemType);if(a)return"elemType."+a}return null},e.fromObject=function(i){if(i instanceof f.onnx.TypeProto.Optional)return i;var a=new f.onnx.TypeProto.Optional;if(i.elemType!=null){if(typeof i.elemType!="object")throw TypeError(".onnx.TypeProto.Optional.elemType: object expected");a.elemType=f.onnx.TypeProto.fromObject(i.elemType)}return a},e.toObject=function(i,a){a||(a={});var s={};return a.defaults&&(s.elemType=null),i.elemType!=null&&i.hasOwnProperty("elemType")&&(s.elemType=f.onnx.TypeProto.toObject(i.elemType,a)),s},e.prototype.toJSON=function(){return this.constructor.toObject(this,j.util.toJSONOptions)},e.getTypeUrl=function(i){return i===void 0&&(i="type.googleapis.com"),i+"/onnx.TypeProto.Optional"},e}(),t.SparseTensor=function(){function e(o){if(o)for(var i=Object.keys(o),a=0;a<i.length;++a)o[i[a]]!=null&&(this[i[a]]=o[i[a]])}return e.prototype.elemType=0,e.prototype.shape=null,e.create=function(i){return new e(i)},e.encode=function(i,a){return a||(a=Z.create()),i.elemType!=null&&Object.hasOwnProperty.call(i,"elemType")&&a.uint32(8).int32(i.elemType),i.shape!=null&&Object.hasOwnProperty.call(i,"shape")&&f.onnx.TensorShapeProto.encode(i.shape,a.uint32(18).fork()).ldelim(),a},e.encodeDelimited=function(i,a){return this.encode(i,a).ldelim()},e.decode=function(i,a){i instanceof _||(i=_.create(i));for(var s=a===void 0?i.len:i.pos+a,u=new f.onnx.TypeProto.SparseTensor;i.pos<s;){var l=i.uint32();switch(l>>>3){case 1:{u.elemType=i.int32();break}case 2:{u.shape=f.onnx.TensorShapeProto.decode(i,i.uint32());break}default:i.skipType(l&7);break}}return u},e.decodeDelimited=function(i){return i instanceof _||(i=new _(i)),this.decode(i,i.uint32())},e.verify=function(i){if(typeof i!="object"||i===null)return"object expected";if(i.elemType!=null&&i.hasOwnProperty("elemType")&&!h.isInteger(i.elemType))return"elemType: integer expected";if(i.shape!=null&&i.hasOwnProperty("shape")){var a=f.onnx.TensorShapeProto.verify(i.shape);if(a)return"shape."+a}return null},e.fromObject=function(i){if(i instanceof f.onnx.TypeProto.SparseTensor)return i;var a=new f.onnx.TypeProto.SparseTensor;if(i.elemType!=null&&(a.elemType=i.elemType|0),i.shape!=null){if(typeof i.shape!="object")throw TypeError(".onnx.TypeProto.SparseTensor.shape: object expected");a.shape=f.onnx.TensorShapeProto.fromObject(i.shape)}return a},e.toObject=function(i,a){a||(a={});var s={};return a.defaults&&(s.elemType=0,s.shape=null),i.elemType!=null&&i.hasOwnProperty("elemType")&&(s.elemType=i.elemType),i.shape!=null&&i.hasOwnProperty("shape")&&(s.shape=f.onnx.TensorShapeProto.toObject(i.shape,a)),s},e.prototype.toJSON=function(){return this.constructor.toObject(this,j.util.toJSONOptions)},e.getTypeUrl=function(i){return i===void 0&&(i="type.googleapis.com"),i+"/onnx.TypeProto.SparseTensor"},e}(),t}(),r.OperatorSetIdProto=function(){function t(n){if(n)for(var e=Object.keys(n),o=0;o<e.length;++o)n[e[o]]!=null&&(this[e[o]]=n[e[o]])}return t.prototype.domain="",t.prototype.version=h.Long?h.Long.fromBits(0,0,!1):0,t.create=function(e){return new t(e)},t.encode=function(e,o){return o||(o=Z.create()),e.domain!=null&&Object.hasOwnProperty.call(e,"domain")&&o.uint32(10).string(e.domain),e.version!=null&&Object.hasOwnProperty.call(e,"version")&&o.uint32(16).int64(e.version),o},t.encodeDelimited=function(e,o){return this.encode(e,o).ldelim()},t.decode=function(e,o){e instanceof _||(e=_.create(e));for(var i=o===void 0?e.len:e.pos+o,a=new f.onnx.OperatorSetIdProto;e.pos<i;){var s=e.uint32();switch(s>>>3){case 1:{a.domain=e.string();break}case 2:{a.version=e.int64();break}default:e.skipType(s&7);break}}return a},t.decodeDelimited=function(e){return e instanceof _||(e=new _(e)),this.decode(e,e.uint32())},t.verify=function(e){return typeof e!="object"||e===null?"object expected":e.domain!=null&&e.hasOwnProperty("domain")&&!h.isString(e.domain)?"domain: string expected":e.version!=null&&e.hasOwnProperty("version")&&!h.isInteger(e.version)&&!(e.version&&h.isInteger(e.version.low)&&h.isInteger(e.version.high))?"version: integer|Long expected":null},t.fromObject=function(e){if(e instanceof f.onnx.OperatorSetIdProto)return e;var o=new f.onnx.OperatorSetIdProto;return e.domain!=null&&(o.domain=String(e.domain)),e.version!=null&&(h.Long?(o.version=h.Long.fromValue(e.version)).unsigned=!1:typeof e.version=="string"?o.version=parseInt(e.version,10):typeof e.version=="number"?o.version=e.version:typeof e.version=="object"&&(o.version=new h.LongBits(e.version.low>>>0,e.version.high>>>0).toNumber())),o},t.toObject=function(e,o){o||(o={});var i={};if(o.defaults)if(i.domain="",h.Long){var a=new h.Long(0,0,!1);i.version=o.longs===String?a.toString():o.longs===Number?a.toNumber():a}else i.version=o.longs===String?"0":0;return e.domain!=null&&e.hasOwnProperty("domain")&&(i.domain=e.domain),e.version!=null&&e.hasOwnProperty("version")&&(typeof e.version=="number"?i.version=o.longs===String?String(e.version):e.version:i.version=o.longs===String?h.Long.prototype.toString.call(e.version):o.longs===Number?new h.LongBits(e.version.low>>>0,e.version.high>>>0).toNumber():e.version),i},t.prototype.toJSON=function(){return this.constructor.toObject(this,j.util.toJSONOptions)},t.getTypeUrl=function(e){return e===void 0&&(e="type.googleapis.com"),e+"/onnx.OperatorSetIdProto"},t}(),r.OperatorStatus=function(){var t={},n=Object.create(t);return n[t[0]="EXPERIMENTAL"]=0,n[t[1]="STABLE"]=1,n}(),r.FunctionProto=function(){function t(n){if(this.input=[],this.output=[],this.attribute=[],this.attributeProto=[],this.node=[],this.opsetImport=[],n)for(var e=Object.keys(n),o=0;o<e.length;++o)n[e[o]]!=null&&(this[e[o]]=n[e[o]])}return t.prototype.name="",t.prototype.input=h.emptyArray,t.prototype.output=h.emptyArray,t.prototype.attribute=h.emptyArray,t.prototype.attributeProto=h.emptyArray,t.prototype.node=h.emptyArray,t.prototype.docString="",t.prototype.opsetImport=h.emptyArray,t.prototype.domain="",t.create=function(e){return new t(e)},t.encode=function(e,o){if(o||(o=Z.create()),e.name!=null&&Object.hasOwnProperty.call(e,"name")&&o.uint32(10).string(e.name),e.input!=null&&e.input.length)for(var i=0;i<e.input.length;++i)o.uint32(34).string(e.input[i]);if(e.output!=null&&e.output.length)for(var i=0;i<e.output.length;++i)o.uint32(42).string(e.output[i]);if(e.attribute!=null&&e.attribute.length)for(var i=0;i<e.attribute.length;++i)o.uint32(50).string(e.attribute[i]);if(e.node!=null&&e.node.length)for(var i=0;i<e.node.length;++i)f.onnx.NodeProto.encode(e.node[i],o.uint32(58).fork()).ldelim();if(e.docString!=null&&Object.hasOwnProperty.call(e,"docString")&&o.uint32(66).string(e.docString),e.opsetImport!=null&&e.opsetImport.length)for(var i=0;i<e.opsetImport.length;++i)f.onnx.OperatorSetIdProto.encode(e.opsetImport[i],o.uint32(74).fork()).ldelim();if(e.domain!=null&&Object.hasOwnProperty.call(e,"domain")&&o.uint32(82).string(e.domain),e.attributeProto!=null&&e.attributeProto.length)for(var i=0;i<e.attributeProto.length;++i)f.onnx.AttributeProto.encode(e.attributeProto[i],o.uint32(90).fork()).ldelim();return o},t.encodeDelimited=function(e,o){return this.encode(e,o).ldelim()},t.decode=function(e,o){e instanceof _||(e=_.create(e));for(var i=o===void 0?e.len:e.pos+o,a=new f.onnx.FunctionProto;e.pos<i;){var s=e.uint32();switch(s>>>3){case 1:{a.name=e.string();break}case 4:{a.input&&a.input.length||(a.input=[]),a.input.push(e.string());break}case 5:{a.output&&a.output.length||(a.output=[]),a.output.push(e.string());break}case 6:{a.attribute&&a.attribute.length||(a.attribute=[]),a.attribute.push(e.string());break}case 11:{a.attributeProto&&a.attributeProto.length||(a.attributeProto=[]),a.attributeProto.push(f.onnx.AttributeProto.decode(e,e.uint32()));break}case 7:{a.node&&a.node.length||(a.node=[]),a.node.push(f.onnx.NodeProto.decode(e,e.uint32()));break}case 8:{a.docString=e.string();break}case 9:{a.opsetImport&&a.opsetImport.length||(a.opsetImport=[]),a.opsetImport.push(f.onnx.OperatorSetIdProto.decode(e,e.uint32()));break}case 10:{a.domain=e.string();break}default:e.skipType(s&7);break}}return a},t.decodeDelimited=function(e){return e instanceof _||(e=new _(e)),this.decode(e,e.uint32())},t.verify=function(e){if(typeof e!="object"||e===null)return"object expected";if(e.name!=null&&e.hasOwnProperty("name")&&!h.isString(e.name))return"name: string expected";if(e.input!=null&&e.hasOwnProperty("input")){if(!Array.isArray(e.input))return"input: array expected";for(var o=0;o<e.input.length;++o)if(!h.isString(e.input[o]))return"input: string[] expected"}if(e.output!=null&&e.hasOwnProperty("output")){if(!Array.isArray(e.output))return"output: array expected";for(var o=0;o<e.output.length;++o)if(!h.isString(e.output[o]))return"output: string[] expected"}if(e.attribute!=null&&e.hasOwnProperty("attribute")){if(!Array.isArray(e.attribute))return"attribute: array expected";for(var o=0;o<e.attribute.length;++o)if(!h.isString(e.attribute[o]))return"attribute: string[] expected"}if(e.attributeProto!=null&&e.hasOwnProperty("attributeProto")){if(!Array.isArray(e.attributeProto))return"attributeProto: array expected";for(var o=0;o<e.attributeProto.length;++o){var i=f.onnx.AttributeProto.verify(e.attributeProto[o]);if(i)return"attributeProto."+i}}if(e.node!=null&&e.hasOwnProperty("node")){if(!Array.isArray(e.node))return"node: array expected";for(var o=0;o<e.node.length;++o){var i=f.onnx.NodeProto.verify(e.node[o]);if(i)return"node."+i}}if(e.docString!=null&&e.hasOwnProperty("docString")&&!h.isString(e.docString))return"docString: string expected";if(e.opsetImport!=null&&e.hasOwnProperty("opsetImport")){if(!Array.isArray(e.opsetImport))return"opsetImport: array expected";for(var o=0;o<e.opsetImport.length;++o){var i=f.onnx.OperatorSetIdProto.verify(e.opsetImport[o]);if(i)return"opsetImport."+i}}return e.domain!=null&&e.hasOwnProperty("domain")&&!h.isString(e.domain)?"domain: string expected":null},t.fromObject=function(e){if(e instanceof f.onnx.FunctionProto)return e;var o=new f.onnx.FunctionProto;if(e.name!=null&&(o.name=String(e.name)),e.input){if(!Array.isArray(e.input))throw TypeError(".onnx.FunctionProto.input: array expected");o.input=[];for(var i=0;i<e.input.length;++i)o.input[i]=String(e.input[i])}if(e.output){if(!Array.isArray(e.output))throw TypeError(".onnx.FunctionProto.output: array expected");o.output=[];for(var i=0;i<e.output.length;++i)o.output[i]=String(e.output[i])}if(e.attribute){if(!Array.isArray(e.attribute))throw TypeError(".onnx.FunctionProto.attribute: array expected");o.attribute=[];for(var i=0;i<e.attribute.length;++i)o.attribute[i]=String(e.attribute[i])}if(e.attributeProto){if(!Array.isArray(e.attributeProto))throw TypeError(".onnx.FunctionProto.attributeProto: array expected");o.attributeProto=[];for(var i=0;i<e.attributeProto.length;++i){if(typeof e.attributeProto[i]!="object")throw TypeError(".onnx.FunctionProto.attributeProto: object expected");o.attributeProto[i]=f.onnx.AttributeProto.fromObject(e.attributeProto[i])}}if(e.node){if(!Array.isArray(e.node))throw TypeError(".onnx.FunctionProto.node: array expected");o.node=[];for(var i=0;i<e.node.length;++i){if(typeof e.node[i]!="object")throw TypeError(".onnx.FunctionProto.node: object expected");o.node[i]=f.onnx.NodeProto.fromObject(e.node[i])}}if(e.docString!=null&&(o.docString=String(e.docString)),e.opsetImport){if(!Array.isArray(e.opsetImport))throw TypeError(".onnx.FunctionProto.opsetImport: array expected");o.opsetImport=[];for(var i=0;i<e.opsetImport.length;++i){if(typeof e.opsetImport[i]!="object")throw TypeError(".onnx.FunctionProto.opsetImport: object expected");o.opsetImport[i]=f.onnx.OperatorSetIdProto.fromObject(e.opsetImport[i])}}return e.domain!=null&&(o.domain=String(e.domain)),o},t.toObject=function(e,o){o||(o={});var i={};if((o.arrays||o.defaults)&&(i.input=[],i.output=[],i.attribute=[],i.node=[],i.opsetImport=[],i.attributeProto=[]),o.defaults&&(i.name="",i.docString="",i.domain=""),e.name!=null&&e.hasOwnProperty("name")&&(i.name=e.name),e.input&&e.input.length){i.input=[];for(var a=0;a<e.input.length;++a)i.input[a]=e.input[a]}if(e.output&&e.output.length){i.output=[];for(var a=0;a<e.output.length;++a)i.output[a]=e.output[a]}if(e.attribute&&e.attribute.length){i.attribute=[];for(var a=0;a<e.attribute.length;++a)i.attribute[a]=e.attribute[a]}if(e.node&&e.node.length){i.node=[];for(var a=0;a<e.node.length;++a)i.node[a]=f.onnx.NodeProto.toObject(e.node[a],o)}if(e.docString!=null&&e.hasOwnProperty("docString")&&(i.docString=e.docString),e.opsetImport&&e.opsetImport.length){i.opsetImport=[];for(var a=0;a<e.opsetImport.length;++a)i.opsetImport[a]=f.onnx.OperatorSetIdProto.toObject(e.opsetImport[a],o)}if(e.domain!=null&&e.hasOwnProperty("domain")&&(i.domain=e.domain),e.attributeProto&&e.attributeProto.length){i.attributeProto=[];for(var a=0;a<e.attributeProto.length;++a)i.attributeProto[a]=f.onnx.AttributeProto.toObject(e.attributeProto[a],o)}return i},t.prototype.toJSON=function(){return this.constructor.toObject(this,j.util.toJSONOptions)},t.getTypeUrl=function(e){return e===void 0&&(e="type.googleapis.com"),e+"/onnx.FunctionProto"},t}(),r}();_u.exports=f});function je(r,t){if(!r)throw new Error(typeof t=="string"?t:t())}function cn(r){return new TextDecoder().decode(r)}var W,Le,Ti,ot,Jn,nt,ct,O,ln,$e,Ne,Fe,V=y(()=>{"use strict";Vr();W=E(Ue());Ce();Le=class{static arraysEqual(t,n){if(t.length!==n.length)return!1;for(let e=0;e<t.length;e++)if(t[e]!==n[e])return!1;return!0}},Ti=class{static preprocessInputShapes(t,n){let e=t.length===1?[1,t[0]]:t,o=n.length===1?[n[0],1]:n;return[e,o]}static postprocessOutputShape(t,n,e){n===1&&t.splice(t.length-2,1),e===1&&t.pop()}static calcMatMulShape(t,n){return t[1]!==n[0]?void 0:[t[0],n[1]]}},ot=class r{static calcShape(t,n,e=!1){let o=t.length,i=n.length;if(o===0)return n;if(i===0)return t;let a=Math.max(t.length,n.length),s=new Array(a);if(e){if(o<2||i<2)return;let u=Ti.calcMatMulShape([t[o-2],t[o-1]],[n[i-2],n[i-1]]);if(u===void 0)return;[s[a-2],s[a-1]]=u}for(let u=e?3:1;u<=a;u++){let l=o-u<0?1:t[o-u],c=i-u<0?1:n[i-u];if(l!==c&&l>1&&c>1)return;s[a-u]=Math.max(l,c)}return s}static index(t,n){let e=new Array(n.length);return r.fillIndex(t,n,e),e}static fillIndex(t,n,e){let o=t.length-n.length;for(let i=0;i<n.length;i++)e[i]=t[o+i]%n[i]}static calc(t,n,e,o,i){let a=r.calcShape(t.dims,n.dims);if(a){if(o&&!O.areEqual(a,t.dims))return;let s=O.size(a),u=o?t:new Y(a,i||t.type);if(a.length===0)u.set([],e(t.get([]),n.get([])));else{let l=new Array(a.length),c=new Array(t.dims.length),d=new Array(n.dims.length),p=0,m=0,b=!1,g=!1;t.dims.length===0&&(p=t.get([]),b=!0),n.dims.length===0&&(m=n.get([]),g=!0);let x;for(let w=0;w<s;w++){x=w;for(let I=a.length-1;I>=0;I--)l[I]=x%a[I],x=Math.floor(x/a[I]);b||(r.fillIndex(l,t.dims,c),p=t.get(c)),g||(r.fillIndex(l,n.dims,d),m=n.get(d)),u.set(l,e(p,m))}}return u}}static isValidBroadcast(t,n){let e=t.length,o=n.length;if(e>o)return!1;for(let i=1;i<=e;i++)if(t[e-i]!==1&&t[e-i]!==n[o-i])return!1;return!0}static getBroadcastDims(t,n){let e=t.length,o=[];for(let i=0;i<e;i++){let a=e-1-i,s=t[a]||1;(n[n.length-1-i]||1)>1&&s===1&&o.unshift(a)}return o}},Jn=class{static getShapeOfGemmResult(t,n,e,o,i){if(t.length!==2||e.length!==2)throw new Error("shape need to be of size 2");let a,s,u;n?(a=t[1],s=t[0]):(a=t[0],s=t[1]);let l=-1;if(o?(u=e[0],l=1):(u=e[1],l=0),e[l]!==s)throw new Error("dimension mismatch");if(a<=0||u<=0||s<=0)throw new Error("invalid shape specified");if(i&&!ot.isValidBroadcast(i,[a,u]))throw new Error("gemm: invalid bias shape for broadcast");return[a,u,s]}},nt=class r{static tensorDataTypeFromProto(t){switch(t){case W.onnx.TensorProto.DataType.INT8:return"int8";case W.onnx.TensorProto.DataType.UINT8:return"uint8";case W.onnx.TensorProto.DataType.BOOL:return"bool";case W.onnx.TensorProto.DataType.INT16:return"int16";case W.onnx.TensorProto.DataType.UINT16:return"uint16";case W.onnx.TensorProto.DataType.INT32:return"int32";case W.onnx.TensorProto.DataType.UINT32:return"uint32";case W.onnx.TensorProto.DataType.FLOAT:return"float32";case W.onnx.TensorProto.DataType.DOUBLE:return"float64";case W.onnx.TensorProto.DataType.STRING:return"string";case W.onnx.TensorProto.DataType.INT64:return"int32";case W.onnx.TensorProto.DataType.UINT64:return"uint32";default:throw new Error(`unsupported data type: ${W.onnx.TensorProto.DataType[t]}`)}}static tensorDataTypeStringToEnum(t){switch(t){case"int8":return W.onnx.TensorProto.DataType.INT8;case"uint8":return W.onnx.TensorProto.DataType.UINT8;case"bool":return W.onnx.TensorProto.DataType.BOOL;case"int16":return W.onnx.TensorProto.DataType.INT16;case"uint16":return W.onnx.TensorProto.DataType.UINT16;case"int32":return W.onnx.TensorProto.DataType.INT32;case"uint32":return W.onnx.TensorProto.DataType.UINT32;case"float32":return W.onnx.TensorProto.DataType.FLOAT;case"float64":return W.onnx.TensorProto.DataType.DOUBLE;case"string":return W.onnx.TensorProto.DataType.STRING;case"int64":return W.onnx.TensorProto.DataType.INT64;case"uint64":return W.onnx.TensorProto.DataType.UINT64;default:throw new Error(`unsupported data type: ${t}`)}}static tensorDimsFromProto(t){return t.map(n=>ge.isLong(n)?n.toNumber():n)}static tensorValueTypeFromProto(t){return{tensorType:r.tensorDataTypeFromProto(t.elemType),shape:{dims:r.tensorDimsFromProto(t.shape.dim.map(n=>n.dimValue))}}}static tensorDimsFromORTFormat(t){let n=[];for(let e=0;e<t.dimsLength();e++)n.push(ct.longToNumber(t.dims(e)));return n}static tensorAttributesFromORTFormat(t){let n=[];for(let e=0;e<t.attributesLength();e++)n.push(t.attributes(e));return n}},ct=class{static longToNumber(t){return ge.isLong(t)?t.toNumber():typeof t=="bigint"?Number(t):t}static isLong(t){return ge.isLong(t)||typeof t=="bigint"}},O=class r{static size(t){return r.getSizeFromDimensionRange(t,0,t.length)}static sizeFromDimension(t,n){if(n<0||n>t.length)throw new Error(`invalid dimension of ${n} for sizeFromDimension as Tensor has ${t.length} dimensions.`);return r.getSizeFromDimensionRange(t,n,t.length)}static sizeToDimension(t,n){if(n<0||n>t.length)throw new Error(`invalid dimension of ${n} for sizeToDimension as Tensor has ${t.length} dimensions.`);return r.getSizeFromDimensionRange(t,0,n)}static getSizeFromDimensionRange(t,n,e){let o=1;for(let i=n;i<e;i++){if(t[i]<=0)throw new Error("cannot get valid size from specified dimension range. Most likely the range contains 0 or negative values in them.");o*=t[i]}return o}static computeStrides(t){let n=t.length;if(n===0)return[];if(n===1)return[1];let e=new Array(n);e[n-1]=1,e[n-2]=t[n-1];for(let o=n-3;o>=0;--o)e[o]=e[o+1]*t[o+1];return e}static transpose(t){return t.slice().reverse()}static indicesToOffset(t,n,e){e===void 0&&(e=t.length);let o=0;for(let i=0;i<e;++i)o+=n[i]*t[i];return o}static offsetToIndices(t,n){let e=n.length;if(e===0)return[];if(e===1)return[t*n[0]];let o=new Array(n.length);for(let i=0;i<o.length-1;++i)o[i]=Math.floor(t/n[i]),t-=o[i]*n[i];return o[o.length-1]=t,o}static normalizeAxis(t,n){if(t<-n&&t>=n)throw new Error("unsupported axis for this operation.");return t<0?t+n:t}static normalizeAxes(t,n){return t.map(e=>this.normalizeAxis(e,n))}static incrementIndex(t,n,e){if(n.length===0||t.length===0)throw new Error("Index incrementing unsupported for scalar Tensor");if(e===void 0)e=n.length;else if(e<=0||e>n.length)throw new Error("Incorrect axis to increment on");for(let o=e-1;o>=0&&(t[o]++,!(t[o]<n[o]));--o)t[o]=0}static calculateReshapedDims(t,n){if(n.length===0){if(t.length===0||r.size(t)===1)return[];throw new Error("cannot reshape to a scalar Tensor")}let e=n.length,o=new Array(e),i=-1,a=1;for(let u=0;u<e;u++){if(n[u]<-1)throw new Error("a dimension in shape hints cannot be less than -1");if(n[u]===-1){if(i!==-1)throw new Error("at most one dimension in shape hints can be -1");i=u}else{if(n[u]===0){if(u>=t.length)throw new Error("the dimension with value zero exceeds the dimension size of the input tensor");o[u]=t[u]}else o[u]=n[u];a*=o[u]}}let s=r.size(t);if(i!==-1){if(s%a!==0)throw new Error(`the input tensor cannot be reshaped to the requested shape. Input shape: [${t}] Output shape: [${n}]`);o[i]=s/a}else if(a!==s)throw new Error("reshapedDims and originalDims don't have matching sizes");return o}static sortBasedOnPerm(t,n){return n?n.map(e=>t[e]):t.slice().reverse()}static padShape(t,n){let e=t.length;return t.map((o,i)=>o+n[i]+n[i+e])}static areEqual(t,n){return t.length!==n.length?!1:t.every((e,o)=>e===n[o])}static validateDimsAndCalcSize(t){if(t.length>6)throw new TypeError("Only rank 0 to 6 is supported for tensor shape.");let n=1;for(let e of t){if(!Number.isInteger(e))throw new TypeError(`Invalid shape: ${e} is not an integer`);if(e<0||e>2147483647)throw new TypeError(`Invalid shape: length ${e} is not allowed`);n*=e}return n}static flattenShape(t,n){n<0&&(n+=t.length);let e=t.reduce((a,s)=>a*s,1),o=t.slice(n).reduce((a,s)=>a*s,1);return[e/o,o]}static squeezeShape(t,n){let e=new Array;n=r.normalizeAxes(n,t.length);for(let o=0;o<t.length;o++){let i=n.indexOf(o)>=0;if(i&&t[o]!==1)throw new Error("squeeze an axis of size different than 1");(n.length===0&&t[o]>1||n.length>0&&!i)&&e.push(t[o])}return e}static unsqueezeShape(t,n){let e=new Array(t.length+n.length);e.fill(0);for(let i=0;i<n.length;i++){let a=r.normalizeAxis(n[i],e.length);if(a>=e.length)throw new Error("'axes' has an out of range axis");if(e[a]!==0)throw new Error("'axes' has a duplicate axis");e[a]=1}let o=0;for(let i=0;i<e.length;i++)e[i]===0&&(e[i]=t[o++]);if(o!==t.length)throw new Error("the unsqueezed dimension could not be established");return e}},ln=class r{static splitShape(t,n,e,o){if(e.length===0){if(!o)throw new Error("need to know number of outputs when the 'split' attribute is not specified");r.determineSplit(t[n],o,e)}let i=[],a=[0];for(let s=0;s<e.length;++s){s!==0&&a.push(a[s-1]+e[s-1]);let u=t.slice();u[n]=e[s],i.push(u)}return[i,a]}static determineSplit(t,n,e){if(t%n!==0)throw new Error("cannot split tensor to equal sized parts");for(let o=0;o<n;++o)e.push(t/n)}},$e=class r{static adjustPoolAttributes(t,n,e,o,i,a){if(!t&&e.length!==n.length-2)throw new Error("length of specified kernel shapes should be 2 less than length of input dimensions");if(t)for(let s=0;s<n.length-2;s++)s>=e.length?e.push(n[s+2]):e[s]=n[s+2];for(let s=0;s<e.length;s++)if(s<o.length){if(o[s]<0)throw new Error("strides should be greater than or equal to 1")}else o.push(1);for(let s=0;s<e.length;s++)if(s<i.length){if(i[s]<0)throw new Error("dilations should be greater than or equal to 1")}else i.push(1);for(let s=0;s<e.length*2;s++)if(s<a.length){if(a[s]<0)throw new Error("pad should be greater than or equal to 1")}else a.push(0);for(let s=0;s<e.length;s++){if(e[s]<=0)throw new Error("kernel shapes need to be greater than 0");if(a[s]>=e[s]||a[s+e.length]>=e[s])throw new Error("pads should be smaller than kernel")}}static adjustPadsBasedOnAutoPad(t,n,e,o,i,a){if(a){if(i.length!==2*(t.length-2))throw new Error("length of pads should be twice the length of data dimensions");if(n.length!==t.length-2)throw new Error("length of strides should be the length of data dimensions");if(o.length!==t.length-2)throw new Error("length of kernel shapes should be the length of data dimensions");for(let s=0;s<t.length-2;s++)r.adjustPadAndReturnShape(t[s+2],n[s],e[s],o[s],i,s,s+t.length-2,a)}}static computePoolOutputShape(t,n,e,o,i,a,s){if(n.length<=0)throw new Error("input shape must be of size greater than 0");let u=[n[0],n[1]];return r.computeShapeHelper(t,n,u,e,o,i,a,s),u}static computeConvOutputShape(t,n,e,o,i,a,s){if(t.length<=0||n.length<=0)throw new Error("invalid input tensor dims or invalid filter tensor dims");let u=[t[0],n[0]];return r.computeShapeHelper(!1,t,u,e,o,i,a,s),u}static computeShapeHelper(t,n,e,o,i,a,s,u){if(t)for(let l=0;l<n.length-2;l++)e.push(1);else for(let l=0;l<n.length-2;l++)e.push(r.adjustPadAndReturnShape(n[l+2],o[l],i[l],a[l],s,l,l+n.length-2,u))}static adjustPadAndReturnShape(t,n,e,o,i,a,s,u){let l=e*(o-1)+1;if(u&&u!=="NOTSET")switch(u){case"VALID":return i[a]=0,i[s]=0,Math.floor((t-l)/n+1);case"SAME_LOWER":case"SAME_UPPER":if(e!==1)throw new Error("Dilation not supported for SAME_UPPER or SAME_LOWER");{let d=((t+n-1)/n-1)*n+o-t;return i[a]=Math.floor(u==="SAME_LOWER"?(d+1)/2:d/2),i[s]=d-i[a],Math.floor((t+d-o)/n+1)}default:throw new Error("Unsupported AutoPad type")}else return Math.floor((t+i[a]+i[s]-l)/n+1)}},Ne=-34028234663852886e22,Fe=34028234663852886e22});function ab(r){switch(r){case"bool":case"int8":case"uint8":return 1;case"int16":case"uint16":return 2;case"int32":case"uint32":case"float32":return 4;case"float64":return 8;default:throw new Error(`cannot calculate sizeof() on type ${r}`)}}function xu(r){switch(r){case $.onnx.TensorProto.DataType.UINT8:case $.onnx.TensorProto.DataType.INT8:case $.onnx.TensorProto.DataType.BOOL:return 1;case $.onnx.TensorProto.DataType.UINT16:case $.onnx.TensorProto.DataType.INT16:return 2;case $.onnx.TensorProto.DataType.FLOAT:case $.onnx.TensorProto.DataType.INT32:case $.onnx.TensorProto.DataType.UINT32:return 4;case $.onnx.TensorProto.DataType.INT64:case $.onnx.TensorProto.DataType.DOUBLE:case $.onnx.TensorProto.DataType.UINT64:return 8;default:throw new Error(`cannot calculate sizeof() on type ${$.onnx.TensorProto.DataType[r]}`)}}function sb(r,t){return new(Iu(t))(r)}function Iu(r){switch(r){case"bool":case"uint8":return Uint8Array;case"int8":return Int8Array;case"int16":return Int16Array;case"uint16":return Uint16Array;case"int32":return Int32Array;case"uint32":return Uint32Array;case"int64":return BigInt64Array;case"float32":return Float32Array;case"float64":return Float64Array;default:throw new Error("unspecified error")}}function _i(r,t){if(t===$.onnx.TensorProto.DataType.INT64||t===rn.TensorDataType.INT64){if(r.greaterThanOrEqual(2147483648)||r.lessThan(-2147483648))throw new TypeError("int64 is not supported")}else if(t===$.onnx.TensorProto.DataType.UINT32||t===rn.TensorDataType.UINT32||t===$.onnx.TensorProto.DataType.UINT64||t===rn.TensorDataType.UINT64){if(r.greaterThanOrEqual(4294967296)||r.lessThan(0))throw new TypeError("uint64 is not supported")}else throw new TypeError(`not a LONG type: ${$.onnx.TensorProto.DataType[t]}`);return r.toNumber()}function vu(r,t,n){switch(t){case $.onnx.TensorProto.DataType.BOOL:case $.onnx.TensorProto.DataType.UINT8:return r.getUint8(n);case $.onnx.TensorProto.DataType.INT8:return r.getInt8(n);case $.onnx.TensorProto.DataType.UINT16:return r.getUint16(n,!0);case $.onnx.TensorProto.DataType.INT16:return r.getInt16(n,!0);case $.onnx.TensorProto.DataType.FLOAT:return r.getFloat32(n,!0);case $.onnx.TensorProto.DataType.INT32:return r.getInt32(n,!0);case $.onnx.TensorProto.DataType.UINT32:return r.getUint32(n,!0);case $.onnx.TensorProto.DataType.INT64:return _i(ge.fromBits(r.getUint32(n,!0),r.getUint32(n+4,!0),!1),t);case $.onnx.TensorProto.DataType.DOUBLE:return r.getFloat64(n,!0);case $.onnx.TensorProto.DataType.UINT64:return _i(ge.fromBits(r.getUint32(n,!0),r.getUint32(n+4,!0),!0),t);default:throw new Error(`cannot read from DataView for type ${$.onnx.TensorProto.DataType[t]}`)}}var wu,$,Y,Ce=y(()=>{"use strict";wu=E(Va());Vr();on();$=E(Ue());V();Y=class r{constructor(t,n,e,o,i,a=wu.Guid.create()){this.dims=t;this.type=n;this.dataProvider=e;this.asyncDataProvider=o;this.cache=i;this.dataId=a;this.size=O.validateDimsAndCalcSize(t);let s=this.size,u=e===void 0&&o===void 0&&i===void 0;if(i!==void 0&&i.length!==s)throw new RangeError("Input dims doesn't match data length.");if(n==="string"){if(i!==void 0&&(!Array.isArray(i)||!i.every(l=>typeof l=="string")))throw new TypeError("cache should be a string array");u&&(this.cache=new Array(s))}else{if(i!==void 0){let l=Iu(n);if(!(i instanceof l))throw new TypeError(`cache should be type ${l.name}`)}if(u){let l=new ArrayBuffer(s*ab(n));this.cache=sb(l,n)}}}get data(){if(this.cache===void 0){let t=this.dataProvider(this.dataId);if(t.length!==this.size)throw new Error("Length of data provided by the Data Provider is inconsistent with the dims of this Tensor.");this.cache=t}return this.cache}get stringData(){if(this.type!=="string")throw new TypeError("data type is not string");return this.data}get integerData(){switch(this.type){case"uint8":case"int8":case"uint16":case"int16":case"int32":case"uint32":case"bool":return this.data;default:throw new TypeError("data type is not integer (uint8, int8, uint16, int16, int32, uint32, bool)")}}get floatData(){switch(this.type){case"float32":case"float64":return this.data;default:throw new TypeError("data type is not float (float32, float64)")}}get numberData(){if(this.type!=="string")return this.data;throw new TypeError("type cannot be non-number (string)")}get(t){return this.data[O.indicesToOffset(t,this.strides)]}set(t,n){this.data[O.indicesToOffset(t,this.strides)]=n}async getData(){return this.cache===void 0&&(this.cache=await this.asyncDataProvider(this.dataId)),this.cache}get strides(){return this._strides||(this._strides=O.computeStrides(this.dims)),this._strides}static fromProto(t){if(!t)throw new Error("cannot construct Value from an empty tensor");let n=nt.tensorDataTypeFromProto(t.dataType),e=nt.tensorDimsFromProto(t.dims),o=new r(e,n);if(n==="string")t.stringData.forEach((i,a)=>{o.data[a]=cn(i)});else if(t.rawData&&typeof t.rawData.byteLength=="number"&&t.rawData.byteLength>0){let i=o.data,a=new DataView(t.rawData.buffer,t.rawData.byteOffset,t.rawData.byteLength),s=xu(t.dataType),u=t.rawData.byteLength/s;if(t.rawData.byteLength%s!==0)throw new Error("invalid buffer length");if(i.length!==u)throw new Error("buffer length mismatch");for(let l=0;l<u;l++){let c=vu(a,t.dataType,l*s);i[l]=c}}else{let i;switch(t.dataType){case $.onnx.TensorProto.DataType.FLOAT:i=t.floatData;break;case $.onnx.TensorProto.DataType.INT32:case $.onnx.TensorProto.DataType.INT16:case $.onnx.TensorProto.DataType.UINT16:case $.onnx.TensorProto.DataType.INT8:case $.onnx.TensorProto.DataType.UINT8:case $.onnx.TensorProto.DataType.BOOL:i=t.int32Data;break;case $.onnx.TensorProto.DataType.INT64:i=t.int64Data;break;case $.onnx.TensorProto.DataType.DOUBLE:i=t.doubleData;break;case $.onnx.TensorProto.DataType.UINT32:case $.onnx.TensorProto.DataType.UINT64:i=t.uint64Data;break;default:throw new Error("unspecific error")}if(i==null)throw new Error("failed to populate data from a tensorproto value");let a=o.data;if(a.length!==i.length)throw new Error("array length mismatch");for(let s=0;s<i.length;s++){let u=i[s];ge.isLong(u)?a[s]=_i(u,t.dataType):a[s]=u}}return o}static fromData(t,n,e){return new r(n,e,void 0,void 0,t)}static fromOrtTensor(t){if(!t)throw new Error("cannot construct Value from an empty tensor");let n=nt.tensorDimsFromORTFormat(t),e=nt.tensorDataTypeFromProto(t.dataType()),o=new r(n,e);if(e==="string")for(let i=0;i<t.stringDataLength();i++)o.data[i]=t.stringData(i);else if(t.rawDataArray()&&typeof t.rawDataLength()=="number"&&t.rawDataLength()>0){let i=o.data,a=new DataView(t.rawDataArray().buffer,t.rawDataArray().byteOffset,t.rawDataLength()),s=xu(t.dataType()),u=t.rawDataLength()/s;if(t.rawDataLength()%s!==0)throw new Error("invalid buffer length");if(i.length!==u)throw new Error("buffer length mismatch");for(let l=0;l<u;l++){let c=vu(a,t.dataType(),l*s);i[l]=c}}return o}}});function A(r){return r===1?ub:lb}function Ou(r){let t=A(r);return`${t.version}
      precision highp float;
      ${t.attribute} vec3 position;
      ${t.attribute} vec2 textureCoord;

      ${t.varyingVertex} vec2 TexCoords;

      void main()
      {
          gl_Position = vec4(position, 1.0);
          TexCoords = textureCoord;
      }`}function Pu(r){let t=A(r);return`${t.version}
    precision highp float;
    precision highp int;
    precision highp sampler2D;
    ${t.varyingFrag} vec2 TexCoords;
    ${t.outputDeclaration}
    const vec2 halfCR = vec2(0.5, 0.5);

    // Custom vector types to handle higher dimenalities.
    struct ivec5
    {
      int x;
      int y;
      int z;
      int w;
      int u;
    };

    struct ivec6
    {
      int x;
      int y;
      int z;
      int w;
      int u;
      int v;
    };

    int imod(int x, int y) {
      return x - y * (x / y);
    }

    `}function Su(r,t){let n=A(r);return`
  void main() {
    int indices[${t}];
    toVec(TexCoords, indices);
    vec4 result = vec4(process(indices));
    ${n.output} = result;
  }
  `}var ub,lb,q=y(()=>{"use strict";ub={version:"",attribute:"attribute",varyingVertex:"varying",varyingFrag:"varying",texture2D:"texture2D",output:"gl_FragColor",outputDeclaration:""},lb={version:"#version 300 es",attribute:"in",varyingVertex:"out",varyingFrag:"in",texture2D:"texture",output:"outputColor",outputDeclaration:"out vec4 outputColor;"}});var C=y(()=>{"use strict"});async function xi(r,t=e=>0,n){return new Promise((e,o)=>{let i=0,a=()=>{if(r()){e();return}i++;let s=t(i);if(n!=null&&i>=n){o();return}setTimeout(a,s)};a()})}function Yn(r){return je(typeof r<"u"&&r.length!==0,()=>"empty string found for sampler name"),"get"+r.charAt(0).toUpperCase()+r.slice(1)}function Au(r){return je(typeof r<"u"&&r.length!==0,()=>"empty string found for sampler name"),"get"+r.charAt(0).toUpperCase()+r.slice(1)+"AtOutCoords"}function We(r,t){let n=JSON.parse(JSON.stringify(r));return n=t,n}function He(r,t){return t.map(n=>r[n]).join(", ")}function it(r){if(r<=1)return"int";if(r===2)return"ivec2";if(r===3)return"ivec3";if(r===4)return"ivec4";if(r===5)return"ivec5";if(r===6)return"ivec6";throw Error(`GPU for rank ${r} is not yet supported`)}function St(r=6){return["x","y","z","w","u","v"].slice(0,r)}var ue=y(()=>{"use strict";V()});function cb(r,t){return St(t).map(n=>`${r}.${n}`)}function qe(r,t){return t===1?[r]:cb(r,t)}function le(){return`
    float getChannel(vec4 frag, int dim) {
      int modCoord = imod(dim, 2);
      return modCoord == 0 ? frag.r : frag.g;
    }

    float getChannel(vec4 frag, vec2 innerDims) {
      vec2 modCoord = mod(innerDims, 2.);
      return modCoord.x == 0. ?
        (modCoord.y == 0. ? frag.r : frag.g) :
        (modCoord.y == 0. ? frag.b : frag.a);
    }
  `}var Re=y(()=>{"use strict";ue()});function db(r,t,n){if(r===0)return"false";if(r===1)return`rc > ${t[0]}`;let e="";for(let o=r-2;o<r;o++)e+=`${n[o]} >= ${t[o-r+2]}`,o<r-1&&(e+="||");return e}function pb(r,t){let n=r.length;if(n===0)return"getA(), 0, 0, 0";if(n===1)return`getA(rc),
            rc + 1 >= ${r[0]} ? 0. : getA(rc + 1),
            0, 0`;let e="r, c",o="r, cp1",i="rp1, c",a="rp1, cp1",s="";if(n>2)for(let u=0;u<n-2;++u)s=s+`${t[u]},`;return`getA(${s}${e}),
          rEdge ? 0. : getA(${s}${i}),
          cEdge ? 0. : getA(${s}${o}),
          rEdge || cEdge ? 0. : getA(${s}${a})`}function hb(r,t,n,e){return r===0||r===1?"":`
    int r = ${t[r-2]};
    int c = ${t[r-1]};
    int rp1 = ${t[r-2]} + 1;
    int cp1 = ${t[r-1]} + 1;
    bool rEdge = rp1 >= ${e};
    bool cEdge = cp1 >= ${n};
    `}var Eu,fb,Du,Lu=y(()=>{"use strict";q();C();ue();Re();Eu={name:"pack",inputNames:["A"],inputTypes:[1]},fb=(r,t)=>{let n=A(r.session.backend.glContext.version),e=t.dims,o=e.length,i=t.dims.length,a=it(i),s=qe("rc",i),u=hb(i,s,e[e.length-2],e[e.length-1]),l;o===0?l=[1,1]:o===1?l=[e[0],1]:l=[e[i-1],e[i-2]];let c=db(i,l,s),d=pb(e,s),p=`
        void main() {
          ${a} rc = getOutputCoords();

          if(${c}) {
            ${n.output} = vec4(0);
          } else {
            ${u}

            ${n.output} = vec4(${d});
          }
        }
      `;return{...Eu,hasMain:!0,output:{dims:t.dims,type:t.type,textureType:2},shaderSource:p}},Du=(r,t)=>({...Eu,get:()=>fb(r,t)})});function vi(r){if(r.length===0)return[1,1,1];let t=1;for(let n=0;n<r.length-2;++n)t*=r[n];return[t,r.length>1?r[r.length-2]:1,r[r.length-1]]}function Nu(r,t){let n=!1;return r.length===0||t.length===0?n=!0:r.length<2||t.length<2?n=r[r.length-1]===t[t.length-1]:n=r[r.length-1]===t[t.length-1]&&r[r.length-2]===t[t.length-2],n}function gb(r){let t=O.computeStrides(r),n=["b","r","c"],e="index";return`
    ivec3 inputCoordsFromReshapedOutCoords(int index) {
      ${t.map((i,a)=>{let s=`int ${n[a]} = ${e} / ${i}`,u=a===t.length-1?`int ${n[a+1]} = ${e} - ${n[a]} * ${i}`:`index -= ${n[a]} * ${i}`;return`${s}; ${u};`}).join("")}
      return ivec3(b, r, c);
    }
  `}function yb(r){let t=O.computeStrides(r);return`
  int getFlattenedIndex(ivec3 coords) {
    // reverse y, z order
    return coords.x * ${t[0]} + coords.z * ${t[1]} + coords.y;
  }
`}var mb,bb,$u,Fu=y(()=>{"use strict";V();q();C();Re();mb=r=>({name:"Reshape (packed)",inputTypes:[2],inputNames:["A"],cacheHint:`${r}`}),bb=(r,t,n,e)=>{let o=t.dims,i=e,a="";for(let l=0;l<4;l++){let c="";switch(l){case 0:c="outputCoords = rc;";break;case 1:c="outputCoords = ivec3(rc.x, rc.y+1, rc.z);";break;case 2:c="outputCoords = ivec3(rc.x, rc.y, rc.z+1);";break;case 3:c="outputCoords = ivec3(rc.x, rc.y+1, rc.z+1);";break;default:throw new Error}a+=`
        ${c}
        ${l>0?"if(outputCoords.y < rows && outputCoords.z < cols){":""}
          int flattenedIndex = getFlattenedIndex(outputCoords);

          ivec3 inputRC = inputCoordsFromReshapedOutCoords(flattenedIndex);
          vec2 innerDims = vec2(float(inputRC.y),float(inputRC.z));

          result[${l}] = getChannel(getA(inputRC.x, inputRC.y, inputRC.z), innerDims);

        ${l>0?"}":""}
      `}let s=A(r.session.backend.glContext.version),u=`
      ${gb(o)}
      ${yb(i)}
      ${le()}

      void main() {
        ivec3 rc = getOutputCoords();

        vec4 result = vec4(0.0);

        ivec3 outputCoords;
        int rows = ${i[2]};
        int cols = ${i[1]};

        ${a}
        ${s.output} = result;
      }
    `;return{...n,output:{dims:i,type:t.type,textureType:2},shaderSource:u,hasMain:!0}},$u=(r,t,n)=>{let e=mb(n);return{...e,get:()=>bb(r,t,e,n)}}});var wi,Cu=y(()=>{"use strict";q();C();wi=(r,t)=>{let n=t.shape,e=A(r.session.backend.glContext.version),o=`
    const float FLOAT_MAX = 1.70141184e38;
    const float FLOAT_MIN = 1.17549435e-38;

    bool isNaN(float val) {
      return (val < 1.0 || 0.0 < val || val == 0.0) ? false : true;
    }

    highp vec4 encodeAsUint8(highp float v) {
      if (isNaN(v)) {
        return vec4(255, 255, 255, 255);
      }

      highp float av = abs(v);

      if(av < FLOAT_MIN) {
        return vec4(0.0, 0.0, 0.0, 0.0);
      } else if(v > FLOAT_MAX) {
        return vec4(0.0, 0.0, 128.0, 127.0) / 255.0;
      } else if(v < -FLOAT_MAX) {
        return vec4(0.0, 0.0,  128.0, 255.0) / 255.0;
      }

      highp vec4 c = vec4(0,0,0,0);

      highp float e = floor(log2(av));
      highp float m = exp2(fract(log2(av))) - 1.0;

      c[2] = floor(128.0 * m);
      m -= c[2] / 128.0;
      c[1] = floor(32768.0 * m);
      m -= c[1] / 32768.0;
      c[0] = floor(8388608.0 * m);

      highp float ebias = e + 127.0;
      c[3] = floor(ebias / 2.0);
      ebias -= c[3] * 2.0;
      c[2] += floor(ebias) * 128.0;

      c[3] += 128.0 * step(0.0, -v);

      return c / 255.0;
    }

    void main() {
      float value = ${e.texture2D}(X,TexCoords).r;
      ${e.output} = encodeAsUint8(value);
    }`,i={name:"Uint8Encode",inputTypes:[0],inputNames:["X"],output:{dims:n,type:t.tensor.type,textureType:3},shaderSource:o,hasMain:!0};return r.executeProgram(i,[t.tensor])}});function _b(r,t){if(r===1)return"rc";let n="";for(let e=0;e<r;e++)n+=t[e],e<r-1&&(n+=",");return n}var Ru,Tb,Gu,ku=y(()=>{"use strict";q();C();ue();Re();Ru={name:"unpack",inputNames:["A"],inputTypes:[2]},Tb=(r,t)=>{let n=t.dims.length,e=qe("rc",n),o=e.slice(-2),i=it(n),a=le(),u=t.dims.length===0?"":_b(n,e),l=n<=1?"rc":`vec2(${o.join(",")})`,c=A(r.session.backend.glContext.version),d=`
    ${a}
    void main() {
      ${i} rc = getOutputCoords();

       // Sample the texture with the coords to get the rgba channel value.
       vec4 packedInput = getA(${u});

       ${c.output} = vec4(getChannel(packedInput, ${l}), 0, 0, 0);
     }
   `;return{...Ru,hasMain:!0,output:{dims:t.dims,type:t.type,textureType:0},shaderSource:d}},Gu=(r,t)=>({...Ru,get:()=>Tb(r,t)})});var Qn,fn,tr,dn=y(()=>{"use strict";pt();Qn=class{constructor(t,n=1){if(n===1)this.internalFormat=t.R32F,this.format=t.RED,this.textureType=t.FLOAT,this.channelSize=n;else if(n===4)this.internalFormat=t.RGBA32F,this.format=t.RGBA,this.textureType=t.FLOAT,this.channelSize=n;else throw new Error(`Invalid number of channels: ${n}`)}encode(t,n){let e,o;return t.constructor!==Float32Array&&(B.warning("Encoder","data was not of type Float32; creating new Float32Array"),o=new Float32Array(t)),n*this.channelSize>t.length?(B.warning("Encoder","Source data too small. Allocating larger array"),o=t,e=this.allocate(n*this.channelSize),o.forEach((i,a)=>e[a]=i)):(o=t,e=o),e}allocate(t){return new Float32Array(t*4)}decode(t,n){return this.channelSize===1?t.filter((o,i)=>i%4===0).subarray(0,n):t.subarray(0,n)}},fn=class{constructor(t,n=1,e){if(n!==1&&n!==4)throw new Error(`Invalid number of channels: ${n}`);this.internalFormat=t.RGBA,this.format=t.RGBA,this.channelSize=n,this.textureType=e||t.FLOAT}encode(t,n){let e=t;return this.channelSize===1&&(B.verbose("Encoder","Exploding into a larger array"),e=this.allocate(n),t.forEach((o,i)=>e[i*4]=o)),e}allocate(t){return new Float32Array(t*4)}decode(t,n){return this.channelSize===1?t.filter((o,i)=>i%4===0).subarray(0,n):t.subarray(0,n)}},tr=class{constructor(t,n=1){this.channelSize=4;if(n===1)this.internalFormat=t.ALPHA,this.format=t.ALPHA,this.textureType=t.UNSIGNED_BYTE,this.channelSize=n;else if(n===4)this.internalFormat=t.RGBA,this.format=t.RGBA,this.textureType=t.UNSIGNED_BYTE,this.channelSize=n;else throw new Error(`Invalid number of channels: ${n}`)}encode(t,n){return new Uint8Array(t.buffer,t.byteOffset,t.byteLength)}allocate(t){return new Uint8Array(t*this.channelSize)}decode(t,n){if(t instanceof Uint8Array)return t.subarray(0,n);throw new Error(`Invalid array type: ${t.constructor}`)}}});var pn,Mu,Ii,Vu=y(()=>{"use strict";V();C();pn=(r,t,n)=>{let e=n===0||n===1?1:4,o=n===2,i=n===1||n===2,a=n===4?t.length-1:void 0,s=n===4?t.map((u,l)=>l===t.length-1?u*4:u):void 0;return Ii(r,t,e,s,{isPacked:o,reverseWH:i,breakAxis:a})},Mu=(r,t,n)=>{let e=pn(r,t,n);return[e.width,e.height]},Ii=(r,t,n=1,e,o)=>{let i=!!(o&&o.isPacked),[a,s]=r.computeTextureWH(i&&e||t,o),u=t.length,l=t.slice(0);if(u===0&&(l=[1]),n===1)e=t;else if(i){if(n!==4)throw new Error("a packed texture must be 4-channel");e=t,u>0&&(l[u-1]=Math.ceil(l[u-1]/2)),u>1&&(l[u-2]=Math.ceil(l[u-2]/2))}else if(!e)throw new Error("Unpacked shape is needed when using channels > 1");return{width:a,height:s,channels:n,isPacked:i,shape:l,strides:O.computeStrides(l),unpackedShape:e,reversedWH:o&&o.reverseWH}}});var vb,er,zu=y(()=>{"use strict";pt();Ce();V();Lu();Fu();Cu();ku();dn();Vu();C();vb=(r,t)=>{let n=t.map(o=>`${o.unpackedShape.join(",")};${o.width}x${o.height}`).join("_"),e=r.name;return r.cacheHint&&(e+="["+r.cacheHint+"]"),e+=":"+n,e},er=class{constructor(t){this.session=t;this.packedTextureDataCache=new Map,this.unpackedTextureDataCache=new Map}calculateTextureWidthAndHeight(t,n){return Mu(this.session.layoutStrategy,t,n)}executeProgram(t,n){if(n.length<t.inputNames.length)throw new Error(`Input size mustn't be less than ${t.inputNames.length}.`);if(t.inputNames.length!==t.inputTypes.length)throw new Error("input names size does not match input types");let e=[];for(let l=0;l<t.inputNames.length;++l)e[l]=this.getOrCreateTextureData(n[l],t.inputTypes[l]);let o=vb(t,e),i=this.session.programManager.getArtifact(o),a=i?i.programInfo:typeof t.get=="function"?t.get():t,s=pn(this.session.layoutStrategy,a.output.dims,a.output.textureType),u=this.createTextureData(s,a.output.type);return i||(i=this.session.programManager.build(a,e,u),this.session.programManager.setArtifact(o,i)),this.runProgram(i,e,u),u}run(t,n){return this.executeProgram(t,n).tensor}runProgram(t,n,e){for(let o=0;o<n.length;++o)if(!!n[o].isPacked!=(t.programInfo.inputTypes[o]===2))throw new Error(`input[${o}] property packed inconsistent`);if(!!e.isPacked!=(t.programInfo.output.textureType===2))throw new Error("output property packed inconsistent");this.session.programManager.run(t,n,e)}getOrCreateTextureData(t,n){let e=this.getTextureData(t.dataId,n===2);if(!e&&(e=this.getTextureData(t.dataId,n!==2),e))return n===2?this.pack(e):this.unpack(e);if(!e){let o=pn(this.session.layoutStrategy,t.dims,n);if(n===4){let s=t.dims;if(s.length===4){let u=[s[0],Math.ceil(s[1]*s[2]*s[3]/4)],l=pn(this.session.layoutStrategy,u,n),c=t.numberData;if(s[1]*s[2]*s[3]%4!==0){let d=s[0],p=s[1]*s[2]*s[3],m=Math.ceil(p*1/4)*4,b=d*m;c=new Float32Array(b);for(let g=0;g<d;++g){let x=g*p,w=g*m+g%1*p;c.set(t.numberData.subarray(x,x+p),w)}}return this.createTextureData(l,t.type,c,t,1)}}if(n===2){let i=Ii(this.session.layoutStrategy,t.dims,1,[],{reverseWH:!0}),a=this.createTextureData(i,t.type,t.numberData,t,1);e=this.pack(a)}else e=this.createTextureData(o,t.type,t.numberData,t,1)}return e}createTextureDataFromLayoutBindTensor(t,n,e,o){return this.createTextureData(t,n,e,o,1)}createTextureData(t,n,e,o,i){B.verbose("InferenceHandler",`Creating TextureData: layout:[${JSON.stringify(t)}]`);let a=this.session.textureManager.createTextureFromLayout(n,t,e,i);return this.createTextureDataFromTexture(t,n,a,o)}reshapeUnpacked(t,n){let e=this.getOrCreateTextureData(t,0),o={channels:e.channels,height:e.height,width:e.width,shape:n.length!==0?n:[1],strides:O.computeStrides(n),unpackedShape:n};return this.createTextureDataFromTexture(o,t.type,e.texture).tensor}reshapePacked(t,n){let e=this.getOrCreateTextureData(t,2);if(Nu(t.dims,n)){let l={channels:e.channels,height:e.height,width:e.width,shape:n.length!==0?n:[1],strides:O.computeStrides(n),unpackedShape:n,isPacked:!0};return this.createTextureDataFromTexture(l,t.type,e.texture).tensor}let o=vi(t.dims),i=vi(n),a=this.reshapePacked(t,o),s=this.run($u(this,a,i),[a]);return this.reshapePacked(s,n)}cast(t,n){let e=this.getOrCreateTextureData(t,0);return this.createTextureDataFromTexture(e,n,e.texture).tensor}createTextureDataFromTexture(t,n,e,o,i){let a={...t,tensor:o||new Y(t.unpackedShape,n,s=>this.readTexture(a),async s=>this.readTextureAsync(a),void 0,i),texture:e};return this.setTextureData(a.tensor.dataId,a,t.isPacked),a}getTextureData(t,n=!1){return this.session.isInitializer(t)?this.session.getTextureData(t,n):n?this.packedTextureDataCache.get(t):this.unpackedTextureDataCache.get(t)}setTextureData(t,n,e=!1){this.session.isInitializer(t)?this.session.setTextureData(t,n,e):(e?this.packedTextureDataCache:this.unpackedTextureDataCache).set(t,n)}isTextureLayoutCached(t,n=!1){return!!this.getTextureData(t.dataId,n)}dispose(){this.session.textureManager.clearActiveTextures(),this.packedTextureDataCache.forEach(t=>this.session.textureManager.releaseTexture(t)),this.packedTextureDataCache=new Map,this.unpackedTextureDataCache.forEach(t=>this.session.textureManager.releaseTexture(t)),this.unpackedTextureDataCache=new Map}readTexture(t){return t.isPacked?this.readTexture(this.unpack(t)):this.session.backend.glContext.isFloat32DownloadSupported?this.session.textureManager.readTexture(t,t.tensor.type,t.channels):this.session.textureManager.readUint8TextureAsFloat(wi(this,t))}async readTextureAsync(t){return t.isPacked?this.readTextureAsync(this.unpack(t)):this.session.backend.glContext.isFloat32DownloadSupported?this.session.textureManager.readTextureAsync(t,t.tensor.type,t.channels):this.session.textureManager.readUint8TextureAsFloat(wi(this,t))}pack(t){return this.executeProgram(Du(this,t.tensor),[t.tensor])}unpack(t){return this.executeProgram(Gu(this,t.tensor),[t.tensor])}}});var Oi,L,tt=y(()=>{"use strict";Oi=class{constructor(t){Object.assign(this,t)}get cacheKey(){return this.key||(this.key=Object.getOwnPropertyNames(this).sort().map(t=>`${this[t]}`).join(";")),this.key}},L=r=>new Oi(r)});var Uu,ju,Wu,wb,Ib,Hu=y(()=>{"use strict";tt();q();C();Uu={name:"BatchNormalization",inputNames:["A","Scale","B","Mean","Variance"],inputTypes:[0,0,0,0,0]},ju=(r,t,n)=>(Ib(t),[r.run({...Uu,cacheHint:n.cacheKey,get:()=>wb(r,t,n)},t)]),Wu=r=>{let t=r.attributes.getFloat("epsilon",1e-5),n=r.attributes.getFloat("momentum",.9),e=r.attributes.getInt("spatial",1);return L({epsilon:t,momentum:n,spatial:e})},wb=(r,t,n)=>{let e=A(r.session.backend.glContext.version),o=t[0].dims.length,[i,a]=r.calculateTextureWidthAndHeight(t[1].dims,0),s=`
  float process(int[${o}] indices) {
    vec2 position = offsetToCoords(indices[1], ${i}, ${a});
    float scale = getColorAsFloat(${e.texture2D}(Scale, position));
    float mean = getColorAsFloat(${e.texture2D}(Mean, position));
    float variance = getColorAsFloat(${e.texture2D}(Variance, position));
    float b = getColorAsFloat(${e.texture2D}(B, position));

    return scale * ( (_A(indices) - mean) / sqrt(variance + float(${n.epsilon})) ) + b;
  }`;return{...Uu,output:{dims:t[0].dims,type:t[0].type,textureType:0},shaderSource:s}},Ib=r=>{if(!r||r.length!==5)throw new Error("BatchNormalization requires 5 inputs.");let t=r[0],n=r[1],e=r[2],o=r[3],i=r[4];if(t.dims.length<3||n.dims.length!==1||e.dims.length!==1||o.dims.length!==1||i.dims.length!==1)throw new Error("invalid input shape.");if(n.dims[0]!==t.dims[1]||e.dims[0]!==t.dims[1]||o.dims[0]!==t.dims[1]||i.dims[0]!==t.dims[1])throw new Error("invalid input shape.");if(t.type!=="float32"&&t.type!=="float64"||n.type!=="float32"&&n.type!=="float64"||e.type!=="float32"&&e.type!=="float64"||o.type!=="float32"&&o.type!=="float64"||i.type!=="float32"&&i.type!=="float64")throw new Error("invalid input tensor types.")}});var nr,Tt,v,hn,rr,he=y(()=>{"use strict";nr=class{constructor(t,n,e,o){this.glContext=t;this.programInfo=n;this.inputTextureLayouts=e;this.outputTextureLayout=o}},Tt=class{constructor(t){this.context=t}},v=class{constructor(t,n){this.routineBody=t;this.dependencies=n}},hn=class{constructor(t,n,e){this.name=t;e?this.dependencies=e:this.dependencies=[],n&&(this.routineBody=n)}addDependency(t){t&&this.dependencies.push(t)}},rr=class{static returnOrderedNodes(t){if(!t||t.length===0)return[];if(t.length===1)return t;let n=new Set,e=new Set,o=new Array;return this.createOrderedNodes(t,n,e,o),o}static createOrderedNodes(t,n,e,o){for(let i=0;i<t.length;++i)this.dfsTraverse(t[i],n,e,o)}static dfsTraverse(t,n,e,o){if(!t||e.has(t.name))return;if(n.has(t.name))throw new Error("Cyclic dependency detected. Can't topologically sort routines needed for shader.");n.add(t.name);let i=t.dependencies;if(i&&i.length>0)for(let a=0;a<i.length;++a)this.dfsTraverse(i[a],n,e,o);o.push(t),e.add(t.name),n.delete(t.name)}}});function Pb(){let r="add_";return{body:`
  float ${r}(float a, float b) {
    return a + b;
  }
  vec4 ${r}(vec4 v1, vec4 v2) {
    return v1 + v2;
  }
  `,name:r,type:0}}function Sb(){let r="div_";return{body:`
  float ${r}(float a, float b) {
    return a / b;
  }
  vec4 ${r}(vec4 v1, vec4 v2) {
    return v1 / v2;
  }
  `,name:r,type:0}}function Ab(){let r="mul_";return{body:`
  float ${r}(float a, float b) {
    return a * b;
  }
  vec4 ${r}(vec4 v1, vec4 v2) {
    return v1 * v2;
  }
  `,name:r,type:0}}function Eb(){let r="sub_";return{body:`
  float ${r}(float a, float b) {
    return a - b;
  }
  vec4 ${r}(vec4 v1, vec4 v2) {
    return v1 - v2;
  }
  `,name:r,type:0}}function Db(){let r="equal_";return{body:`
  float ${r}(float a, float b) {
    return float(a == b);
  }
  vec4 ${r}(vec4 v1, vec4 v2) {
    return vec4(equal(v1, v2));
  }
  `,name:r,type:0}}function Lb(){let r="greater_";return{body:`
  float ${r}(float a, float b) {
    return float(a > b);
  }
  vec4 ${r}(vec4 v1, vec4 v2) {
    return vec4( v1.r > v2.r ,
      v1.g > v2.g,
      v1.b > v2.b,
      v1.a > v2.a );
  }
  `,name:r,type:0}}function $b(){let r="less_";return{body:`
  float ${r}(float a, float b) {
    return float(a < b);
  }
  vec4 ${r}(vec4 v1, vec4 v2) {
    return vec4( v1.r < v2.r ,
                v1.g < v2.g,
                v1.b < v2.b,
                v1.a < v2.a );
  }
  `,name:r,type:0}}function Nb(){let r="and_";return{body:`
  float ${r}(float a, float b) {
    return float( bool(a) && bool(b) );
  }
  vec4 ${r}(vec4 v1, vec4 v2) {
    bvec4 b1 = bvec4(v1);
    bvec4 b2 = bvec4(v2);
    return vec4( b1.r && b2.r ,
                b1.g && b2.g,
                b1.b && b2.b,
                b1.a && b2.a );
  }
  `,name:r,type:0}}function Fb(){let r="or_";return{body:`
  float ${r}(float a, float b) {
    return float( bool(a) || bool(b) );
  }
  vec4 ${r}(vec4 v1, vec4 v2) {
    bvec4 b1 = bvec4(v1);
    bvec4 b2 = bvec4(v2);
    return vec4( b1.r || b2.r ,
                b1.g || b2.g,
                b1.b || b2.b,
                b1.a || b2.a );
  }
  `,name:r,type:0}}function Cb(){let r="xor_";return{body:`
  float ${r}(float a, float b) {
    return float( bool(a) ^^ bool(b) );
  }
  vec4 ${r}(vec4 v1, vec4 v2) {
    bvec4 b1 = bvec4(v1);
    bvec4 b2 = bvec4(v2);
    return vec4( b1.r ^^ b2.r ,
                b1.g ^^ b2.g,
                b1.b ^^ b2.b,
                b1.a ^^ b2.a );
  }
  `,name:r,type:0}}function Rb(){return kb("pow")}function Gb(){let r="prelu_";return{body:`
  float ${r}(float a, float b) {
    return a < 0.0 ? a * b: a;
  }
  vec4 ${r}(vec4 v1, vec4 v2) {
    return vec4(
      v1.r < 0.0 ? v1.r * v2.r: v1.r,
      v1.g < 0.0 ? v1.g * v2.g: v1.g,
      v1.b < 0.0 ? v1.b * v2.b: v1.b,
      v1.a < 0.0 ? v1.a * v2.a: v1.a
      );
  }
  `,name:r,type:0}}function kb(r){let t=`${r}_`;return{body:`
  float ${t}(float a, float b) {
    return ${r}(a, b);
  }
  vec4 ${t}(vec4 v1, vec4 v2) {
    return ${r}(v1, v2);
  }
  `,name:t,type:0}}var _t,Mb,qu,Ku,Xu,Zu,Ju,Yu,Qu,tl,el,nl,rl,ol,il=y(()=>{"use strict";V();he();q();C();_t=(r,t,n,e=t[0].type,o)=>{let i=r.session.pack?2:0;return{name:n.name,inputNames:["A","B"],inputTypes:[i,i],cacheHint:o,get:()=>Mb(r,t,n,e)}},Mb=(r,t,n,e=t[0].type)=>{let o=r.session.pack?2:0,i=!O.areEqual(t[0].dims,t[1].dims),a=t[0].dims,s=r.session.pack;if(i){let c=ot.calcShape(t[0].dims,t[1].dims,!1);if(!c)throw new Error("Can't perform binary op on the given tensors");a=c;let d=a.length,p=t[0].dims.length!==0?t[0].dims.length:1,m=t[1].dims.length!==0?t[1].dims.length:1,b=t[0].dims.length!==0?"bcastIndices_A(indices, aindices);":"aindices[0] = 0;",g=t[1].dims.length!==0?"bcastIndices_B(indices, bindices);":"bindices[0] = 0;",x=A(r.session.backend.glContext.version),w=s?`
      ${n.body}
      void main() {
        vec4 a = getAAtOutCoords();
        vec4 b = getBAtOutCoords();
        vec4 result = ${n.name}(a, b);
        ${x.output} = result;
      }`:`
      ${n.body}
      float process(int indices[${d}]) {
        int aindices[${p}];
        int bindices[${m}];
        ${b}
        ${g}
        return ${n.name}(_A(aindices), _B(bindices));
      }`;return{name:n.name,inputNames:["A","B"],inputTypes:[o,o],output:{dims:a,type:e,textureType:o},shaderSource:w,hasMain:s}}let u=A(r.session.backend.glContext.version),l=`
    ${n.body}
    void main() {
      vec4 v1 = ${u.texture2D}(A, TexCoords);
      vec4 v2 = ${u.texture2D}(B, TexCoords);
      vec4 result = ${n.name}(v1, v2);
      ${u.output} = result;
    }
    `;return{name:n.name,inputNames:["A","B"],inputTypes:[o,o],output:{dims:t[0].dims,type:e,textureType:o},shaderSource:l,hasMain:!0}},qu=(r,t)=>[r.run(_t(r,t,Pb()),t)],Ku=(r,t)=>[r.run(_t(r,t,Nb(),"bool"),t)],Xu=(r,t)=>[r.run(_t(r,t,Sb()),t)],Zu=(r,t)=>[r.run(_t(r,t,Db(),"bool"),t)],Ju=(r,t)=>[r.run(_t(r,t,Lb(),"bool"),t)],Yu=(r,t)=>[r.run(_t(r,t,$b(),"bool"),t)],Qu=(r,t)=>[r.run(_t(r,t,Ab()),t)],tl=(r,t)=>[r.run(_t(r,t,Fb(),"bool"),t)],el=(r,t)=>[r.run(_t(r,t,Rb()),t)],nl=(r,t)=>[r.run(_t(r,t,Gb()),t)],rl=(r,t)=>[r.run(_t(r,t,Eb()),t)],ol=(r,t)=>[r.run(_t(r,t,Cb(),"bool"),t)]});var al,sl,Bb,ul=y(()=>{"use strict";V();al=(r,t,n)=>(Bb(t),[r.cast(t[0],n)]),sl=r=>nt.tensorDataTypeFromProto(r.attributes.getInt("to")),Bb=r=>{if(!r||r.length!==1)throw new Error("Cast requires 1 input.");if(r[0].type==="string")throw new Error("Invalid input type.")}});var zb,Ub,ll,or,cl=y(()=>{"use strict";q();C();ue();Re();zb=(r,t)=>({name:"Concat (packed)",inputNames:Array.from({length:r},(n,e)=>`X${e}`),inputTypes:Array(r).fill(2),cacheHint:t}),Ub=(r,t,n,e)=>{let o=n[0].dims.slice();if(e>=o.length||e<-1*o.length)throw new Error("axis specified for concat doesn't match input dimensionality");e<0&&(e=o.length+e);let i=o.slice(0);for(let F=1;F<n.length;F++){let at=n[F].dims.slice();for(let At=0;At<o.length;At++)if(At===e)i[e]+=at[At];else if(o[At]!==at[At])throw new Error("non concat dimensions must match")}let a=i.length,s=qe("coords",a),u=it(a),l=le(),c=n.map(F=>F.dims),d=St(a),p=new Array(c.length-1);p[0]=c[0][e];for(let F=1;F<p.length;F++)p[F]=p[F-1]+c[F][e];let m=d[e],b=d.slice(-2),g=d.join(),x=`if (${m} < ${p[0]}) {
        return getChannel(
            getX0(${g}), vec2(${b.join()}));
        }`;for(let F=1;F<p.length;F++){let at=p[F-1];x+=`
            if (${m} < ${p[F]}  && ${m} >= ${p[F-1]}) {
              return getChannel(
                getX${F}(${or(d,m,at)}),
                vec2(${or(b,m,at)}));
            }`}let w=p.length,I=p[p.length-1];x+=`
            return getChannel(
              getX${w}(${or(d,m,I)}),
              vec2(${or(b,m,I)}));`;let D=A(r.session.backend.glContext.version),z=`
          ${l}
          float getValue(${d.map(F=>"int "+F)}) {
            ${x}
          }

          void main() {
            ${u} coords = getOutputCoords();
            int lastDim = coords.${d[a-1]};
            coords.${d[a-1]} = coords.${d[a-2]};
            coords.${d[a-2]} = lastDim;

            vec4 result = vec4(getValue(${s}), 0., 0., 0.);

            ${s[a-1]} = ${s[a-1]} + 1;
            if (${s[a-1]} < ${i[a-1]}) {
              result.g = getValue(${s});
            }

            ${s[a-2]} = ${s[a-2]} + 1;
            if (${s[a-2]} < ${i[a-2]}) {
              result.a = getValue(${s});
            }

            ${s[a-1]} = ${s[a-1]} - 1;
            if (${s[a-2]} < ${i[a-2]} &&
                ${s[a-1]} < ${i[a-1]}) {
              result.b = getValue(${s});
            }
            ${D.output} = result;
          }
        `;return{...t,output:{dims:i,type:n[0].type,textureType:2},shaderSource:z,hasMain:!0}},ll=(r,t,n)=>{let e=zb(t.length,n.cacheKey);return{...e,get:()=>Ub(r,e,t,n.axis)}},or=(r,t,n)=>{let e=r.indexOf(t);return r.map((i,a)=>a===e?`${i} - ${n}`:i).join()}});var fl,jb,Wb,Hb,dl,qb,Kb,Xb,pl,Zb,hl=y(()=>{"use strict";tt();C();cl();fl=(r,t,n)=>(Zb(t),r.session.pack&&t[0].dims.length>1?[r.run(ll(r,t,n),t)]:[r.run(Hb(r,t,n),t)]),jb=(r,t)=>({name:"Concat",inputNames:Array.from({length:r},(n,e)=>`X${e}`),inputTypes:Array(r).fill(0),cacheHint:t}),Wb=(r,t,n,e)=>{let o=n[0].dims.slice();if(e>=o.length||e<-1*o.length)throw new Error("axis specified for concat doesn't match input dimensionality");e<0&&(e=o.length+e);let i=o.slice(0);for(let m=1;m<n.length;m++){let b=n[m].dims.slice();for(let g=0;g<o.length;g++)if(g===e)i[e]+=b[g];else if(o[g]!==b[g])throw new Error("non concat dimensions must match")}let a=i.length,s=new Array(n.length),u=0;for(let m=0;m<s.length;++m)u+=n[m].dims[e],s[m]=u;let l="";n.length<5?l=dl(s):l=qb(s);let c=Kb(n.length,a),d=Xb(s),p=`
        ${c}
        ${d}
        ${l}
        float process(int indices[${a}]) {
          int textureIndex = getTextureWhereDataResides (indices[${e}]);

          if(textureIndex != 0) {
            indices[${e}] = indices[${e}] - int(getSizeInConcatAxisValueFromIndex(textureIndex-int(1)));
          }

          return fetchDataFromCorrectTexture(textureIndex, indices);
        }`;return{...t,output:{dims:i,type:n[0].type,textureType:0},shaderSource:p}},Hb=(r,t,n)=>{let e=jb(t.length,n.cacheKey);return{...e,get:()=>Wb(r,e,t,n.axis)}},dl=r=>`int getTextureWhereDataResides(int index) {
      ${r.map((n,e)=>`if(index<${n}) {return ${e};}
`).join("")}
    }`,qb=r=>dl(r),Kb=(r,t)=>{let n=[`float fetchDataFromCorrectTexture(int textureIndex, int indices[${t}]) {`];for(let e=0;e<r;++e)e===0?n.push(`	if (textureIndex == ${e}) { return _X${e}(indices); }`):e===r-1?n.push(`	else { return _X${e}(indices); }`):n.push(`	else if (textureIndex == ${e}) { return _X${e}(indices); }`);return n.push("	}"),n.join(`
`)},Xb=r=>{let t=["int getSizeInConcatAxisValueFromIndex(int index) {"];for(let n=0;n<r.length;++n)n===0?t.push(`	if (index == ${n}) { return ${r[n]}; }`):n===r.length-1?t.push(`	else { return ${r[n]}; }`):t.push(`	else if (index == ${n}) { return ${r[n]}; }`);return t.push("	}"),t.join(`
`)},pl=r=>L({axis:r.attributes.getInt("axis")}),Zb=r=>{if(!r||r.length<1)throw new Error("too few inputs");let t=r[0].type,n=r[0].dims.length;if(t==="string")throw new Error("string tensor is not supported yet");for(let e of r){if(e.type!==t)throw new Error("input tensors should be one type");if(e.dims.length!==n)throw new Error("input tensors should have the same shape")}}});function Jb(){return xt("abs")}function Yb(){return xt("acos")}function Qb(){return xt("asin")}function tg(){return xt("atan")}function eg(){return xt("ceil")}function ng(){return xt("cos")}function rg(r){let t="elu";return{body:`
  const float alpha = float(${r});

  float ${t}_(float a) {
    return a >= 0.0 ? a: (exp(a) - 1.0) * alpha;
  }
  vec4 ${t}_(vec4 v) {
    return vec4(${t}_(v.x), ${t}_(v.y), ${t}_(v.z), ${t}_(v.w));
  }
  `,name:t,type:0}}function og(){return xt("exp")}function ig(){return xt("floor")}function Pi(r,t){let n="clip";return{body:`
  const float min = float(${r});
  const float max = float(${t});

  float ${n}_(float a) {
    return clamp(a, min, max);
  }
  vec4 ${n}_(vec4 v) {
    return clamp(v, min, max);
  }
  `,name:n,type:0}}function ag(){let r="indentity";return{body:`
  float ${r}_(float a) {
    return a;
  }
  vec4 ${r}_(vec4 v) {
    return v;
  }
  `,name:r,type:0}}function sg(r){let t="leakyRelu";return{body:`
  const float alpha = float(${r});

  float ${t}_(float a) {
    return a < 0.0 ? a * alpha : a;
  }
  vec4 ${t}_(vec4 v) {
    return vec4(${t}_(v.x), ${t}_(v.y), ${t}_(v.z), ${t}_(v.w));
  }
  `,name:t,type:0}}function ug(){return xt("log")}function lg(){let r="neg";return{body:`
  float ${r}_(float a) {
    return -a;
  }
  vec4 ${r}_(vec4 v) {
    return -v;
  }
  `,name:r,type:0}}function cg(){let r="not";return{body:`
  float ${r}_(float a) {
    return float( ! bool(a) );
  }
  bool ${r}_(bool a) {
    return !a;
  }
  vec4 ${r}_(vec4 v) {
    return vec4(!bool(v.x), !bool(v.y), !bool(v.z), !bool(v.w));
  }
  bvec4 ${r}_(bvec4 v) {
    return bvec4(!v.x, !v.y, !v.z, !v.w);
  }
  `,name:r,type:0}}function fg(){return xt("sin")}function Si(){let r="relu";return{body:`
  float ${r}_(float a) {
    return max( a, 0.0 );
  }
  vec4 ${r}_(vec4 v) {
    return max( v, 0.0 );
  }
  `,name:r,type:0}}function Ai(){let r="sigmoid";return{body:`
  float ${r}_(float a) {
    return 1.0 / (1.0 + exp(-a));
  }
  vec4 ${r}_(vec4 v) {
    return 1.0 / (1.0 + exp(-v));
  }
  `,name:r,type:0}}function dg(){return xt("sqrt")}function pg(){return xt("tan")}function hg(){let r="tanh";return{body:`
  float ${r}_(float a) {
    a = clamp(a, -10., 10.);
    a = exp(2.*a);
    return (a - 1.) / (a + 1.);
  }
  vec4 ${r}_(vec4 v) {
    v = clamp(v, -10., 10.);
    v = exp(2.*v);
    return (v - 1.) / (v + 1.);
  }
  `,name:r,type:0}}function xt(r){return{body:`
  float ${r}_(float a) {
    return ${r}(a);
  }
  vec4 ${r}_(vec4 v) {
    return ${r}(v);
  }
  `,name:r,type:0}}var mg,J,ml,bl,gl,yl,Ei,Tl,_l,bg,xl,vl,wl,Il,Ol,Pl,Di,Sl,Al,El,Dl,Ll,$l,Nl,Fl,Cl,Rl,Gl,Li=y(()=>{"use strict";tt();V();he();q();C();mg=(r,t,n,e)=>{let o=r.session.pack?2:0,i=A(r.session.backend.glContext.version);return{...t,output:{dims:n.dims,type:n.type,textureType:o},shaderSource:`
     ${e.body}
     void main() {
       vec4 v = ${i.texture2D}(A, TexCoords);
       v = ${e.name}_(v);
       ${i.output} = v;
     }
     `,hasMain:!0}},J=(r,t,n,e)=>{let o=r.session.pack?2:0,i={name:n.name,inputTypes:[o],inputNames:["A"],cacheHint:e};return{...i,get:()=>mg(r,i,t,n)}},ml=(r,t)=>[r.run(J(r,t[0],Jb()),t)],bl=(r,t)=>[r.run(J(r,t[0],Yb()),t)],gl=(r,t)=>[r.run(J(r,t[0],Qb()),t)],yl=(r,t)=>[r.run(J(r,t[0],tg()),t)],Ei=(r,t,n)=>[r.run(J(r,t[0],Pi(n.min,n.max),n.cacheKey),t)],Tl=r=>L({min:r.attributes.getFloat("min",Ne),max:r.attributes.getFloat("max",Fe)}),_l=(r,t)=>{let n=bg(r,t);return Ei(r,[t[0]],n)},bg=(r,t)=>{if(t.length>=3&&(!r.session.isInitializer(t[1].dataId)||!r.session.isInitializer(t[2].dataId)))throw new Error("dynamic clip attributes are not allowed");let n=t.length>=3?t[1].numberData[0]:Ne,e=t.length>=3?t[2].numberData[0]:Fe;return L({min:n,max:e})},xl=(r,t)=>[r.run(J(r,t[0],eg()),t)],vl=(r,t)=>[r.run(J(r,t[0],ng()),t)],wl=(r,t,n)=>[r.run(J(r,t[0],rg(n.alpha),n.cacheKey),t)],Il=r=>L({alpha:r.attributes.getFloat("alpha",1)}),Ol=(r,t)=>[r.run(J(r,t[0],og()),t)],Pl=(r,t)=>[r.run(J(r,t[0],ig()),t)],Di=(r,t)=>[r.run(J(r,t[0],ag()),t)],Sl=(r,t,n)=>[r.run(J(r,t[0],sg(n.alpha),n.cacheKey),t)],Al=r=>L({alpha:r.attributes.getFloat("alpha",.01)}),El=(r,t)=>[r.run(J(r,t[0],ug()),t)],Dl=(r,t)=>[r.run(J(r,t[0],lg()),t)],Ll=(r,t)=>[r.run(J(r,t[0],cg()),t)],$l=(r,t)=>[r.run(J(r,t[0],Si()),t)],Nl=(r,t)=>[r.run(J(r,t[0],Ai()),t)],Fl=(r,t)=>[r.run(J(r,t[0],fg()),t)],Cl=(r,t)=>[r.run(J(r,t[0],dg()),t)],Rl=(r,t)=>[r.run(J(r,t[0],pg()),t)],Gl=(r,t)=>[r.run(J(r,t[0],hg()),t)]});function ce(r){let t;switch(r.activation){case"Relu":t=Si();break;case"Sigmoid":t=Ai();break;case"Clip":t=Pi(r.clipMin,r.clipMax);break;default:return{activationFunction:"",applyActivation:""}}let n=t.name,e=t.body,o=`value = ${n}_(value);`;return{activationFunction:e,applyActivation:o}}var Ke,Ge=y(()=>{"use strict";V();Li();Ke=r=>{let t=r.getString("activation","");if(t==="Clip"){let[n,e]=r.getFloats("activation_params",[Ne,Fe]);return{activation:t,clipMax:e,clipMin:n,activationCacheKey:`${t}:${n},${e}`}}return{activation:t,activationCacheKey:t}}});var yg,Tg,kl,Ml=y(()=>{"use strict";pt();q();C();ir();Ge();yg=(r,t)=>({name:"GroupedConv",inputNames:r?["X","W","Bias"]:["X","W"],inputTypes:r?[0,0,0]:[0,0],cacheHint:t}),Tg=(r,t,n,e)=>{let i=t.length>2?"value += getBias(output_channel);":"",a=t[0].dims.slice(),s=t[1].dims.slice(),u=s[0]/e.group;B.verbose("GroupedConv",`autpPad:${e.autoPad}, dilations:${e.dilations}, group:${e.group}, kernelShape:${e.kernelShape}, pads:${e.pads}, strides:${e.strides}`);let l=Xe(a,s,e.dilations,e.pads,e.strides),c=A(r.session.backend.glContext.version),{activationFunction:d,applyActivation:p}=ce(e),m=`
  const ivec2 strides = ivec2(${e.strides[0]}, ${e.strides[1]});
  const ivec2 pads = ivec2(${e.pads[0]}, ${e.pads[1]});
  ${d}
  void main() {
    ivec4 coords = getOutputCoords();
    int batch = coords.x;
    int output_channel = coords.y;
    ivec2 xRCCorner = coords.zw * strides - pads;
    int group_id = output_channel / ${u};

    float value = 0.0;
    for (int wInChannel = 0; wInChannel < ${s[1]}; wInChannel++) {
      int input_channel = group_id * ${s[1]} + wInChannel;
      for (int wHeight = 0; wHeight < ${s[2]}; wHeight++) {
        int xHeight = xRCCorner.x + wHeight * ${e.dilations[0]};

        if (xHeight < 0 || xHeight >= ${a[2]}) {
          continue;
        }

        for (int wWidth = 0; wWidth < ${s[3]}; wWidth++) {
          int xWidth = xRCCorner.y + wWidth * ${e.dilations[1]};
          if (xWidth < 0 || xWidth >= ${a[3]}) {
            continue;
          }

          float xVal = getX(batch, input_channel, xWidth, xHeight);
          float wVal = getW(output_channel, wInChannel, wWidth, wHeight);
          value += xVal*wVal;
        }
      }
    }
    ${i}
    ${p}
    ${c.output} = vec4(value, .0, .0, .0);
  }
`;return{...n,output:{dims:l,type:t[0].type,textureType:0},shaderSource:m,hasMain:!0}},kl=(r,t,n)=>{let e=yg(t.length>2,n.cacheKey);return{...e,get:()=>Tg(r,t,e,n)}}});var _g,xg,Vl,Bl=y(()=>{"use strict";q();C();Re();_g=r=>({name:"Im2Col (packed)",inputNames:["A"],inputTypes:[2],cacheHint:r}),xg=(r,t,n,e,o,i)=>{let a=n.dims,s=e.dims,u=2,l=3,c=o.length,d=[s[1]*s[2]*s[3],o[2]*o[3]],p=s[2]*s[3],m=le(),b=A(r.session.backend.glContext.version),g="";for(let w=0;w<=1;w++)for(let I=0;I<=1;I++)g+=`
            blockIndex = rc.x + ${I};
            pos = rc.y + ${w};

            if(blockIndex < ${d[1]} && pos < ${d[0]}) {
              offsetY = int(blockIndex / (${o[c-1]})) * ${i.strides[0]} -
                ${i.pads[0]};
              d0 = offsetY + ${i.dilations[0]} * (imod(pos, ${p}) / ${s[2]});

              if(d0 < ${a[u]} && d0 >= 0) {
                offsetX = imod(blockIndex, ${o[c-1]}) * ${i.strides[1]} -
                  ${i.pads[1]};
                d1 = offsetX + ${i.dilations[1]} * imod(imod(pos, ${p}), ${s[2]});

                if(d1 < ${a[l]} && d1 >= 0) {

                  ch = int(float(pos)/ ${p}.);
                    innerDims = vec2(d0, d1);
                    result[${w*2+I}] = getChannel(
                      getA(0, ch, int(innerDims.x),
                      int(innerDims.y)), innerDims);
                }
              }
            }

          `;let x=`
      ${m}

      void main() {
        ivec2 rc = getOutputCoords();
          vec4 result = vec4(0.0);
          int blockIndex, pos, offsetY, d0, offsetX, d1, ch;
          vec2 innerDims;
          ${g}
          ${b.output} = result;
      }
            `;return{...t,output:{dims:d,type:n.type,textureType:2},shaderSource:x,hasMain:!0}},Vl=(r,t,n,e,o)=>{let i=_g(o.cacheKey);return{...i,get:()=>xg(r,i,t,n,e,o)}}});function wg(r,t,n){let e=t[0].dims,o=t[1].dims,i=ot.calcShape(e,o,!0);if(!i)throw new Error("Can't use matmul on the given tensors");let a=it(i.length),s=St(),{activationFunction:u,applyActivation:l}=ce(n),c=t.length>2,d=c?"value += getBiasForMatmul();":"",p=c?`${Ni(a,s,t[2].dims,i,!1)}`:"",m=i.length,b=e.length,g=o.length,x=e[e.length-1],w=`
    ${u}
    ${p}
    float process(int indices[${m}]) {
        int a[${b}];
        int b[${g}];
        bcastMatmulIndices_A(indices, a);
        bcastMatmulIndices_B(indices, b);

        float value;
        for (int k=0; k<${x}; ++k) {
            a[${b-1}] = k;
            b[${g-2}] = k;
            value += _A(a) * _B(b);
        }
        ${d}
        ${l}
        return value;
    }`;return{...r,output:{dims:i,type:t[0].type,textureType:0},shaderSource:w}}function $i(r,t){let n=vg(r.length>2,t.activationCacheKey);return{...n,get:()=>wg(n,r,t)}}function Ni(r,t,n,e,o){let i="",a=n.length,s=e.length,u=s-a;s<2&&a>0?i="coords":i=n.map((g,x)=>`coords.${t[x+u]}`).join(", ");let c=ot.getBroadcastDims(n,e).map(g=>`coords.${t[g+u]} = 0;`).join(`
`),p=O.size(n)===1,m="vec4(outputValue.xx, outputValue.yy)";return p&&(m="vec4(outputValue.x)"),o?`
vec4 getBiasForMatmul() {
  ${r} coords = getOutputCoords();
  ${c}
  vec4 outputValue = getBias(${i});
  return ${m};
}`:`
float getBiasForMatmul() {
  ${r} coords = getOutputCoords();
  ${c}
  return getBias(coords.x);
}`}var zl,Ul,vg,Ig,ar=y(()=>{"use strict";V();C();ue();Ge();Fi();zl=(r,t,n)=>(Ig(t),r.session.pack?[r.run(sr(r,t,n),t)]:[r.run($i(t,n),t)]),Ul=r=>Ke(r.attributes),vg=(r,t)=>({name:"MatMul",inputNames:r?["A","B","Bias"]:["A","B"],inputTypes:r?[0,0,0]:[0,0],cacheHint:t});Ig=r=>{if(!r||r.length!==2)throw new Error("MatMul requires 2 inputs.");if(r[0].dims[r[0].dims.length-1]!==r[1].dims[r[1].dims.length-2])throw new Error("shared dimension does not match.");if(r[0].type!=="float32"&&r[0].type!=="float64"||r[1].type!=="float32"&&r[1].type!=="float64")throw new Error("inputs should be float type");if(r[0].type!==r[1].type)throw new Error("inputs types should match")}});function Sg(r,t,n,e){let o=[],i=[],a=n[0].dims,s=n[1].dims,u=a.length,l=s.length,c=e.length,d=c-u,p=c-l;o=a.map((D,z)=>`coords.${t[z+d]}`),o[u-1]="i*2",o.join(", "),i=s.map((D,z)=>`coords.${t[z+p]}`),i[l-2]="i*2",i.join(", ");let m=ot.getBroadcastDims(a,e),b=ot.getBroadcastDims(s,e),g=m.map(D=>`coords.${t[D+d]} = 0;`).join(`
`),x=b.map(D=>`coords.${t[D+p]} = 0;`).join(`
`),w=`int lastDim = coords.${t[c-1]};
  coords.${t[c-1]} = coords.${t[c-2]};
  coords.${t[c-2]} = lastDim;`;return`
vec4 getAAtOutCoordsMatmul(int i) {
  ${r} coords = getOutputCoords();
  ${w}
  ${g}
  vec4 outputValue = getA(${o});
  return outputValue;
}

vec4 getBAtOutCoordsMatmul(int i) {
  ${r} coords = getOutputCoords();
  ${w}
  ${x}
  vec4 outputValue = getB(${i});
  return outputValue;
}`}function Ag(r,t){let n="";for(let e=0;e<t-2;e++)n+=`rc.${r[e]}, `;return n+=`rc.${r[t-2]}, i*2`,n}function Eg(r,t){let n="";for(let e=0;e<t-2;e++)n+=`rc.${r[e]}, `;return n+=`i*2, rc.${r[t-1]}`,n}var Og,Pg,sr,Fi=y(()=>{"use strict";V();q();C();ue();Ge();ar();Og=(r,t)=>({name:"MatMul (packed)",inputNames:r?["A","B","Bias"]:["A","B"],inputTypes:r?[2,2,2]:[2,2],cacheHint:t}),Pg=(r,t,n,e)=>{let o=n.length>2,i=o?"value += getBiasForMatmul();":"",a=n[0].dims,s=n[1].dims,u=ot.calcShape(a,s,!0),l=!O.areEqual(n[0].dims,n[1].dims);if(!u)throw new Error("Can't use matmul on the given tensors");let c=a[a.length-1],d=Math.ceil(c/2),p=a.length,m=s.length,b=A(r.session.backend.glContext.version),g=it(u.length),x=u.length,w=St(),{activationFunction:I,applyActivation:D}=ce(e),z=o?`${Ni(g,w,n[2].dims,u,!0)}`:"",F=l?`${Sg(g,w,n,u)}`:"",at=l?"getAAtOutCoordsMatmul(i)":`getA(${Ag(w,p)})`,At=l?"getBAtOutCoordsMatmul(i)":`getB(${Eg(w,m)})`,Ar=l?"":`${g} rc =
          getOutputCoords(); int lastDim = rc.${w[x-1]}; rc.${w[x-1]} =
          rc.${w[x-2]}; rc.${w[x-2]} = lastDim;
      `,Ie=`
            ${F}
            ${z}
            ${I}
            void main() {
              ${Ar}

              vec4 value = vec4(0);
              for (int i = 0; i < ${d}; i++) {
                vec4 a = ${at};
                vec4 b = ${At};

                value += (a.rrbb * b.rgrg);
                value += (a.ggaa * b.baba);
              }
              ${i}
              ${D}
              ${b.output} = value;
            }`;return{...t,output:{dims:u,type:n[0].type,textureType:2},shaderSource:Ie,hasMain:!0}},sr=(r,t,n)=>{let e=Og(t.length>2,n.activationCacheKey);return{...e,get:()=>Pg(r,e,t,n)}}});var jl,Wl=y(()=>{"use strict";ir();Bl();Fi();jl=(r,t,n)=>{let e=t[0].dims,o=t[1].dims,i=Xe(e,o,n.dilations,n.pads,n.strides),a=r.run(Vl(r,t[0],t[1],i,n),[t[0]]),s=r.reshapePacked(t[1],[o[0],o[1]*o[2]*o[3]]),u=t.length===3?[s,a,t[2]]:[s,a],l=r.run(sr(r,u,n),u);return r.reshapePacked(l,i)}});var Dg,Lg,Hl,Ci,Ri=y(()=>{"use strict";C();Dg=r=>({name:"Im2Col",inputNames:["X"],inputTypes:[0],cacheHint:r}),Lg=(r,t,n,e,o,i)=>{let a=n.dims,s=e.dims,u=o.length,l=Ci(a,s,o,4),c=`
        const int XC = ${a[1]};
        const int XH = ${a[2]};
        const int XW = ${a[3]};
        const int KH = ${i.kernelShape[0]};
        const int KW = ${i.kernelShape[1]};
        const int dilationH = ${i.dilations[0]};
        const int dilationW = ${i.dilations[1]};
        const int strideH = ${i.strides[0]};
        const int strideW = ${i.strides[1]};
        const int padH = ${i.pads[0]};
        const int padW = ${i.pads[1]};
        const int KHKW = KH*KW;
        const int XCKHKW = XC * KHKW;
        const int outputChannels = 4;
        vec4 process(int indices[${u}]) {
          int b  = indices[0]; // batch size
          int oh = indices[1] * strideH - padH; //output height
          int ow = indices[2] * strideW - padW; //output width
          int p = indices[3] * outputChannels; //patch
          vec4 value = vec4(0.0);
          for(int i=0; i < outputChannels; ++i) {
            if(p < XCKHKW) {
              int patchC = p / KHKW;
              int patchH = (p - patchC*KHKW) / KW;
              int patchW = (p - patchC*KHKW) - patchH * KW;
              int xh2 = oh + patchH * dilationH;
              int xw2 = ow + patchW * dilationW;
              int x[${a.length}];
              x[0] = b;
              x[1] = patchC;
              x[2] = xh2;
              x[3] = xw2;
              if(xh2 >= 0 &&
                  xh2 < XH &&
                  xw2 >= 0 &&
                  xw2 < XW) {
                value[i] = _X(x);
              }
            }
            ++p;
          }
          return value;
        }
        `;return{...t,output:{dims:l,type:n.type,textureType:4},shaderSource:c}},Hl=(r,t,n,e,o)=>{let i=Dg(o.cacheKey);return{...i,get:()=>Lg(r,i,t,n,e,o)}},Ci=(r,t,n,e=4)=>[n[0],n[2],n[3],Math.ceil(r[1]*t[2]*t[3]/e)]});var $g,Ng,ql,Kl=y(()=>{"use strict";V();q();C();Ge();Ri();$g=(r,t)=>({name:"ConvDotProduct",inputNames:r?["Im2Col","K","B"]:["Im2Col","K"],inputTypes:r?[0,4,0]:[0,4],cacheKey:t.activationCacheKey}),Ng=(r,t,n,e,o)=>{let i=n[0].dims,a=n[1].dims,s=[a[0],Math.ceil(i[1]*a[2]*a[3]/4)],u=Ci(i,a,e),[l,c]=r.calculateTextureWidthAndHeight(s,4),d=O.computeStrides(u),[p,m]=r.calculateTextureWidthAndHeight(u,4),b=e.length,g=n.length<3?"0.0":"_B(b)",x=Math.ceil(i[1]*a[2]*a[3]/4),{activationFunction:w,applyActivation:I}=ce(o),D=A(r.session.backend.glContext.version),z=`
${w}
float process(int indices[${b}]) {
  int b[1];
  b[0] = indices[1];
  int im2col[4];
  im2col[0] = indices[0];
  im2col[1] = indices[2];
  im2col[2] = indices[3];
  int im2colOffset = im2col[0] * ${d[0]} + im2col[1] * ${d[1]} + im2col[2] * ${d[2]};
  int kernelOffset = indices[1] * ${s[1]};
  float value = ${g};
  for (int i = 0; i < ${x}; ++i) {
    vec2 im2colCoords = offsetToCoords(im2colOffset, ${p}, ${m});
    vec2 kernelCoords = offsetToCoords(kernelOffset, ${l}, ${c});
    value += dot(${D.texture2D}(Im2Col, im2colCoords), ${D.texture2D}(K, kernelCoords));
    ++im2colOffset;
    ++kernelOffset;
  }
  ${I}
  return value;
}`;return{...t,output:{dims:e,type:n[0].type,textureType:0},shaderSource:z}},ql=(r,t,n,e)=>{let o=$g(t.length>2,e);return{...o,get:()=>Ng(r,o,t,n,e)}}});var Xe,Gi,Fg,Cg,Rg,Gg,ki,kg,ir=y(()=>{"use strict";tt();V();Ml();Wl();Kl();Ge();Ri();ar();Xe=(r,t,n,e,o)=>{let i=r[0],a=r.slice(2),s=a.length,u=t[0],c=t.slice(2).map((b,g)=>b+(b-1)*(n[g]-1)),p=a.map((b,g)=>b+e[g]+e[g+s]).map((b,g)=>Math.floor((b-c[g]+o[g])/o[g]));return[i,u].concat(...p)},Gi=(r,t,n)=>(kg(t,n),Fg(r,t,n)),Fg=(r,t,n)=>{let e=Gg(n,t),o=r.session.pack,i=e.kernelShape[0]===1&&e.kernelShape[1]===1;return e.group>1?[r.run(kl(r,t,e),t)]:i&&o?[Cg(r,t,e)]:o&&t[0].dims.length===4&&t[0].dims[0]===1&&!i?[jl(r,t,e)]:[Rg(r,t,e)]},Cg=(r,t,n)=>{let e=t[0].dims,o=t[1].dims,i=Xe(e,o,n.dilations,n.pads,n.strides),a=r.reshapeUnpacked(t[0],[e[1],e[2]*e[3]]),s=r.reshapeUnpacked(t[1],[o[0],o[1]]),u=t.length>2?[s,a,t[2]]:[s,a],l=r.run($i(u,n),u);return r.reshapeUnpacked(l,i)},Rg=(r,t,n)=>{let e=t[0].dims,o=t[1].dims,i=Xe(e,o,n.dilations,n.pads,n.strides),a=r.run(Hl(r,t[0],t[1],i,n),[t[0]]),s=t.length===3?[a,t[1],t[2]]:[a,t[1]];return r.run(ql(r,t,i,n),s)},Gg=(r,t)=>{let n=r.kernelShape.slice();if(r.kernelShape.length===0)for(let i=2;i<t[1].dims.length;++i)n.push(t[1].dims[i]);let e=r.pads.slice();$e.adjustPadsBasedOnAutoPad(t[0].dims,r.strides,r.dilations,n,e,r.autoPad);let o=Object.assign({},r);return Object.assign(o,{kernelShape:n,pads:e,cacheKey:r.cacheKey}),o},ki=r=>{let t=r.attributes,n=Ke(t),e=t.getString("auto_pad","NOTSET"),o=t.getInts("dilations",[1,1]),i=t.getInt("group",1),a=t.getInts("kernel_shape",[]),s=t.getInts("pads",[0,0,0,0]),u=t.getInts("strides",[1,1]);return L({autoPad:e,dilations:o,group:i,kernelShape:a,pads:s,strides:u,...n})},kg=(r,t)=>{if(!r||r.length!==2&&r.length!==3)throw new Error("Conv requires 2 or 3 inputs");if(r[0].dims.length!==4||r[1].dims.length!==4)throw new Error("currently only support 2-dimensional conv");let n=r[0].dims[1],e=r[1].dims[1]*t.group;if(n!==e)throw new Error("FILTER_IN_CHANNEL should be equal to DATA_CHANNEL");if(r.length===3&&(r[2].dims.length!==1||r[1].dims[0]!==r[2].dims[0]))throw new Error("invalid bias");let o=r[0].dims.length-2;if(t.dilations.length!==o)throw new Error(`dilations should be ${o}D`);if(t.strides.length!==o)throw new Error(`strides should be ${o}D`);if(t.pads.length!==o*2)throw new Error(`pads should be ${o*2}D`);if(t.kernelShape.length!==0&&t.kernelShape.length!==r[1].dims.length-2)throw new Error("invalid kernel shape");if(r[0].type!=="float32"||r[1].type!=="float32")throw new Error("Conv input(X,W) should be float tensor");if(r.length===3&&r[2].type!=="float32")throw new Error("Conv input(bias) should be float tensor")}});var Mg,Vg,Bg,Xl,zg,Ug,jg,Wg,Hg,qg,Zl,Kg,Jl=y(()=>{"use strict";tt();q();C();Ge();Mg=(r,t,n,e,o,i)=>(r-1)*t+n+(e-1)*o+1-i,Vg=(r,t,n,e,o)=>{let i=Math.floor(r/2);t==="SAME_UPPER"?(n[e]=i,n[o]=r-i):t==="SAME_LOWER"&&(n[e]=r-i,n[o]=i)},Bg=(r,t,n,e,o,i,a,s)=>{let u=r.length-2,l=s.length===0;for(let c=0;c<u;++c){let d=l?r[c+2]*i[c]:s[c],p=Mg(r[c+2],i[c],o[c],t[c],n[c],d);Vg(p,e,o,c,c+u),l&&s.push(i[c]*(r[c+2]-1)+a[c]+(t[c]-1)*n[c]+1-o[c]-o[c+u])}},Xl=(r,t,n)=>(Kg(t,n),zg(r,t,n)),zg=(r,t,n)=>{let e=qg(n,t);return[Hg(r,t,e)]},Ug=(r,t)=>({name:"ConvTranspose",inputNames:r?["X","W","B"]:["X","W"],inputTypes:r?[0,0,0]:[0,0],cacheHint:t}),jg=(r,t,n,e)=>{let i=t.length>2?"getB(output_channel)":"0.0",a=t[0].dims,s=t[1].dims,u=s[1],l=s[0]/e.group,c=[t[0].dims[0],t[1].dims[1]*e.group,...e.outputShape],d=A(r.session.backend.glContext.version),{activationFunction:p,applyActivation:m}=ce(e),b=`
  const ivec2 strides = ivec2(${e.strides[0]}, ${e.strides[1]});
  const ivec2 pads = ivec2(${e.pads[0]}, ${e.pads[1]});
  ${p}
  void main() {
    ivec4 coords = getOutputCoords();
    int batch = coords.x;
    int output_channel = coords.y;

    ivec2 loc = coords.zw + pads;

    int group_id = output_channel / ${u};
    int wOutChannel = output_channel - group_id * ${u};

    float value = ${i};
    for (int inChannelOffset = 0; inChannelOffset < ${l}; inChannelOffset++) {
      int input_channel = group_id * ${l} + inChannelOffset;
      for (int wWOff = 0; wWOff < ${s[2]}; wWOff++) {
        for (int wHOff = 0; wHOff < ${s[3]}; wHOff++) {
          ivec2 wOff = ivec2(wWOff * ${e.dilations[0]}, wHOff * ${e.dilations[1]});
          ivec2 wLoc = loc - wOff;
          ivec2 wLocIn = wLoc / strides;
          if (
            wLocIn * strides == wLoc &&
            wLocIn.x >= 0 && wLocIn.x < ${a[2]} &&
            wLocIn.y >= 0 && wLocIn.y < ${a[3]}
          ) {
            float xVal = getX(batch, input_channel, wLocIn.y, wLocIn.x);
            float wVal = getW(input_channel, wOutChannel, wHOff, wWOff);
            value += xVal * wVal;
          }
        }
      }
    }
    ${m}
    ${d.output} = vec4(value, .0, .0, .0);
  }
`;return{...n,output:{dims:c,type:t[0].type,textureType:0},shaderSource:b,hasMain:!0}},Wg=(r,t,n)=>{let e=Ug(t.length>2,n.cacheKey);return{...e,get:()=>jg(r,t,e,n)}},Hg=(r,t,n)=>r.run(Wg(r,t,n),t),qg=(r,t)=>{let n=r.kernelShape.slice();if(r.kernelShape.length===0)for(let s=2;s<t[1].dims.length;++s)n.push(t[1].dims[s]);let e=r.pads.slice(),o=r.outputShape.slice(),i=t[0].dims;Bg(i,n,r.dilations,r.autoPad,e,r.strides,r.outputPadding,o);let a=Object.assign({},r);return Object.assign(a,{kernelShape:n,pads:e,outputShape:o,cacheKey:r.cacheKey}),a},Zl=r=>{let t=r.attributes,n=Ke(t),e=t.getString("auto_pad","NOTSET"),o=t.getInts("dilations",[1,1]),i=t.getInt("group",1),a=t.getInts("kernel_shape",[]),s=t.getInts("output_padding",[0,0]),u=t.getInts("output_shape",[]),l=t.getInts("pads",[0,0,0,0]),c=t.getInts("strides",[1,1]);return L({autoPad:e,dilations:o,group:i,kernelShape:a,outputPadding:s,outputShape:u,pads:l,strides:c,...n})},Kg=(r,t)=>{if(!r||r.length!==2&&r.length!==3)throw new Error("Conv requires 2 or 3 inputs");if(r[0].dims.length!==4||r[1].dims.length!==4)throw new Error("currently only support 2-dimensional conv");let n=r[0].dims[1],e=r[1].dims[0];if(n!==e)throw new Error("FILTER_IN_CHANNEL should be equal to DATA_CHANNEL");let o=r[1].dims[1]*t.group;if(r.length===3&&(r[2].dims.length!==1||r[2].dims[0]!==o))throw new Error("invalid bias");let i=r[0].dims.length-2;if(t.dilations.length!==i)throw new Error(`dilations should be ${i}D`);if(t.strides.length!==i)throw new Error(`strides should be ${i}D`);if(t.pads.length!==i*2)throw new Error(`pads should be ${i*2}D`);if(t.outputPadding.length!==i)throw new Error(`output_padding should be ${i}D`);if(t.kernelShape.length!==0&&t.kernelShape.length!==r[1].dims.length-2)throw new Error("invalid kernel shape");if(t.outputShape.length!==0&&t.outputShape.length!==r[0].dims.length-2)throw new Error("invalid output shape");if(r[0].type!=="float32"||r[1].type!=="float32")throw new Error("ConvTranspose input(X,W) should be float tensor");if(r.length===3&&r[2].type!=="float32")throw new Error("ConvTranspose input(bias) should be float tensor")}});var Yl,ke,Ql,Xg,tc,Zg,Jg,Yg,ur=y(()=>{"use strict";tt();V();C();Yl={name:"Transpose",inputNames:["A"],inputTypes:[0]},ke=(r,t,n)=>(Yg(t),[r.run({...Yl,cacheHint:n.cacheKey,get:()=>Xg(r,t[0],n.perm)},t)]),Ql=r=>L({perm:r.attributes.getInts("perm",[])}),Xg=(r,t,n)=>{let e=t.dims;n=tc(e,n);let o=Zg(e,n),i=e.length,a=`
      ${Jg("perm",n,i)}
      float process(int indices[${i}]) {
        int a[${i}];
        perm(a, indices);
        return _A(a);
      }`;return{...Yl,output:{dims:o,type:t.type,textureType:0},shaderSource:a}},tc=(r,t)=>(t&&t.length!==r.length&&(t=[...r.keys()].reverse()),t),Zg=(r,t)=>(t=tc(r,t),O.sortBasedOnPerm(r,t)),Jg=(r,t,n)=>{let e=[];e.push(`void ${r}(out int a[${n}], int src[${n}]) {`);for(let o=0;o<n;++o)e.push(`	a[${t[o]}]=src[${o}];`);return e.push("	}"),e.join(`
`)},Yg=r=>{if(!r||r.length!==1)throw new Error("Transpose requires 1 input.");if(r[0].type!=="float32"&&r[0].type!=="float64")throw new Error("input should be float tensor")}});var ec,nc,Qg,rc=y(()=>{"use strict";ur();ec=(r,t,n)=>{Qg(t);let e=n.blocksize,o=e*e,i=n.mode==="DCR"?[0,3,4,1,5,2]:[0,1,4,2,5,3],a=n.mode==="DCR"?[t[0].dims[0],e,e,t[0].dims[1]/o,t[0].dims[2],t[0].dims[3]]:[t[0].dims[0],t[0].dims[1]/o,e,e,t[0].dims[2],t[0].dims[3]],s=r.reshapeUnpacked(t[0],a),u={perm:i,cacheKey:`${i}`},[l]=ke(r,[s],u),c=[t[0].dims[0],t[0].dims[1]/o,t[0].dims[2]*e,t[0].dims[3]*e];return[r.reshapeUnpacked(l,c)]},nc=r=>{let t=r.attributes.getInt("blocksize");if(t<1)throw new Error(`blocksize must be >= 1, but got : ${t} for DepthToSpace`);let n=r.attributes.getString("mode","DCR");if(n!=="DCR"&&n!=="CRD")throw new Error(`unrecognized mode: ${n} for DepthToSpace`);return{mode:n,blocksize:t}},Qg=r=>{if(r.length!==1)throw new Error(`DepthToSpace expect 1 inputs, but got ${r.length}`);if(r[0].type==="string"||r[0].dims.length!==4)throw new TypeError("DepthToSpace input should be a 4-D numeric tensor")}});var oc,ic,ty,ac=y(()=>{"use strict";V();oc=(r,t,n)=>{ty(t,n);let e=O.flattenShape(t[0].dims,n);return[r.reshapeUnpacked(t[0],e)]},ic=r=>r.attributes.getInt("axis",1),ty=(r,t)=>{if(!r||r.length!==1)throw new Error("Flatten requires 1 input.");let n=r[0].dims.length;if(n===0)throw new Error("scalar tensor is not supported.");if(t<-n||t>n)throw new Error("Invalid axis");if(r[0].type==="string")throw new Error("string tensor is not supported.")}});var ve,mn=y(()=>{"use strict";ve=["float32","float64","int32","int16","int8","uint16","uint32","uint8"]});var sc,uc,ey,ny,ry,oy,lc=y(()=>{"use strict";tt();mn();V();C();sc=(r,t,n)=>(oy(t,n.axis),[r.run(ry(r,t,n),t)]),uc=r=>L({axis:r.attributes.getInt("axis",0)}),ey={name:"Gather",inputNames:["A","B"],inputTypes:[0,0]},ny=(r,t,n,e)=>{let o=n[0].dims.slice(),i=n[1].dims.slice(),a=new Array(o.length+i.length-1);e=O.normalizeAxis(e,o.length);let s=[];for(let p=0;p<a.length;p++)p<e?(a[p]=o[p],s.push(`inputIdx[${p}] = outputIdx[${p}];`)):p<e+i.length?(a[p]=i[p-e],s.push(`indexDataIdx[${p-e}] = outputIdx[${p}];`)):(a[p]=o[p-i.length+1],s.push(`inputIdx[${p-i.length+1}] = outputIdx[${p}];`));let u=a.length||1,l=o.length,c=i.length||1,d=`
      float process(int outputIdx[${u}]) {
        int inputIdx[${l}];
        int indexDataIdx[${c}];
        indexDataIdx[0] = 0;
        ${s.join(`
        `)}
        int idx = int(_B(indexDataIdx));
        inputIdx[${e}] = idx < 0 ? idx + ${o[e]} : idx;
        return _A(inputIdx);
      }`;return{...t,output:{dims:a,type:n[0].type,textureType:0},shaderSource:d}},ry=(r,t,n)=>{let e={...ey,cacheHint:n.cacheKey};return{...e,get:()=>ny(r,e,t,n.axis)}},oy=(r,t)=>{if(!r||r.length!==2)throw new Error("Gather requires 2 inputs.");let n=r[0].dims.length;if(n<1)throw new Error("Invalid input shape.");if(t<-n||t>n-1)throw new Error("Invalid axis.");if(ve.indexOf(r[0].type)===-1)throw new Error("Invaid input type.");if(r[1].type!=="int32"&&r[1].type!=="int16")throw new Error("Invaid input type.")}});var Mi,cc,fc,dc,iy,ay,sy,pc=y(()=>{"use strict";tt();V();C();Mi=(r,t,n)=>(sy(t,n),[r.run(iy(t,n),t)]),cc=(r,t)=>{let n=r.attributes.getInt("transA",0)!==0,e=r.attributes.getInt("transB",0)!==0,o=r.attributes.getFloat("alpha",1),i=r.attributes.getFloat("beta",1);return L({transA:n,transB:e,alpha:o,beta:i,isOptionalC:t})},fc=r=>cc(r,!1),dc=r=>cc(r,!0),iy=(r,t)=>{let n={name:"Gemm",inputNames:r.length===3?["A","B","C"]:["A","B"],inputTypes:r.length===3?[0,0,0]:[0,0],key:t.cacheKey};return{...n,get:()=>ay(n,r,t)}},ay=(r,t,n)=>{let e=t[0].dims.slice(),o=t[1].dims.slice(),[i,a]=Jn.getShapeOfGemmResult(e,n.transA,o,n.transB,t.length===3?t[2].dims:void 0),s=[i,a];if(!s)throw new Error("Can't use gemm on the given tensors");let u=e[e.length-1],l="";n.transA&&(u=e[0]),n.transA&&n.transB?l="value += _A_T(a) * _B_T(b);":n.transA&&!n.transB?l="value += _A_T(a) * _B(b);":!n.transA&&n.transB?l="value += _A(a) * _B_T(b);":!n.transA&&!n.transB&&(l="value += _A(a) * _B(b);");let c=s.length,d=t.length===3?`int c[${t[2].dims.length}];`:"",p=t.length===3?"bcastIndices_C(indices, c);":"",m=t.length===3?"value += beta * _C(c);":"",b=`
      float process(int indices[${c}]) {
          int a[${c}];
          int b[${c}];
          ${d}

          copyVec(indices, a);
          copyVec(indices, b);
          ${p}

          float value = 0.0;
          for (int k=0; k<${u}; ++k) {
              a[${c-1}] = k;
              b[${c-2}] = k;
              ${l}
          }

          value = value * alpha;
          ${m}
          return value;
      }`;return{...r,output:{dims:s,type:t[0].type,textureType:0},variables:[{name:"alpha",type:"float",data:n.alpha},{name:"beta",type:"float",data:n.beta}],shaderSource:b}},sy=(r,t)=>{if(!r)throw new Error("Input is missing");if(t.isOptionalC&&(r.length<2||r.length>3))throw new Error("Invaid input shape.");if(!t.isOptionalC&&r.length!==3)throw new Error("Gemm requires 3 inputs");if(r.length===3&&r[2].dims.length!==1&&r[2].dims.length!==2)throw new Error("Invalid input shape of C");if(r[0].type!=="float32"&&r[0].type!=="float64"||r[1].type!=="float32"&&r[1].type!=="float64"||r.length===3&&r[2].type!=="float32"&&r[2].type!=="float64")throw new Error("Invalid input type.");if(r[0].type!==r[1].type||r.length===3&&r[0].type!==r[2].type)throw new Error("Input types are mismatched")}});var hc,mc,uy,ly,cy,fy,dy,bc=y(()=>{"use strict";tt();C();hc=(r,t,n)=>(dy(t),[r.run(cy(r,t,n),t)]),mc=r=>{let t=r.attributes.getFloat("scale"),n=r.attributes.getFloats("bias");return L({scale:t,bias:n})},uy={name:"ImageScaler",inputNames:["X"],inputTypes:[0]},ly=(r,t,n,e)=>{let o=n[0].dims.slice(),i=o.length,s=`
      ${fy(e.bias.length)}
      float process(int indices[${i}]) {
        return _X(indices) * scale + getBias(bias, indices[1]);
      }`;return{...t,output:{dims:o,type:n[0].type,textureType:0},variables:[{name:"bias",type:"float",arrayLength:e.bias.length,data:e.bias},{name:"scale",type:"float",data:e.scale}],shaderSource:s}},cy=(r,t,n)=>{let e={...uy,cacheHint:n.cacheKey};return{...e,get:()=>ly(r,e,t,n)}},fy=r=>{let t=[`float getBias(float bias[${r}], int channel) {`];for(let n=0;n<r;++n)n===0?t.push(`	if (channel == ${n}) { return bias[${n}]; }`):n===r-1?t.push(`	else { return bias[${n}]; }`):t.push(`	else if (channel == ${n}) { return bias[${n}]; }`);return t.push("	}"),t.join(`
`)},dy=r=>{if(!r||r.length!==1)throw new Error("ImageScaler requires 1 input.");if(r[0].dims.length!==4)throw new Error("Invalid input shape.");if(r[0].type!=="float32"&&r[0].type!=="float64")throw new Error("Invalid input type.")}});var yc,Tc,gc,py,hy,my,by,gy,yy,_c=y(()=>{"use strict";q();C();yc=(r,t,n)=>{yy(t);let e=r.run(hy(t[0]),t);return[r.run(gy(r,t[0],n,e.dims),[t[0],e,t[1],t[2]])]},Tc=r=>r.attributes.getFloat("epsilon",1e-5),gc={name:"InstanceNormalization_MeanAndVariance",inputNames:["X"],inputTypes:[0]},py=(r,t)=>{let n=t.dims.slice(),e=n[1],o=n[2]*n[3],i=[n[0],e],a=`
      vec4 process(int[2] indices) {
        vec4 v = vec4(0.0);
        int a[4];
        a[0] = indices[0];
        a[1] = indices[1];
        float temp = 0.0;
        for(int a2=0; a2<${n[2]}; a2++) {
          a[2] = a2;
          for(int a3=0; a3<${n[3]}; a3++) {
            a[3] = a3;
            float x = _X(a);
            temp += x;
          }
        }
        float mean = temp / float(${o});
        temp = 0.0;
        for(int a2=0; a2<${n[2]}; a2++) {
          a[2] = a2;
          for(int a3=0; a3<${n[3]}; a3++) {
            a[3] = a3;
            float x = _X(a);
            temp += (x - mean) * (x - mean);
          }
        }
        v.r = mean;
        v.g = temp / float(${o});

        return v;
      }`;return{...r,output:{dims:i,type:t.type,textureType:4},shaderSource:a}},hy=r=>({...gc,get:()=>py(gc,r)}),my={name:"InstanceNormalization_ComputeOutput",inputNames:["X","MeanAndVariance","Scale","B"],inputTypes:[0,4,0,0]},by=(r,t,n,e,o)=>{let i=A(r.session.backend.glContext.version),[a,s]=r.calculateTextureWidthAndHeight(o,4),[u,l]=[a/4,s],c=`
      vec4 get_MeanAndVariance(int[2] mv) {
        int offset = indicesToOffset_MeanAndVariance(mv);
        vec2 coords = offsetToCoords(offset, ${u}, ${l});
        return ${i.texture2D}(MeanAndVariance, coords);
      }

      float process(int[4] indices) {
        int mv[2];
        mv[0] = indices[0];
        mv[1] = indices[1];
        vec4 mean_and_variance = get_MeanAndVariance(mv);
        float mean = mean_and_variance.r;
        float variance = mean_and_variance.g;

        int sb[1];
        sb[0] = indices[1];
        float scale = _Scale(sb);
        float b = _B(sb);

        return scale * (_X(indices) - mean) / sqrt(variance + epsilon) + b;
      }`;return{...t,output:{dims:n.dims,type:n.type,textureType:0},variables:[{name:"epsilon",type:"float",data:e}],shaderSource:c}},gy=(r,t,n,e)=>{let o={...my,cacheHint:`${n}`};return{...o,get:()=>by(r,o,t,n,e)}},yy=r=>{if(!r||r.length!==3)throw new Error("InstanceNormalization requires 3 inputs.");let t=r[0],n=r[1],e=r[2];if(t.dims.length<3||n.dims.length!==1||e.dims.length!==1)throw new Error("Invalid input shape.");if(n.dims[0]!==t.dims[1]||e.dims[0]!==t.dims[1])throw new Error("Input shapes are mismatched.");if(t.type!=="float32"&&t.type!=="float64"||n.type!=="float32"&&n.type!=="float64"||e.type!=="float32"&&e.type!=="float64")throw new Error("Invalid input type.");if(r[0].dims.length!==4)throw new Error("Only support 4-D input shape.")}});function Ty(r,t){let n=r[0].dims[1],e=r[0].dims.length,o=-Math.floor((t.size-1)/2),i=Math.ceil((t.size-1)/2),a=`float(${t.alpha}) / float(${t.size})`,s=`float(${t.bias})`,u=`float(${t.beta})`,l=`
    float process(int indices[${e}]) {
        int c = indices[1];
        float x = _X(indices);
        float square_sum = 0.0;

        for (int i = ${o}; i <= ${i}; i++) {
          int idx = c + i;
          if (c >= 0 && c < ${n}) {
            indices[1] = idx;
            float j = _X(indices);
            square_sum += j * j;
          }
        }
        return x / pow(${s} + ${a} * square_sum, ${u});
    }`;return{...wc,cacheHint:t.cacheKey,output:{dims:r[0].dims,type:r[0].type,textureType:0},shaderSource:l}}function _y(r,t){return{...wc,cacheHint:t.cacheKey,get:()=>Ty(r,t)}}var xc,vc,wc,xy,Ic=y(()=>{"use strict";tt();C();xc=(r,t,n)=>(xy(t),[r.run(_y(t,n),t)]),vc=r=>{let t=r.attributes.getFloat("alpha",1e-4),n=r.attributes.getFloat("beta",.75),e=r.attributes.getFloat("bias",1),o=r.attributes.getInt("size");return L({alpha:t,beta:n,bias:e,size:o})},wc={name:"LRN",inputNames:["X"],inputTypes:[0]};xy=r=>{if(!r||r.length!==1)throw new Error("LRN requires 1 input.");if(r[0].dims.length!==4)throw new Error('currently only support LRN for input with "NCHW" format');if(r[0].type!=="float32")throw new Error("input should be float type")}});var vy,Vi,Oc,Pc,Sc,wy,Iy,Oy,Py,Sy,Ay,Ey,Dy,Ac=y(()=>{"use strict";tt();V();q();C();vy={name:"Pad",inputNames:["A"],inputTypes:[0]},Vi=(r,t,n)=>(Oy(t),[r.run({...vy,cacheHint:n.cacheKey,get:()=>Iy(r,t[0],n)},t)]),Oc=r=>{let t=r.attributes.getString("mode","constant"),n=r.attributes.getFloat("value",0),e=r.attributes.getInts("pads");return L({mode:t,value:n,pads:e})},Pc=(r,t,n)=>{Py(t);let e=wy(r,t,n);return Vi(r,[t[0]],e)},Sc=r=>r.attributes.getString("mode","constant"),wy=(r,t,n)=>{if(!r.session.isInitializer(t[1].dataId)||t.length>=3&&!r.session.isInitializer(t[2].dataId))throw new Error("dynamic pad attributes are not allowed");let e=Array.from(t[1].integerData),o=t.length>=3?t[2].floatData[0]:0;return L({mode:n,pads:e,value:o})},Iy=(r,t,n)=>{let e=O.padShape(t.dims.slice(),n.pads),o=e.length,a=`
      ${Sy(r,t,n)}
      float process(int[${o}] indices) {
          return padA(indices);
      }`;return{name:"Pad",inputNames:["A"],inputTypes:[0],output:{dims:e,type:t.type,textureType:0},shaderSource:a}},Oy=r=>{if(!r||r.length!==1)throw new Error("Pad requires 1 input");if(r[0].type!=="float32"&&r[0].type!=="float64")throw new Error("Invalid input type.")},Py=r=>{if(!r||r.length!==2&&r.length!==3)throw new Error("Pad requires 2 or 3 inputs");if(r[1].type!=="int32")throw new Error("Invalid input type.");if(r.length>=3&&r[2].type==="string")throw new Error("Invalid input type.")},Sy=(r,t,n)=>{let e=A(r.session.backend.glContext.version),[o,i]=r.calculateTextureWidthAndHeight(t.dims,0),a=O.computeStrides(t.dims);switch(n.mode){case"constant":return Ay(e,t.dims,a,o,i,n.pads,n.value);case"reflect":return Ey(e,t.dims,a,o,i,n.pads);case"edge":return Dy(e,t.dims,a,o,i,n.pads);default:throw new Error("Invalid mode")}},Ay=(r,t,n,e,o,i,a)=>{let s=t.length,u="";for(let l=s-1;l>=0;--l)u+=`
        k = m[${l}] - ${i[l]};
        if (k < 0)  return constant;
        if (k >= ${t[l]}) return constant;
        offset += k * ${n[l]};
        `;return`
      float padA(int m[${s}]) {
        const float constant = float(${a});
        int offset = 0;
        int k = 0;
        ${u}
        vec2 coords = offsetToCoords(offset, ${e}, ${o});
        float value = getColorAsFloat(${r.texture2D}(A, coords));
        return value;
      }
      `},Ey=(r,t,n,e,o,i)=>{let a=t.length,s="";for(let u=a-1;u>=0;--u)s+=`
        k = m[${u}] - ${i[u]};
        if (k < 0) { k = -k; }
        {
          const int _2n_1 = ${2*(t[u]-1)};
          k = int( mod( float(k), float(_2n_1) ) ) ;
          if(k >= ${t[u]}) { k = _2n_1 - k; }
        }
        offset += k * ${n[u]};
        `;return`
      float padA(int m[${a}]) {
        int offset = 0;
        int k = 0;
        ${s}
        vec2 coords = offsetToCoords(offset, ${e}, ${o});
        float value = getColorAsFloat(${r.texture2D}(A, coords));
        return value;
      }
      `},Dy=(r,t,n,e,o,i)=>{let a=t.length,s="";for(let u=a-1;u>=0;--u)s+=`
        k = m[${u}] - ${i[u]};
        if (k < 0)  k = 0;
        if (k >= ${t[u]}) k = ${t[u]-1};
        offset += k * ${n[u]};
      `;return`
      float padA(int m[${a}]) {
        int offset = 0;
        int k = 0;
        ${s}
        vec2 coords = offsetToCoords(offset, ${e}, ${o});
        float value = getColorAsFloat(${r.texture2D}(A, coords));
        return value;
      }
      `}});var Dc,Lc,$c,Nc,Fc,Cc,Rc,Gc,kc,Ly,Ec,Mc,cr,Vc,lr,$y,Bc=y(()=>{"use strict";tt();V();C();Dc=(r,t,n)=>{cr(t);let e={name:"AveragePool",inputNames:["X"],inputTypes:[0],cacheHint:n.cacheKey};return[r.run({...e,get:()=>$c(t,e,!1,n)},t)]},Lc=r=>{let t=r.attributes.getString("auto_pad","NOTSET"),n=r.attributes.getInt("ceil_mode",0),e=r.attributes.getInt("count_include_pad",0)!==0,o=r.attributes.getInts("kernel_shape"),i=r.attributes.getInts("strides",[]),a=r.attributes.getInts("pads",[]);if(n!==0)throw new Error("using ceil() in shape computation is not yet supported for AveragePool");return L({autoPad:t,ceilMode:n,countIncludePad:e,kernelShape:o,strides:i,pads:a})},$c=(r,t,n,e)=>{let[o,i]=kc(r,e,n),a=O.size(o.kernelShape),s="value += _X(x);",u="";o.countIncludePad?u+=`value /= float(${a});`:u+=`value /= float(${a} - pad);`;let c=`
        ${Vc(r[0].dims,o,s,u,"0.0")}
      `;return{...t,output:{dims:i,type:r[0].type,textureType:0},shaderSource:c}},Nc=(r,t,n)=>{cr(t);let e={name:"GlobalAveragePool",inputNames:["X"],inputTypes:[0],cacheHint:`${n.countIncludePad}`};return[r.run({...e,get:()=>$c(t,e,!0,n)},t)]},Fc=r=>{let t=r.attributes.getInt("count_include_pad",0)!==0;return L({autoPad:"",ceilMode:0,countIncludePad:t,kernelShape:[],strides:[],pads:[]})},Cc=(r,t,n)=>{cr(t);let e={name:"MaxPool",inputNames:["X"],inputTypes:[0],cacheHint:n.cacheKey};return[r.run({...e,get:()=>Gc(t,e,!1,n)},t)]},Rc=r=>{let t=r.attributes.getString("auto_pad","NOTSET"),n=r.attributes.getInt("ceil_mode",0),e=r.attributes.getInts("kernel_shape"),o=r.attributes.getInts("strides",[]),i=r.attributes.getInts("pads",[]),a=r.attributes.getInt("storage_order",0),s=r.attributes.getInts("dilations",[]);if(a!==0)throw new Error("column major storage order is not yet supported for MaxPool");if(n!==0)throw new Error("using ceil() in shape computation is not yet supported for MaxPool");return L({autoPad:t,ceilMode:n,countIncludePad:!1,kernelShape:e,strides:o,pads:i,storageOrder:a,dilations:s})},Gc=(r,t,n,e)=>{let[o,i]=kc(r,e,n),a=`
      value = max(_X(x), value);
    `,s="",l=`
      ${Vc(r[0].dims,o,a,s,"-1e5")}
    `;return{...t,output:{dims:i,type:r[0].type,textureType:0},shaderSource:l}},kc=(r,t,n)=>{let e=r[0].dims.slice(),o=Object.hasOwnProperty.call(t,"dilations"),i=t.kernelShape.slice(),a=t.strides.slice(),s=o?t.dilations.slice():[],u=t.pads.slice();$e.adjustPoolAttributes(n,e,i,a,s,u);let l=$e.computePoolOutputShape(n,e,a,s,i,u,t.autoPad),c=Object.assign({},t);return o?Object.assign(c,{kernelShape:i,strides:a,pads:u,dilations:s,cacheKey:t.cacheKey}):Object.assign(c,{kernelShape:i,strides:a,pads:u,cacheKey:t.cacheKey}),[c,l]},Ly={autoPad:"",ceilMode:0,countIncludePad:!1,kernelShape:[],strides:[],pads:[],storageOrder:0,dilations:[],cacheKey:""},Ec={name:"GlobalMaxPool",inputNames:["X"],inputTypes:[0]},Mc=(r,t)=>(cr(t),[r.run({...Ec,get:()=>Gc(t,Ec,!0,Ly)},t)]),cr=r=>{if(!r||r.length!==1)throw new Error("Pool ops requires 1 input.");if(r[0].type!=="float32"&&r[0].type!=="float64")throw new Error("Invalid input type.")},Vc=(r,t,n,e,o)=>{let i=r.length;if(t.kernelShape.length<=2){let a=t.kernelShape[t.kernelShape.length-1],s=t.strides[t.strides.length-1],u=t.pads[t.pads.length/2-1],l=t.pads[t.pads.length-1],c=r[i-1],d="",p="",m="";if(u+l!==0?d=`
          for (int i = 0; i < ${a}; i++) {
            x[${i} - 1] = indices[${i} - 1] * ${s} - ${u} + i;
            if (x[${i} - 1] < 0 || x[${i} - 1] >= ${c}) {
              pad++;
              continue;
            }
            ${n}
          }`:d=`
          for (int i = 0; i < ${a}; i++) {
            x[${i} - 1] = indices[${i} - 1] * ${s} - ${u} + i;
            ${n}
          }`,t.kernelShape.length===2){let g=t.kernelShape[t.kernelShape.length-2],x=t.strides[t.strides.length-2],w=t.pads[t.pads.length/2-2],I=t.pads[t.pads.length-2],D=r[i-2];w+I!==0?p=`
            for (int j = 0; j < ${g}; j++) {
              x[${i} - 2] = indices[${i} - 2] * ${x} - ${w} + j;
              if (x[${i} - 2] < 0 || x[${i} - 2] >= ${D}) {
                pad+= ${a};
                continue;
              }
          `:p=`
            for (int j = 0; j < ${g}; j++) {
              x[${i} - 2] = indices[${i} - 2] * ${x} - ${w} + j;
            `,m=`
          }
        `}return`
        float process(int indices[${i}]) {
          int x[${i}];
          copyVec(indices, x);

          float value = ${o};
          int pad = 0;
          ${p}
          ${d}
          ${m}
          ${e}
          return value;
        }
      `}else{let a=O.size(t.kernelShape),s=O.computeStrides(t.kernelShape),u=s.length,l=t.pads.length,c=$y(u),d=lr(r,"inputDims"),p=lr(t.pads,"pads"),m=lr(s,"kernelStrides"),b=lr(t.strides,"strides"),g=t.pads.reduce((I,D)=>I+D),x="";return g?x=`
            if (x[j] >= inputDims[j] || x[j] < 0) {
              pad++;
              isPad = true;
              break;
            }
          }
          if (!isPad) {
            ${n}
          }`:x=`
          }
          ${n}
        `,`
        ${c}
        float process(int indices[${i}]) {
          int x[${i}];
          copyVec(indices, x);
          int offset[${u}];
          int pads[${l}];
          int inputDims[${i}];
          int kernelStrides[${u}];
          int strides[${u}];
          ${p}
          ${d}
          ${b}
          ${m}

          float value = ${o};
          int pad = 0;
          bool isPad = false;
          for (int i = 0; i < ${a}; i++) {
            offsetToIndices(i, kernelStrides, offset);
            isPad = false;
            for (int j = ${i} - ${u}; j < ${i}; j++) {
              x[j] = indices[j] * strides[j - ${i} + ${u}]
                + offset[j - ${i} + ${u}] - pads[j - 2];
              ${x}
          }
          ${e}

          return value;
        }
      `}},lr=(r,t)=>{let n="";for(let e=0;e<r.length;e++)n+=`
      ${t}[${e}] = ${r[e]};
    `;return n},$y=r=>`
  void offsetToIndices(int offset, int[${r}] strides, out int[${r}] indices) {
    if (${r} == 0) {
      return;
    }
    for (int i = 0; i < ${r} - 1; ++i) {
      indices[i] = offset / strides[i];
      offset -= indices[i] * strides[i];
    }
    indices[${r} - 1] = offset;
  }`});var Me,we,Ny,Fy,zc,Uc,jc,Wc,Hc,qc,Kc,Xc=y(()=>{"use strict";tt();mn();V();C();Me=(r,t,n,e,o)=>{Fy(t);let i={name:e,inputNames:["A"],inputTypes:[0]};return[r.run({...i,cacheHint:n.cacheKey,get:()=>Ny(r,t,n,e,o,i)},t)]},we=r=>{let t=r.attributes.getInts("axes",[]),n=r.attributes.getInt("keepdims",1)===1;return L({axes:t,keepDims:n})},Ny=(r,t,n,e,o,i)=>{let a=[],s=t[0].dims.length||1,u=[],l=O.normalizeAxes(n.axes,t[0].dims.length),c=o(t,l),d=c[1];for(let b=0;b<t[0].dims.length;b++)l.indexOf(b)>=0||l.length===0?(n.keepDims&&a.push(1),d=`
          for(int j${b} = 0; j${b} < ${t[0].dims[b]}; j${b}++) {
            inputIdx[${b}] = j${b};
            ${d}
          }`):(u.push(`inputIdx[${b}] = outputIdx[${a.length}];`),a.push(t[0].dims[b]));let m=`
      float process(int outputIdx[${a.length||1}]) {
        float value;                 // final result
        int inputIdx[${s}];      // addressing input data
        ${u.join(`
`)}
        ${c[0]}       // init ops for reduce max/min
        ${d}
        ${c[2]}       // final computation for reduce mean
        return value;
      }`;return{...i,output:{dims:a,type:t[0].type,textureType:0},shaderSource:m}},Fy=r=>{if(!r||r.length!==1)throw new Error("Reduce op requires 1 input.");if(ve.indexOf(r[0].type)===-1)throw new Error("Invalid input type.")},zc=(r,t,n)=>Me(r,t,n,"ReduceSum",()=>["value = 0.0;","value += _A(inputIdx);",""]),Uc=(r,t,n)=>Me(r,t,n,"ReduceMean",(o,i)=>{let a=1;for(let s=0;s<o[0].dims.length;s++)(i.indexOf(s)>=0||i.length===0)&&(a*=o[0].dims[s]);return["value = 0.0;","value += _A(inputIdx);",`value /= ${a}.;`]}),jc=(r,t,n)=>Me(r,t,n,"ReduceMax",(o,i)=>{let a=[];for(let s=0;s<o[0].dims.length;s++)(i.indexOf(s)>=0||i.length===0)&&a.push(`inputIdx[${s}] = 0;`);return[`${a.join(`
`)}
value = _A(inputIdx);`,"value = max(value, _A(inputIdx));",""]}),Wc=(r,t,n)=>Me(r,t,n,"ReduceMin",(o,i)=>{let a=[];for(let s=0;s<o[0].dims.length;s++)(i.indexOf(s)>=0||i.length===0)&&a.push(`inputIdx[${s}] = 0;`);return[`${a.join(`
`)}
value = _A(inputIdx);`,"value = min(value, _A(inputIdx));",""]}),Hc=(r,t,n)=>Me(r,t,n,"ReduceProd",()=>["value = 1.0;","value *= _A(inputIdx);",""]),qc=(r,t,n)=>Me(r,t,n,"ReduceLogSum",()=>["value = 0.0;","value += _A(inputIdx);","value = log(value);"]),Kc=(r,t,n)=>Me(r,t,n,"ReduceLogSumSquare",()=>["float t; value = 0.0;","t = _A(inputIdx); value += t * t;",""])});var Zc,Jc=y(()=>{"use strict";V();Zc=(r,t)=>{let n=O.calculateReshapedDims(t[0].dims,t[1].integerData);return r.session.pack?[r.reshapePacked(t[0],n)]:[r.reshapeUnpacked(t[0],n)]}});var Yc,Bi,Qc,tf,bn,Cy,zi,fr,Ui=y(()=>{"use strict";tt();q();C();Yc={name:"Upsample",inputNames:["X"],inputTypes:[0]},Bi=(r,t,n)=>(zi(t,n),[r.run({...Yc,cacheHint:n.cacheKey,get:()=>Cy(r,t,n)},t)]),Qc=r=>bn(r,7),tf=r=>bn(r,9),bn=(r,t)=>{let n=t>=10,e=r.attributes.getString("mode","nearest");if(e!=="nearest"&&e!=="linear"&&(t<11||e!=="cubic"))throw new Error(`unrecognized mode: ${e}`);let o=[];t<9&&(o=r.attributes.getFloats("scales"),fr(o,e,n));let i=r.attributes.getFloat("extrapolation_value",0),a=t>10?r.attributes.getString("coordinate_transformation_mode","half_pixel"):"asymmetric";if(["asymmetric","pytorch_half_pixel","tf_half_pixel_for_nn","align_corners","tf_crop_and_resize","half_pixel"].indexOf(a)===-1)throw new Error(`coordinate_transform_mode '${a}' is not supported`);let s=a==="tf_crop_and_resize",u=s,l=e==="nearest"&&t>=11?r.attributes.getString("nearest_mode","round_prefer_floor"):"";if(["round_prefer_floor","round_prefer_ceil","floor","ceil",""].indexOf(l)===-1)throw new Error(`nearest_mode '${l}' is not supported`);let c=r.attributes.getFloat("cubic_coeff_a",-.75),d=r.attributes.getInt("exclude_outside",0)!==0;if(d&&e!=="cubic")throw new Error("exclude_outside can be set to 1 only when mode is CUBIC.");let p=t<11?!0:e==="nearest"&&a==="asymmetric"&&l==="floor",m=0,b=0,g=0;return t>10?r.inputs.length>2?(m=1,b=2,g=3):(b=1,g=2):t===9&&(b=1),L({opset:t,isResize:n,mode:e,scales:o,extrapolationValue:i,coordinateTransformMode:a,useExtrapolation:u,needRoiInput:s,nearestMode:l,cubicCoefficientA:c,excludeOutside:d,useNearest2xOptimization:p,roiInputIdx:m,scalesInputIdx:b,sizesInputIdx:g})},Cy=(r,t,n)=>{let e=A(r.session.backend.glContext.version),[o,i]=r.calculateTextureWidthAndHeight(t[0].dims,0),a=t[0].dims.map((g,x)=>Math.floor(g*n.scales[x])),[s,u]=r.calculateTextureWidthAndHeight(a,0),l=a.length,c=new Array(l),d=new Array(l),p=`
      int output_pitches[${l}];
      int input_pitches[${l}];
      `;for(let g=l-1;g>=0;g--)c[g]=g===l-1?1:c[g+1]*a[g+1],d[g]=g===l-1?1:d[g+1]*t[0].dims[g+1],p+=`
        output_pitches[${g}] = ${c[g]};
        input_pitches[${g}] = ${d[g]};
        `;let m=`
      float getInputFloat(int index) {
        vec2 coords = offsetToCoords(index, ${o}, ${i});
        float value = getColorAsFloat(${e.texture2D}(X, coords));
        return value;
      }
      `,b=n.mode==="nearest"?`
    ${m}
    float process(int indices[${l}]) {
      int input_index = 0;
      int output_index = coordsToOffset(TexCoords, ${s}, ${u});

      ${p}

      int d, m;
      for (int dim = 0; dim < ${l}; ++dim) {
        d = output_index / output_pitches[dim];
        m = output_index - d * output_pitches[dim];
        output_index = m;

        if (scales[dim] != 1 && d > 0) {
          int d2 = d / scales[dim];
          m = d - d2 * scales[dim];
          d = d2;
        }
        input_index += input_pitches[dim] * d;
      }

      return getInputFloat(input_index);
    }`:l===4?`
    ${m}
    float process(int indices[4]) {
      int input_index = 0;
      int output_index = coordsToOffset(TexCoords, ${s}, ${u});

      ${p}

      int m;
      int index_of_dim0, index_of_dim1, index_of_dim2, index_of_dim3;
      index_of_dim0 = output_index / output_pitches[0];
      m = output_index - index_of_dim0 * output_pitches[0];
      index_of_dim1 = m / output_pitches[1];
      m = m - index_of_dim1 * output_pitches[1];
      index_of_dim2 = m / output_pitches[2];
      m = m - index_of_dim2 * output_pitches[2];
      index_of_dim3 = m;

      int index_of_input_dim2, index_of_input_dim3, x_offset, y_offset;
      index_of_input_dim2 = index_of_dim2 / scales[2];
      y_offset = index_of_dim2 - index_of_input_dim2 * scales[2];
      index_of_input_dim3 = index_of_dim3 / scales[3];
      x_offset = index_of_dim3 - index_of_input_dim3 * scales[3];

      input_index = index_of_dim0 * input_pitches[0] +
            index_of_dim1 * input_pitches[1] +
            index_of_input_dim2 * input_pitches[2] +
            index_of_input_dim3;

      float x00 = getInputFloat(input_index);
      float x10, x01, x11;

      bool end_of_dim2 = false;
      if (index_of_input_dim2 == (${t[0].dims[2]} - 1)) {
        // It's the end in dimension 2
        x01 = x00;
        end_of_dim2 = true;
      } else {
        x01 = getInputFloat(input_index + input_pitches[2]);
      }

      if (index_of_input_dim3 == (input_pitches[2] - 1)) {
        // It's the end in dimension 3
        x10 = x00;
        x11 = x01;
      }
      else {
        x10 = getInputFloat(input_index + 1);
        x11 = end_of_dim2 ? x10 : getInputFloat(input_index + input_pitches[2] + 1);
      }

      float y0 = x00 + float(y_offset) * (x01 - x00) / float(scales[2]);
      float y1 = x10 + float(y_offset) * (x11 - x10) / float(scales[2]);
      return y0 + float(x_offset) * (y1 - y0) / float(scales[3]);
    }`:`
    ${m}
    float process(int indices[2]) {
      int input_index = 0;
      int output_index = coordsToOffset(TexCoords, ${s}, ${u});

      ${p}

      int m;
      int index_of_dim0, index_of_dim1;
      index_of_dim0 = output_index / output_pitches[0];
      m = output_index - index_of_dim0 * output_pitches[0];
      index_of_dim1 = m;

      int index_of_input_dim0, index_of_input_dim1, x_offset, y_offset;
      index_of_input_dim0 = index_of_dim0 / scales[0];
      y_offset = index_of_dim0 - index_of_input_dim0 * scales[0];
      index_of_input_dim1 = index_of_dim1 / scales[1];
      x_offset = index_of_dim1 - index_of_input_dim1 * scales[1];

      input_index = index_of_input_dim0 * input_pitches[0] + index_of_input_dim1;

      float x00 = getInputFloat(input_index);
      float x10, x01, x11;

      bool end_of_dim0 = false;
      if (index_of_input_dim0 == (${t[0].dims[0]} - 1)) {
        // It's the end in dimension 0
        x01 = x00;
        end_of_dim0 = true;
      } else {
        x01 = getInputFloat(input_index + input_pitches[0]);
      }

      if (index_of_input_dim1 == (input_pitches[0] - 1)) {
        // It's the end in dimension 1
        x10 = x00;
        x11 = x01;
      }
      else {
        x10 = getInputFloat(input_index + 1);
        x11 = end_of_dim0 ? x10 : getInputFloat(input_index + input_pitches[0] + 1);
      }

      float y0 = x00 + float(y_offset) * (x01 - x00) / float(scales[0]);
      float y1 = x10 + float(y_offset) * (x11 - x10) / float(scales[0]);
      return y0 + float(x_offset) * (y1 - y0) / float(scales[1]);
    }`;return{...Yc,output:{dims:a,type:t[0].type,textureType:0},shaderSource:b,variables:[{name:"scales",type:"int",arrayLength:n.scales.length,data:n.scales.map(g=>Math.ceil(g))}]}},zi=(r,t)=>{if(!r||t.opset<9&&r.length!==1||t.opset>=9&&t.opset<11&&r.length!==2||t.opset>=11&&r.length<2)throw new Error("invalid inputs.");if(t.scales.length>0&&r[0].dims.length!==t.scales.length)throw new Error("Invalid input shape.");if(r[0].type==="string")throw new Error("Invalid input tensor types.")},fr=(r,t,n)=>{if(n){for(let e of r)if(e<=0)throw new Error("Scale value should be greater than 0.")}else for(let e of r)if(e<1)throw new Error("Scale value should be greater than or equal to 1.");if((t==="linear"||t==="cubic")&&r.length!==2&&(r.length!==4||r[0]!==1||r[1]!==1))throw new Error(`'Linear' mode and 'Cubic' mode only support 2-D inputs ('Bilinear', 'Bicubic')         or 4-D inputs with the corresponding outermost 2 scale values being 1         in the ${n?"Resize":"Upsample"} opeartor.`)}});var ji,Wi,ef,nf,Ry,Gy,ky,My,rf=y(()=>{"use strict";q();C();ue();Re();Ui();ji={name:"Resize",inputNames:["A"],inputTypes:[2]},Wi=(r,t,n)=>(zi(t,n),[r.run({...ji,cacheHint:n.cacheKey,get:()=>Ry(r,t,n)},t)]),ef=r=>bn(r,10),nf=r=>bn(r,11),Ry=(r,t,n)=>{let e=A(r.session.backend.glContext.version),[o,i]=Gy(t,n);if(o.every(D=>D===1)&&n.coordinateTransformMode!=="tf_crop_and_resize")return{...ji,output:{dims:i,type:t[0].type,textureType:2},hasMain:!0,shaderSource:`void main() {
                    vec4 v = ${e.texture2D}(X, TexCoords);
                    ${e.output} = v;
                }`};let s=i.length;if(s<2)throw new Error(`output dimension should be at least 2, but got ${s}`);let u=i[s-2],l=i[s-1],c=t[0].dims;if(s!==c.length)throw new Error(`output dimension should match input ${c.length}, but got ${s}`);let d=c[s-2],p=c[s-1],m=o[s-2],b=o[s-1],g="";if(n.mode!=="linear")throw new Error(`resize (packed) does not support mode: '${n.mode}'`);switch(n.coordinateTransformMode){case"asymmetric":g=`
                    vec4 getSourceFracIndex(ivec4 coords) {
                        return vec4(coords) / scaleWHWH;
                    }
                `;break;case"half_pixel":g=`
                    vec4 getSourceFracIndex(ivec4 coords) {
                        return (vec4(coords) + 0.5) / scaleWHWH - 0.5;
                    }
                `;break;case"pytorch_half_pixel":g=`
                    vec4 getSourceFracIndex(ivec4 coords) {
                        vec4 fcoords = vec4(coords);
                        return vec4(
                            ${l}.0 > 1.0 ? (fcoords.x + 0.5) / scaleWHWH.x - 0.5 : 0.0,
                            ${u}.0 > 1.0 ? (fcoords.y + 0.5) / scaleWHWH.y - 0.5 : 0.0,
                            ${l}.0 > 1.0 ? (fcoords.z + 0.5) / scaleWHWH.z - 0.5 : 0.0,
                            ${u}.0 > 1.0 ? (fcoords.w + 0.5) / scaleWHWH.w - 0.5 : 0.0
                          );
                    }
                `;break;case"align_corners":g=`
                    vec4 getSourceFracIndex(ivec4 coords) {
                        vec4 resized = vec4(${l}.0 - 1.0, ${u}.0 - 1.0, ${l}.0 - 1.0,
                            ${u}.0 - 1.0);
                        vec4 original = vec4(${p}.0 - 1.0, ${d}.0 - 1.0, ${p}.0 - 1.0,
                            ${d}.0 - 1.0);
                        vec4 new_scale = original / resized;
                        return vec4(coords) * new_scale;
                    }
                `;break;default:throw new Error(`resize (packed) does not support coordinateTransformMode:                                 '${n.coordinateTransformMode}'`)}let x=it(s),w=le(),I=`
            const vec2 inputWH = vec2(${d}.0, ${p}.0);
            const vec4 scaleWHWH = vec4(float(${m}), float(${b}), float(${m}), float(${b}));
            ${w}
            ${g}
            float getAValue(int x10, int r, int c, int d) {
                return getChannel(getA(x10, r, c, d), vec2(c, d));
            }
            void main() {
                ${x} rc = getOutputCoords();

                int batch = rc[0];
                int depth = rc[1];

                // retrieve the 4 coordinates that is used in the 4 packed output values.
                ivec4 coords = ivec4(rc.wz, rc.w + 1, rc.z + 1);

                // calculate the source index in fraction
                vec4 sourceFrac = getSourceFracIndex(coords);

                // get the lower and upper bound of the 4 values that will be packed into one texel.
                ivec4 x00 = ivec4(max(sourceFrac.xy, vec2(0.0)), min(inputWH - 1.0, ceil(sourceFrac.xy)));
                ivec4 x01 = ivec4(max(sourceFrac.xw, vec2(0.0)), min(inputWH - 1.0, ceil(sourceFrac.xw)));
                ivec4 x10 = ivec4(max(sourceFrac.zy, vec2(0.0)), min(inputWH - 1.0, ceil(sourceFrac.zy)));
                ivec4 x11 = ivec4(max(sourceFrac.zw, vec2(0.0)), min(inputWH - 1.0, ceil(sourceFrac.zw)));

                bool hasNextRow = rc.w < ${u-1};
                bool hasNextCol = rc.z < ${l-1};

                // pack x00, x01, x10, x11's top-left corner into one vec4 structure
                vec4 topLeft = vec4(
                    getAValue(batch, depth, x00.x, x00.y),
                    hasNextCol ? getAValue(batch, depth, x01.x, x01.y) : 0.0,
                    hasNextRow ? getAValue(batch, depth, x10.x, x10.y) : 0.0,
                    (hasNextRow && hasNextCol) ? getAValue(batch, depth, x11.x, x11.y) : 0.0);

                // pack x00, x01, x10, x11's top-right corner into one vec4 structure
                vec4 topRight = vec4(
                    getAValue(batch, depth, x00.x, x00.w),
                    hasNextCol ? getAValue(batch, depth, x01.x, x01.w) : 0.0,
                    hasNextRow ? getAValue(batch, depth, x10.x, x10.w) : 0.0,
                    (hasNextRow && hasNextCol) ? getAValue(batch, depth, x11.x, x11.w) : 0.0);

                // pack x00, x01, x10, x11's bottom-left corner into one vec4 structure
                vec4 bottomLeft = vec4(
                    getAValue(batch, depth, x00.z, x00.y),
                    hasNextCol ? getAValue(batch, depth, x01.z, x01.y) : 0.0,
                    hasNextRow ? getAValue(batch, depth, x10.z, x10.y) : 0.0,
                    (hasNextRow && hasNextCol) ? getAValue(batch, depth, x11.z, x11.y) : 0.0);

                // pack x00, x01, x10, x11's bottom-right corner into one vec4 structure
                vec4 bottomRight = vec4(
                    getAValue(batch, depth, x00.z, x00.w),
                    hasNextCol ? getAValue(batch, depth, x01.z, x01.w) : 0.0,
                    hasNextRow ? getAValue(batch, depth, x10.z, x10.w) : 0.0,
                    (hasNextRow && hasNextCol) ? getAValue(batch, depth, x11.z, x11.w) : 0.0);

                // calculate the interpolation fraction on u and v direction
                vec4 frac = vec4(sourceFrac) - floor(sourceFrac);
                vec4 clampFrac = clamp(frac, vec4(0.0), vec4(1.0));

                vec4 top = mix(topLeft, topRight, clampFrac.ywyw);
                vec4 bottom = mix(bottomLeft, bottomRight, clampFrac.ywyw);
                vec4 newValue = mix(top, bottom, clampFrac.xxzz);

                ${e.output} = vec4(newValue);
            }
        `;return{...ji,output:{dims:i,type:t[0].type,textureType:2},hasMain:!0,shaderSource:I}},Gy=(r,t)=>{let e=r[0].dims,o=t.scales,i;if(o.length===0){let s=r[t.scalesInputIdx];if(s&&s.size!==0){if(r[t.sizesInputIdx])throw new Error("Only one of scales or sizes must be provided as input.");o=ky(s,t.mode,t.isResize)}else{let u=r[t.sizesInputIdx];if(!u||u.size===0)throw new Error("Either scales or sizes MUST be provided as input.");i=Array.from(u.integerData),o=My(i,e,t.mode,t.isResize)}}else if(r[t.sizesInputIdx])throw new Error("Only one of scales or sizes must be provided as input.");let a=i||e.map((s,u)=>Math.floor(s*o[u]));return[o,a]},ky=(r,t,n)=>{let e=Array.from(r.floatData);return fr(e,t,n),e},My=(r,t,n,e)=>{let o=t.length,i=new Array(o);for(let a=0,s=o;a<s;a++)if(t[a]===0){if(r[a]!==0)throw new Error("Input dim is zero but required output dim is non-zero.");i[a]=1}else i[a]=r[a]/t[a];return fr(i,n,e),i}});var of,Vy,af=y(()=>{"use strict";Ce();of=(r,t)=>(Vy(t),[new Y([t[0].dims.length],"int32",void 0,void 0,new Int32Array(t[0].dims))]),Vy=r=>{if(!r||r.length!==1)throw new Error("Shape requires 1 input.")}});var Hi,sf,uf,lf,By,cf,zy,Uy,ff=y(()=>{"use strict";tt();mn();V();C();Hi={name:"Slice",inputNames:["A"],inputTypes:[0]},sf=(r,t,n)=>(By(t),[r.run({...Hi,cacheHint:n.cacheKey,get:()=>lf(r,t[0],n)},t)]),uf=r=>{let t=r.attributes.getInts("starts"),n=r.attributes.getInts("ends"),e=r.attributes.getInts("axes",[]);return L({starts:t,ends:n,axes:e})},lf=(r,t,n)=>{let e=n.axes.length===0?t.dims.slice(0).map((d,p)=>p):n.axes,o=O.normalizeAxes(e,t.dims.length),i=n.starts.map((d,p)=>d>t.dims[o[p]]-1?t.dims[o[p]]:O.normalizeAxis(d,t.dims[o[p]])),a=n.ends.map((d,p)=>d>t.dims[o[p]]-1?t.dims[o[p]]:O.normalizeAxis(d,t.dims[o[p]])),s=t.dims.slice(),u=[];for(let d=0;d<o.length;d++)s[o[d]]=a[d]-i[d],i[d]>0&&u.push(`outputIdx[${o[d]}] += ${i[d]};`);let c=`
      float process(int outputIdx[${s.length}]) {
        ${u.join(`
      `)}
        return _A(outputIdx);
      }`;return{...Hi,output:{dims:s,type:t.type,textureType:0},shaderSource:c}},By=r=>{if(!r||r.length!==1)throw new Error("Slice requires 1 input.");if(ve.indexOf(r[0].type)===-1)throw new Error("Invalid input type.")},cf=(r,t)=>{Uy(t);let n=zy(r,t);return[r.run({...Hi,cacheHint:n.cacheKey,get:()=>lf(r,t[0],n)},[t[0]])]},zy=(r,t)=>{if(!r.session.isInitializer(t[1].dataId)||!r.session.isInitializer(t[2].dataId)||t.length>=4&&!r.session.isInitializer(t[3].dataId)||t.length>=5&&!r.session.isInitializer(t[4].dataId))throw new Error("dynamic slice attributes are not allowed");if(t.length>=5&&t[4].integerData.some(a=>a!==1))throw new Error("currently non-1 steps is not supported for Slice");let n=Array.from(t[1].integerData),e=Array.from(t[2].integerData),o=t.length>=4?Array.from(t[3].integerData):[],i=`${o};${n};${e}`;return{starts:n,ends:e,axes:o,cacheKey:i}},Uy=r=>{if(!r||r.length<3||r.length>5)throw new Error("Invalid input number.");if(r[1].type!=="int32"||r[1].dims.length!==1)throw new Error("Invalid input type.");if(r[2].type!=="int32"||r[2].dims.length!==1)throw new Error("Invalid input type.");if(r.length>=4&&(r[3].type!=="int32"||r[3].dims.length!==1))throw new Error("Invalid input type.");if(r.length>=5&&(r[4].type!=="int32"||r[4].dims.length!==1))throw new Error("Invalid input type.")}});var df,pf,hf,mf,bf,gf,yf,Tf,jy,Wy,Hy,_f,xf=y(()=>{"use strict";tt();V();q();C();ur();df={name:"SoftmaxComputeMax",inputNames:["A"],inputTypes:[0]},pf={name:"SoftmaxComputeScale",inputNames:["A","Max"],inputTypes:[0,0]},hf={name:"SoftMax",inputNames:["A","Max","Norm"],inputTypes:[0,0,0]},mf=(r,t,n)=>{_f(t);let e=t[0].dims.slice(),o=O.normalizeAxis(n.axis,e.length),i=O.sizeToDimension(e,o),a=O.sizeFromDimension(e,o);return Tf(r,t,n,i,a)},bf=r=>L({axis:r.attributes.getInt("axis",1)}),gf=r=>L({axis:r.attributes.getInt("axis",-1)}),yf=(r,t,n)=>{_f(t);let e=t[0].dims.slice(),o=O.normalizeAxis(n.axis,e.length),i=e.length,a=o!==i-1,s=[],u=[],l=[],c;a&&(u=Array.from({length:i}).map((b,g)=>g),u[o]=i-1,u[i-1]=o,u.map(b=>s.push(e[b])),c=L({perm:u}),l=ke(r,t,c));let d=a?O.sizeToDimension(s,i-1):O.sizeToDimension(e,i-1),p=a?O.sizeFromDimension(s,i-1):O.sizeFromDimension(e,i-1),m=Tf(r,a?l:t,n,d,p);return a?ke(r,m,c):m},Tf=(r,t,n,e,o)=>{let i=jy(r,t[0],e,o,[e]),a=r.run({...df,cacheHint:n.cacheKey,get:()=>i},t),s=Wy(r,t[0],e,o,i.output.dims,[e]),u=r.run({...pf,cacheHint:n.cacheKey,get:()=>s},[t[0],a]),l=Hy(r,t[0],e,o,i.output.dims,s.output.dims);return[r.run({...hf,cacheHint:n.cacheKey,get:()=>l},[t[0],a,u])]},jy=(r,t,n,e,o)=>{let[i,a]=r.calculateTextureWidthAndHeight(t.dims,0),s=o.length;if(n<1||e<1)throw new Error("Logical row count N and feature count D must be greater than or equal to 1");if(o.length!==1)throw new Error("Dimensionality of the output should be 1");if(o[0]!==n)throw new Error("Shape of the output should be equal to logical row count");let u=A(r.session.backend.glContext.version),l=`
      float process(int[${s}] indices) {
        int logical_row_start_offset = indices[0] * ${e};

        float max = getColorAsFloat(${u.texture2D}(A, offsetToCoords(logical_row_start_offset, ${i},
        ${a} )));
        for(int i=1; i<${e}; ++i)
        {
          float current = getColorAsFloat(${u.texture2D}(A, offsetToCoords(logical_row_start_offset + i,
            ${i}, ${a})));
          if(current > max)
          max = current;
        }

        return max;
      }`;return{...df,output:{dims:o,type:t.type,textureType:0},shaderSource:l}},Wy=(r,t,n,e,o,i)=>{let[a,s]=r.calculateTextureWidthAndHeight(t.dims,0),u=i.length;if(n<1||e<1)throw new Error("Logical row count N and feature count D must be greater than or equal to 1");if(i.length!==1)throw new Error("Dimensionality of the output should be 1");if(i[0]!==n)throw new Error("Shape of the output should be equal to logical row count");if(o.length!==1)throw new Error("Dimensionality of the intermediate results should be 1");if(o[0]!==n)throw new Error("Shape of the intermediate results should be equal to logical row count");let l=A(r.session.backend.glContext.version),c=`
      float process(int[${u}] indices) {
        int logical_row_start_offset = indices[0] * ${e};

        float norm_factor = 0.0;
        float max = _Max(indices);
        for(int i=0; i<${e}; ++i)
        {
          norm_factor += exp(getColorAsFloat(${l.texture2D}(A, offsetToCoords(logical_row_start_offset + i,
            ${a}, ${s}))) - max);
        }

        return norm_factor;
      }`;return{...pf,output:{dims:i,type:t.type,textureType:0},shaderSource:c}},Hy=(r,t,n,e,o,i)=>{let[a,s]=r.calculateTextureWidthAndHeight(t.dims,0),u=t.dims.length;if(n<1||e<1)throw new Error("Logical row count N and feature count D must be greater than or equal to 1");if(o.length!==1||i.length!==1)throw new Error("Dimensionality of the intermediate results should be 1");if(o[0]!==n||i[0]!==n)throw new Error("Shape of the intermediate results should be equal to logical row count");let l=`
      float process(int[${u}] indices) {

      // get offset of current logical tensor index from the 2-D texture coordinates (TexCoords)
      int offset = coordsToOffset(TexCoords, ${a}, ${s});

      //determine the logical row for this index
      int logical_row_index[1];
      logical_row_index[0] = offset / ${e};

      float norm_factor = _Norm(logical_row_index);

      // avoid possible division by 0
      // if norm_facor is 0, all elements are zero
      // if so, return 0
      if(norm_factor == 0.0)
        return 0.0;

      return exp(_A(indices) - _Max(logical_row_index)) / norm_factor;
    }`;return{...hf,output:{dims:t.dims,type:t.type,textureType:0},shaderSource:l}},_f=r=>{if(!r||r.length!==1)throw new Error("Softmax requires 1 input.");if(r[0].type!=="float32"&&r[0].type!=="float64")throw new Error("Invalid input type")}});var vf,wf,If,qy,Ky,Xy,Of=y(()=>{"use strict";tt();V();C();vf={name:"Split",inputNames:["A"],inputTypes:[0]},wf=(r,t,n)=>{Xy(t);let e=O.normalizeAxis(n.axis,t[0].dims.length),o=qy(r,t,e,n),i=[];for(let a=0;a<o;++a)i.push(r.run({...vf,cacheHint:`${n.cacheKey};${a}`,get:()=>Ky(r,t[0],n,e,a)},t));return i},If=r=>{let t=r.attributes.getInt("axis",0),n=r.attributes.getInts("split",[]),e=r.outputs.length;return L({axis:t,split:n,numOutputs:e})},qy=(r,t,n,e)=>{let[,o]=ln.splitShape(t[0].dims,n,e.split,e.numOutputs);return o.length},Ky=(r,t,n,e,o)=>{let[i,a]=ln.splitShape(t.dims,e,n.split,n.numOutputs),s=a[o],u=i[o],c=`
      float process(int indices[${u.length}]) {
        indices[${e}] += ${s};
        return _A(indices);
      }
    `;return{...vf,cacheHint:`${n.cacheKey}:${o}`,output:{dims:u,type:t.type,textureType:0},shaderSource:c}},Xy=r=>{if(!r||r.length!==1)throw new Error("Split requires one input.");if(r[0].type!=="int8"&&r[0].type!=="uint8"&&r[0].type!=="int16"&&r[0].type!=="uint16"&&r[0].type!=="int32"&&r[0].type!=="uint32"&&r[0].type!=="float32"&&r[0].type!=="float64"&&r[0].type!=="bool")throw new Error("Invalid input type.")}});var qi,Pf,Sf,Zy,Jy,Af=y(()=>{"use strict";V();qi=(r,t,n)=>{Zy(t);let e=O.squeezeShape(t[0].dims,n);return[r.reshapeUnpacked(t[0],e)]},Pf=(r,t)=>(Jy(t),qi(r,[t[0]],Array.from(t[1].integerData))),Sf=r=>r.attributes.getInts("axes"),Zy=r=>{if(!r||r.length!==1)throw new Error("Squeeze requires 1 input.");if(r[0].type==="string")throw new Error("invalid input tensor types.")},Jy=r=>{if(!r||r.length!==2)throw new Error("Squeeze requires 2 inputs.");if(r[1].type!=="int32")throw new Error("Invalid input type.")}});var Ef,Yy,Qy,Df=y(()=>{"use strict";q();C();Ef=(r,t)=>{Qy(t);let n={name:"Sum",inputNames:t.map((o,i)=>`X${i}`),inputTypes:new Array(t.length).fill(0)};return[r.run({...n,get:()=>Yy(r,t,n)},t)]},Yy=(r,t,n)=>{let e=A(r.session.backend.glContext.version),o=t[0].dims.slice(),a=`
      void main() {
        vec4 result = ${t.map((s,u)=>`${e.texture2D}(X${u},TexCoords)`).join(" + ")};
        ${e.output} = result;
      }
    `;return{...n,output:{dims:o,type:t[0].type,textureType:0},hasMain:!0,shaderSource:a}},Qy=r=>{if(!r||r.length===0)throw new Error("Sum requires inputs.");let t=r[0].dims.length;for(let n=1;n<r.length;n++){if(t!==r[n].dims.length)throw new Error("Input shapes are mismatched.");for(let e=0;e<t;e++)if(r[0].dims[e]!==r[n].dims[e])throw new Error("Input shapes are not matched.")}if(r[0].type!=="float32"&&r[0].type!=="float64")throw new Error("Invalid input type.");for(let n=1;n<r.length;n++)if(r[0].type!==r[n].type)throw new Error("Input types are not matched.")}});var Lf,tT,eT,$f=y(()=>{"use strict";mn();C();Lf=(r,t)=>{eT(t);let n={name:"Tile",inputNames:["A"],inputTypes:[0]};return[r.run({...n,get:()=>tT(r,t,n)},t)]},tT=(r,t,n)=>{let e=t[0].dims.slice(),o=new Array(e.length),i=[];for(let u=0;u<e.length;u++)o[u]=e[u]*t[1].numberData[u],i.push(`inputIdx[${u}] = int(mod(float(outputIdx[${u}]), ${e[u]}.));`);let a=o.length,s=`
      float process(int outputIdx[${a}]) {
        int inputIdx[${a}];
        ${i.join(`
`)}
        return _A(inputIdx);
      }
    `;return{...n,output:{dims:o,type:t[0].type,textureType:0},shaderSource:s}},eT=r=>{if(!r||r.length!==2)throw new Error("Tile requires 2 input.");if(r[1].dims.length!==1)throw new Error("The second input shape must 1 dimension.");if(r[1].dims[0]!==r[0].dims.length)throw new Error("Invalid input shape.");if(ve.indexOf(r[0].type)===-1)throw new Error("Invalid input type.");if(r[1].type!=="int32"&&r[1].type!=="int16")throw new Error("Invalid repeat type.")}});var Ki,Nf,Ff,nT,rT,Cf=y(()=>{"use strict";V();Ki=(r,t,n)=>{nT(t);let e=O.unsqueezeShape(t[0].dims,n);return[r.reshapeUnpacked(t[0],e)]},Nf=(r,t)=>(rT(t),Ki(r,[t[0]],Array.from(t[1].integerData))),Ff=r=>r.attributes.getInts("axes"),nT=r=>{if(!r||r.length!==1)throw new Error("Unsqueeze requires 1 input.");if(r[0].type==="string")throw new Error("invalid input tensor types.")},rT=r=>{if(!r||r.length!==2)throw new Error("Unsqueeze requires 2 inputs.");if(r[1].type!=="int32")throw new Error("Invalid input type.")}});var Rf,Gf=y(()=>{"use strict";Hu();il();ul();hl();ir();Jl();rc();ac();lc();pc();bc();_c();Ic();ar();Ac();Bc();Xc();Jc();rf();af();ff();xf();Of();Af();Df();$f();ur();Li();Cf();Ui();Rf=[["Abs","","6+",ml],["Acos","","7+",bl],["Add","","7+",qu],["And","","7+",Ku],["Asin","","7+",gl],["Atan","","7+",yl],["AveragePool","","7+",Dc,Lc],["BatchNormalization","","7+",ju,Wu],["Cast","","6+",al,sl],["Ceil","","6+",xl],["Clip","","6-10",Ei,Tl],["Clip","","11+",_l],["Concat","","4+",fl,pl],["Conv","","1+",Gi,ki],["ConvTranspose","","1+",Xl,Zl],["Cos","","7+",vl],["Div","","7+",Xu],["Dropout","","7+",Di],["DepthToSpace","","1+",ec,nc],["Equal","","7+",Zu],["Elu","","6+",wl,Il],["Exp","","6+",Ol],["Flatten","","1+",oc,ic],["Floor","","6+",Pl],["FusedConv","com.microsoft","1+",Gi,ki],["Gather","","1+",sc,uc],["Gemm","","7-10",Mi,fc],["Gemm","","11+",Mi,dc],["GlobalAveragePool","","1+",Nc,Fc],["GlobalMaxPool","","1+",Mc],["Greater","","7+",Ju],["Identity","","1+",Di],["ImageScaler","","1+",hc,mc],["InstanceNormalization","","6+",yc,Tc],["LeakyRelu","","6+",Sl,Al],["Less","","7+",Yu],["LRN","","1+",xc,vc],["Log","","6+",El],["MatMul","","1+",zl,Ul],["MaxPool","","1+",Cc,Rc],["Mul","","7+",Qu],["Neg","","6+",Dl],["Not","","1+",Ll],["Or","","7+",tl],["Pad","","2-10",Vi,Oc],["Pad","","11+",Pc,Sc],["Pow","","7+",el],["PRelu","","7+",nl],["ReduceLogSum","","1+",qc,we],["ReduceMax","","1+",jc,we],["ReduceMean","","1+",Uc,we],["ReduceMin","","1+",Wc,we],["ReduceProd","","1+",Hc,we],["ReduceSum","","1-12",zc,we],["ReduceSumSquare","","1+",Kc,we],["Relu","","6+",$l],["Reshape","","5+",Zc],["Resize","","10",Wi,ef],["Resize","","11+",Wi,nf],["Shape","","1+",of],["Sigmoid","","6+",Nl],["Sin","","7+",Fl],["Slice","","10+",cf],["Slice","","1-9",sf,uf],["Softmax","","1-12",mf,bf],["Softmax","","13+",yf,gf],["Split","","2-12",wf,If],["Sqrt","","6+",Cl],["Squeeze","","1-12",qi,Sf],["Squeeze","","13+",Pf],["Sub","","7+",rl],["Sum","","6+",Ef],["Tan","","7+",Rl],["Tanh","","6+",Gl],["Tile","","6+",Lf],["Transpose","","1+",ke,Ql],["Upsample","","7-8",Bi,Qc],["Upsample","","9",Bi,tf],["Unsqueeze","","1-12",Ki,Ff],["Unsqueeze","","13+",Nf],["Xor","","7+",ol]]});function Mf(r){let t={},n;for(;(n=kf.exec(r))!==null;){let e=n[3].split(",").map(o=>{let i=o.trim().split(" ");return i&&i.length===2?{type:i[0],name:i[1]}:null}).filter(o=>o!==null);t[n[2]]={params:e,body:n[4]}}for(let e in t){let o=oT.replace("__FUNC__",e),i=new RegExp(o,"gm");for(;(n=i.exec(r))!==null;){let a=n[1],s=n[2],u=n[3].split(","),l=a?`${a} ${s};`:"",c=t[e].body,d="";t[e].params.forEach((m,b)=>{m&&(d+=`${m.type} ${m.name} = ${u[b]};
`)}),c=`${d}
 ${c}`,c=c.replace("return",`${s} = `);let p=`
      ${l}
      {
        ${c}
      }
      `;r=r.replace(n[0],p)}}return r=r.replace(kf,""),r}var kf,oT,Vf=y(()=>{"use strict";kf=/@inline[\s\n\r]+(\w+)[\s\n\r]+([0-9a-zA-Z_]+)\s*\(([^)]*)\)\s*{(([^}]|[\n\r])*)}/gm,oT="(\\w+)?\\s+([_0-9a-zA-Z]+)\\s+=\\s+__FUNC__\\((.*)\\)\\s*;"});function Ze(r,t){let n=[],e=[],o=t!=null&&Array.isArray(t)&&t.length===0,i=t==null||o?null:iT(t,r).sort(),a=0;for(let s=0;s<r.length;++s){if(i!=null){if(i[a]===s&&r[s]!==1)throw new Error(`Can't squeeze axis ${s} since its dim '${r[s]}' is not 1`);(i[a]==null||i[a]>s)&&r[s]===1&&(n.push(r[s]),e.push(s)),i[a]<=s&&a++}r[s]!==1&&(n.push(r[s]),e.push(s))}return{newShape:n,keptDims:e}}function iT(r,t){let n=t.length;return r=r==null?t.map((e,o)=>o):[].concat(r),je(r.every(e=>e>=-n&&e<n),()=>`All values in axis param must be in range [-${n}, ${n}) but got axis ${r}`),je(r.every(aT),()=>`All values in axis param must be integers but got axis ${r}`),r.map(e=>e<0?n+e:e)}function aT(r){return r%1===0}function sT(r){if(r.length===0)return 1;let t=r[0];for(let n=1;n<r.length;n++)t*=r[n];return t}function Bf(r){let t=Math.ceil(Math.sqrt(r));return[t,Math.ceil(r/t)]}var dr,Xi=y(()=>{"use strict";pt();V();dr=class{constructor(t){this.maxTextureSize=t}computeTextureWH(t,n){let e=this.computeTexture(t,n);return n&&n.isPacked&&(e[0]/=2,e[1]/=2),n&&n.reverseWH?[e[1],e[0]]:e}computeTexture(t,n){let e=n&&n.isPacked;if(t.length===0)return e?[2,2]:[1,1];let o=this.maxTextureSize;if(n&&n.breakAxis!==void 0){let s=n.breakAxis>=t.length?1:t.slice(n.breakAxis).reduce((l,c)=>l*c),u=n.breakAxis<=0?1:t.slice(0,n.breakAxis).reduce((l,c)=>l*c);if(s>o||u>o)B.verbose("TextureLayout",`Given width/height preferences were unattainable: shape:${t}, breakAxis:${n.breakAxis}`);else return[s,u]}let i=t.slice(0);e&&(o=o*2,i=i.map((s,u)=>u>=i.length-2?i[u]%2===0?i[u]:i[u]+1:i[u]),i.length===1&&(i=[2,i[0]])),i.length!==2&&(i=Ze(i).newShape);let a=sT(i);return i.length<=1&&a<=o?[1,a]:i.length===2&&i[0]<=o&&i[1]<=o?i:i.length===3&&i[0]*i[1]<=o&&i[2]<=o?[i[0]*i[1],i[2]]:i.length===3&&i[0]<=o&&i[1]*i[2]<=o?[i[0],i[1]*i[2]]:i.length===4&&i[0]*i[1]*i[2]<=o&&i[3]<=o?[i[0]*i[1]*i[2],i[3]]:i.length===4&&i[0]<=o&&i[1]*i[2]*i[3]<=o?[i[0],i[1]*i[2]*i[3]]:e?Bf(a/4).map(s=>s*2):Bf(a)}}});var pr,zf=y(()=>{"use strict";V();he();q();Xi();ue();pr=class extends Tt{constructor(n){super(n)}getFunctions(){return{...this.offsetToCoords(),...this.coordsToOffset(),...this.toVec(),...this.valueFrom(),...this.getCommonUtilFuncs(),...this.getInputsSamplingSnippets(),...this.getOutputSamplingSnippet()}}getCustomTypes(){return{}}offsetToCoords(){let n="offsetToCoords";return{offsetToCoords:new v(`
      vec2 ${n}(int offset, int width, int height) {
        int t = offset / width;
        int s = offset - t*width;
        vec2 coords = (vec2(s,t) + vec2(0.5,0.5)) / vec2(width, height);
        return coords;
      }
      `)}}coordsToOffset(){let n="coordsToOffset";return{coordsToOffset:new v(`
      int ${n}(vec2 coords, int width, int height) {
        float s = coords.s * float(width);
        float t = coords.t * float(height);
        int offset = int(t) * width + int(s);
        return offset;
      }
      `)}}getOutputSamplingSnippet(){let n=this.context.outputTextureLayout;return n.isPacked?this.getPackedOutputSamplingSnippet(n):this.getUnpackedOutputSamplingSnippet(n)}getPackedOutputSamplingSnippet(n){let e=n.unpackedShape,o=[n.width,n.height],i={},a="getOutputCoords";switch(e.length){case 0:i[a]=this.getOutputScalarCoords();break;case 1:i[a]=this.getOutputPacked1DCoords(e,o);break;case 2:i[a]=this.getOutputPacked2DCoords(e,o);break;case 3:i[a]=this.getOutputPacked3DCoords(e,o);break;default:i[a]=this.getOutputPackedNDCoords(e,o)}let u=`
      void setOutput(vec4 val) {
        ${A(this.context.glContext.version).output} = val;
      }
    `,l="floatTextureSetRGBA";return i[l]=new v(u),i}getUnpackedOutputSamplingSnippet(n){let e=n.unpackedShape,o=[n.width,n.height],i={},a="getOutputCoords";switch(e.length){case 0:i[a]=this.getOutputScalarCoords();break;case 1:i[a]=this.getOutputUnpacked1DCoords(e,o);break;case 2:i[a]=this.getOutputUnpacked2DCoords(e,o);break;case 3:i[a]=this.getOutputUnpacked3DCoords(e,o);break;case 4:i[a]=this.getOutputUnpacked4DCoords(e,o);break;case 5:i[a]=this.getOutputUnpacked5DCoords(e,o);break;case 6:i[a]=this.getOutputUnpacked6DCoords(e,o);break;default:throw new Error(`Unsupported output dimensionality: ${e.length}`)}let u=`
        void setOutput(float val) {
          ${A(this.context.glContext.version).output} = vec4(val, 0, 0, 0);
        }
    `,l="floatTextureSetR";return i[l]=new v(u),i}getOutputScalarCoords(){return new v(`
      int getOutputCoords() {
        return 0;
      }
    `)}getOutputPacked1DCoords(n,e){let o=e,i="";return o[0]===1?(i=`
          int getOutputCoords() {
            return 2 * int(TexCoords.y * ${o[1]}.0);
          }
        `,new v(i)):o[1]===1?(i=`
          int getOutputCoords() {
            return 2 * int(TexCoords.x * ${o[0]}.0);
          }
        `,new v(i)):(i=`
        int getOutputCoords() {
          ivec2 resTexRC = ivec2(TexCoords.xy *
                                 vec2(${o[0]}, ${o[1]}));
          return 2 * (resTexRC.y * ${o[0]} + resTexRC.x);
        }
      `,new v(i))}getOutputPacked2DCoords(n,e){let o="";if(Le.arraysEqual(n,e))return o=`
        ivec2 getOutputCoords() {
          return 2 * ivec2(TexCoords.xy * vec2(${e[0]}, ${e[1]}));
        }
      `,new v(o);let i=e,a=Math.ceil(n[1]/2);return o=`
        ivec2 getOutputCoords() {
          ivec2 resTexRC = ivec2(TexCoords.xy *
                                vec2(${i[0]}, ${i[1]}));

          int index = resTexRC.y * ${i[0]} + resTexRC.x;

          // reverse r and c order for packed texture
          int r = imod(index, ${a}) * 2;
          int c = 2 * (index / ${a});

          return ivec2(r, c);
        }
      `,new v(o)}getOutputPacked3DCoords(n,e){let o=[e[0],e[1]],i=Math.ceil(n[2]/2),a=i*Math.ceil(n[1]/2),s=`
        ivec3 getOutputCoords() {
          ivec2 resTexRC = ivec2(TexCoords.xy *
                                vec2(${o[0]}, ${o[1]}));
          int index = resTexRC.y * ${o[0]} + resTexRC.x;

          int b = index / ${a};
          index -= b * ${a};

          // reverse r and c order for packed texture
          int r = imod(index, ${i}) * 2;
          int c = 2 * (index / ${i});

          return ivec3(b, r, c);
        }
      `;return new v(s)}getOutputPackedNDCoords(n,e){let o=[e[0],e[1]],i=Math.ceil(n[n.length-1]/2),a=i*Math.ceil(n[n.length-2]/2),s=a,u="",l="b, r, c";for(let d=2;d<n.length-1;d++)s*=n[n.length-d-1],u=`
      int b${d} = index / ${s};
      index -= b${d} * ${s};
    `+u,l=`b${d}, `+l;let c=`
      ivec${n.length} getOutputCoords() {
        ivec2 resTexRC = ivec2(TexCoords.xy *
                              vec2(${o[0]}, ${o[1]}));
        int index = resTexRC.y * ${o[0]} + resTexRC.x;

        ${u}

        int b = index / ${a};
        index -= b * ${a};

        // reverse r and c order for packed texture
        int r = imod(index, ${i}) * 2;
        int c = 2 * (index / ${i});

        return ivec${n.length}(${l});
      }
    `;return new v(c)}getOutputUnpacked1DCoords(n,e){let o=`
        int getOutputCoords() {
          ivec2 resTexRC = ivec2(TexCoords.xy *
                                vec2(${e[0]}, ${e[1]}));
          return resTexRC.y * ${e[0]} + resTexRC.x;
        }
      `;return new v(o)}getOutputUnpacked2DCoords(n,e){let o=`
        ivec2 getOutputCoords() {
          ivec2 resTexRC = ivec2(TexCoords.xy *
                                vec2(${e[0]}, ${e[1]}));
          int index = resTexRC.y * ${e[0]} + resTexRC.x;
          int r = index / ${n[1]};
          int c = index - r * ${n[1]};
          return ivec2(r, c);
        }
      `;return new v(o)}getOutputUnpacked3DCoords(n,e){let o="",i=n.length,a=null;i<2&&(a=[]),a=new Array(i-1),a[i-2]=n[i-1];for(let l=i-3;l>=0;--l)a[l]=a[l+1]*n[l+1];let s=["r","c","d"],u=a.map((l,c)=>{let d=`int ${s[c]} = index / ${l}`,p=c===a.length-1?`int ${s[c+1]} = index - ${s[c]} * ${l}`:`index -= ${s[c]} * ${l}`;return`${d}; ${p};`}).join("");return o=`
        ivec3 getOutputCoords() {
          ivec2 resTexRC = ivec2(TexCoords.xy *
                                vec2(${e[0]}, ${e[1]}));
          int index = resTexRC.y * ${e[0]} + resTexRC.x;
          ${u}
          return ivec3(r, c, d);
        }
      `,new v(o)}getOutputUnpacked4DCoords(n,e){let o="",i=n.length,a=null;i<2&&(a=[]),a=new Array(i-1),a[i-2]=n[i-1];for(let l=i-3;l>=0;--l)a[l]=a[l+1]*n[l+1];let s=["r","c","d","d2"],u=a.map((l,c)=>{let d=`int ${s[c]} = index / ${l}`,p=c===a.length-1?`int ${s[c+1]} = index - ${s[c]} * ${l}`:`index -= ${s[c]} * ${l}`;return`${d}; ${p};`}).join("");return o=`
      ivec4 getOutputCoords() {
          ivec2 resTexRC = ivec2(TexCoords.xy *
                                vec2(${e[0]}, ${e[1]}));
          int index = resTexRC.y * ${e[0]} + resTexRC.x;
          ${u}
          return ivec4(r, c, d, d2);
        }
      `,new v(o)}getOutputUnpacked5DCoords(n,e){let o="",i=n.length,a=null;i<2&&(a=[]),a=new Array(i-1),a[i-2]=n[i-1];for(let l=i-3;l>=0;--l)a[l]=a[l+1]*n[l+1];let s=["r","c","d","d2","d3"],u=a.map((l,c)=>{let d=`int ${s[c]} = index / ${l}`,p=c===a.length-1?`int ${s[c+1]} = index - ${s[c]} * ${l}`:`index -= ${s[c]} * ${l}`;return`${d}; ${p};`}).join("");return o=`
      ivec5 getOutputCoords() {
          ivec2 resTexRC = ivec2(TexCoords.xy *
                                vec2(${e[0]}, ${e[1]}));
          int index = resTexRC.y * ${e[0]} + resTexRC.x;
          ${u}
          return ivec5(r, c, d, d2, d3);
        }
      `,new v(o)}getOutputUnpacked6DCoords(n,e){let o="",i=n.length,a=null;i<2&&(a=[]),a=new Array(i-1),a[i-2]=n[i-1];for(let l=i-3;l>=0;--l)a[l]=a[l+1]*n[l+1];let s=["r","c","d","d2","d3","d4"],u=a.map((l,c)=>{let d=`int ${s[c]} = index / ${l}`,p=c===a.length-1?`int ${s[c+1]} = index - ${s[c]} * ${l}`:`index -= ${s[c]} * ${l}`;return`${d}; ${p};`}).join("");return o=`
     ivec6 getOutputCoords() {
         ivec2 resTexRC = ivec2(TexCoords.xy *
                               vec2(${e[0]}, ${e[1]}));
         int index = resTexRC.y * ${e[0]} + resTexRC.x;
         ${u}
         return ivec6(r, c, d, d2, d3, d4);
       }
     `,new v(o)}getCommonUtilFuncs(){let n={},e="uvFromFlat";n[e]=new v(`
    vec2 uvFromFlat(int texNumR, int texNumC, int index) {
      int texC = index / texNumR;
      int texR = index - texC * texNumR;
      // TODO: swap texR, texC order in following function so row is corresponding to u and column is corresponding to
      //       v.
      return (vec2(texR, texC) + halfCR) / vec2(texNumR, texNumC);
    }
    `),e="packedUVfrom1D",n[e]=new v(`
      vec2 packedUVfrom1D(int texNumR, int texNumC, int index) {
        int texelIndex = index / 2;
        int texR = texelIndex / texNumC;
        int texC = texelIndex - texR * texNumC;
        return (vec2(texC, texR) + halfCR) / vec2(texNumC, texNumR);
      }
      `),e="packedUVfrom2D",n[e]=new v(`
      vec2 packedUVfrom2D(int texNumR, int texNumC, int texelsInLogicalRow, int row, int col) {
        int texelIndex = (row / 2) * texelsInLogicalRow + (col / 2);
        int texR = texelIndex / texNumC;
        int texC = texelIndex - texR * texNumC;
        return (vec2(texC, texR) + halfCR) / vec2(texNumC, texNumR);
      }
      `),e="packedUVfrom3D",n[e]=new v(`
      vec2 packedUVfrom3D(int texNumR, int texNumC,
          int texelsInBatch, int texelsInLogicalRow, int b,
          int row, int col) {
        int index = b * texelsInBatch + (row / 2) * texelsInLogicalRow + (col / 2);
        int texR = index / texNumC;
        int texC = index - texR * texNumC;
        return (vec2(texC, texR) + halfCR) / vec2(texNumC, texNumR);
      }
      `),e="sampleTexture";let o=A(this.context.glContext.version);return n[e]=new v(`
        float sampleTexture(sampler2D textureSampler, vec2 uv) {
            return ${o.texture2D}(textureSampler, uv).r;
        }`),n}getInputsSamplingSnippets(){let n={},e=this.context.outputTextureLayout;return this.context.programInfo.inputNames.forEach((o,i)=>{let a=this.context.inputTextureLayouts[i],s=Yn(o);a.isPacked?n[s]=this.getPackedSamplerFromInput(s,o,a):n[s]=this.getUnpackedSamplerFromInput(s,o,a);let u=Au(o);a.unpackedShape.length<=e.unpackedShape.length&&(a.isPacked?n[u]=this.getPackedSamplerAtOutputCoords(u,a,e,o):n[u]=this.getUnpackedSamplerAtOutputCoords(u,a,e,o))}),n}getPackedSamplerAtOutputCoords(n,e,o,i){let a=e.unpackedShape,s=o.unpackedShape,l=Yn(i),c=a.length,d=s.length,p=ot.getBroadcastDims(a,s),m=it(d),b=d-c,g,x=St();c===0?g="":d<2&&p.length>=1?g="coords = 0;":g=p.map(Ie=>`coords.${x[Ie+b]} = 0;`).join(`
`);let w="";d<2&&c>0?w="coords":w=a.map((Ie,Tn)=>`coords.${x[Tn+b]}`).join(", ");let I="return outputValue;",z=O.size(a)===1,at=O.size(s)===1;if(c===1&&!z&&!at)I=`
        return vec4(outputValue.xy, outputValue.xy);
      `;else if(z&&!at)d===1?I=`
          return vec4(outputValue.x, outputValue.x, 0., 0.);
        `:I=`
          return vec4(outputValue.x);
        `;else if(p.length){let Ie=c-2,Tn=c-1;p.indexOf(Ie)>-1&&p.indexOf(Tn)>-1?I="return vec4(outputValue.x);":p.indexOf(Ie)>-1?I="return vec4(outputValue.x, outputValue.y, outputValue.x, outputValue.y);":p.indexOf(Tn)>-1&&(I="return vec4(outputValue.xx, outputValue.zz);")}let At=`
        int lastDim = coords.${x[d-1]};
        coords.${x[d-1]} = coords.${x[d-2]};
        coords.${x[d-2]} = lastDim;
      `,Ar=`
      vec4 ${n}() {
        ${m} coords = getOutputCoords();
        ${At}
        ${g}
        vec4 outputValue = ${l}(${w});
        ${I}
      }
    `;return new v(Ar,["coordinates.getOutputCoords"])}getUnpackedSamplerAtOutputCoords(n,e,o,i){let a=[o.width,o.height],s=[e.width,e.height],u=e.unpackedShape.length,l=o.unpackedShape.length,c=e.unpackedShape,d=o.unpackedShape,p=Yn(i);if(u===l&&Le.arraysEqual(s,a)){let z=`
          float ${n}() {
            return sampleTexture(${i}, TexCoords);
          }
        `;return new v(z,["coordinates.sampleTexture"])}let m=it(l),b=ot.getBroadcastDims(c,d),g=l-u,x,w=St();u===0?x="":l<2&&b.length>=1?x="coords = 0;":x=b.map(z=>`coords.${w[z+g]} = 0;`).join(`
`);let I="";l<2&&u>0?I="coords":I=e.unpackedShape.map((z,F)=>`coords.${w[F+g]}`).join(", ");let D=`
        float ${n}() {
          ${m} coords = getOutputCoords();
          ${x}
          return ${p}(${I});
        }
      `;return new v(D,["coordinates.getOutputCoords"])}getPackedSamplerFromInput(n,e,o){switch(o.unpackedShape.length){case 0:return this.getPackedSamplerScalar(n,e);case 1:return this.getPackedSampler1D(n,e,o);case 2:return this.getPackedSampler2D(n,e,o);case 3:return this.getPackedSampler3D(n,e,o);default:return this.getPackedSamplerND(n,e,o)}}getUnpackedSamplerFromInput(n,e,o){let i=o.unpackedShape;switch(i.length){case 0:return this.getUnpackedSamplerScalar(n,e,o);case 1:return this.getUnpackedSampler1D(n,e,o);case 2:return this.getUnpackedSampler2D(n,e,o);case 3:return this.getUnpackedSampler3D(n,e,o);case 4:return this.getUnpackedSampler4D(n,e,o);case 5:return this.getUnpackedSampler5D(n,e,o);case 6:return this.getUnpackedSampler6D(n,e,o);default:throw new Error(`Unsupported dimension ${i.length}-D`)}}getPackedSamplerScalar(n,e){let o=A(this.context.glContext.version),i=`
          vec4 ${n}() {
            return ${o.texture2D}(${e}, halfCR);
          }
        `;return new v(i)}getPackedSampler1D(n,e,o){let i=[o.width,o.height],a=[i[1],i[0]],s=A(this.context.glContext.version),l=`vec4 ${n}(int index) {
      vec2 uv = packedUVfrom1D(
      ${a[0]}, ${a[1]}, index);
      return ${s.texture2D}(${e}, uv);
    }`;return new v(l,["coordinates.packedUVfrom1D"])}getPackedSampler2D(n,e,o){let i=o.unpackedShape,a=[o.width,o.height],s=A(this.context.glContext.version),u=a[0],l=a[1];if(a!=null&&Le.arraysEqual(i,a)){let b=`vec4 ${n}(int row, int col) {
        vec2 uv = (vec2(col, row) + halfCR) / vec2(${l}.0, ${u}.0);
        return ${s.texture2D}(${e}, uv);
      }`;return new v(b)}let c=a,d=Math.ceil(i[1]/2),m=`vec4 ${n}(int row, int col) {
      vec2 uv = packedUVfrom2D(${c[1]}, ${c[0]}, ${d}, row, col);
      return ${s.texture2D}(${e}, uv);
    }`;return new v(m,["coordinates.packedUVfrom2D"])}getPackedSampler3D(n,e,o){let i=o.unpackedShape,a=[o.width,o.height],s=[a[0],a[1]],u=A(this.context.glContext.version);if(i[0]===1){let g=i.slice(1),x=[1,2],w=We(i,g),I=["b","row","col"],D=JSON.parse(JSON.stringify(o));D.unpackedShape=w;let z=this.getPackedSamplerFromInput(n,e,D),at=`${z.routineBody}
      vec4 ${n}(int b, int row, int col) {
        return ${n}(${He(I,x)});
      } `;return new v(at,z.dependencies)}let l=s[0],c=s[1],d=Math.ceil(i[2]/2),p=d*Math.ceil(i[1]/2),b=`vec4 ${n}(int b, int row, int col) {
      vec2 uv = packedUVfrom3D(
        ${c}, ${l}, ${p}, ${d}, b, row, col);
      return ${u.texture2D}(${e}, uv);}`;return new v(b,["coordinates.packedUVfrom3D"])}getPackedSamplerND(n,e,o){let i=o.unpackedShape,a=i.length,s=[o.width,o.height],u=A(this.context.glContext.version),l=[s[0],s[1]],c=l[1],d=l[0],p=Math.ceil(i[a-1]/2),m=p*Math.ceil(i[a-2]/2),b="int b, int row, int col",g=`b * ${m} + (row / 2) * ${p} + (col / 2)`;for(let I=2;I<a-1;I++)b=`int b${I}, `+b,m*=i[a-I-1],g=`b${I} * ${m} + `+g;let w=`vec4 ${n}(${b}) {
      int index = ${g};
      int texR = index / ${d};
      int texC = index - texR * ${d};
      vec2 uv = (vec2(texC, texR) + halfCR) / vec2(${d}, ${c});
      return ${u.texture2D}(${e}, uv);
    }`;return new v(w)}getUnpackedSamplerScalar(n,e,o){let[i,a]=[o.width,o.height];if(i===1&&a===1){let u=`
          float ${n}() {
            return sampleTexture(${e}, halfCR);
          }
        `;return new v(u,["coordinates.sampleTexture"])}let s=`
        float ${n}() {
          int offset_${e} = coordsToOffset(TexCoords, ${i}, ${a});
          vec2 uv = uvFromFlat(${i}, ${a}, offset_${e});
          return sampleTexture(${e}, uv);
        }
      `;return new v(s,["coordinates.uvFromFlat","coordinates.sampleTexture","coordinates.coordsToOffset"])}getUnpackedSampler1D(n,e,o){let i=o.width,a=o.height;if(a===1&&i===1){let u=`
        float ${n}(int index) {
          return sampleTexture(${e}, halfCR);
        }
      `;return new v(u,["coordinates.sampleTexture"])}if(a===1){let u=`
          float ${n}(int index) {
            vec2 uv = vec2((float(index) + 0.5) / ${i}.0, 0.5);
            return sampleTexture(${e}, uv);
          }
        `;return new v(u,["coordinates.sampleTexture"])}if(i===1){let u=`
          float ${n}(int index) {
            vec2 uv = vec2(0.5, (float(index) + 0.5) / ${a}.0);
            return sampleTexture(${e}, uv);
          }
        `;return new v(u,["coordinates.sampleTexture"])}let s=`
        float ${n}(int index) {
          vec2 uv = uvFromFlat(${i}, ${a}, index);
          return sampleTexture(${e}, uv);
        }
      `;return new v(s,["coordinates.uvFromFlat","coordinates.sampleTexture"])}getUnpackedSampler2D(n,e,o){let i=o.unpackedShape,a=[o.height,o.width];if(a!=null&&Le.arraysEqual(i,a)){let m=a[1],b=a[0],g=`
          float ${n}(int row, int col) {
            vec2 uv = (vec2(row, col) + halfCR) / vec2(${m}.0, ${b}.0);
            return sampleTexture(${e}, uv);
          }
        `;return new v(g,["coordinates.sampleTexture"])}let{newShape:s,keptDims:u}=Ze(i),l=s;if(l.length<i.length){let m=We(i,l),b=JSON.parse(JSON.stringify(o));b.unpackedShape=m;let g=["col","row"],x=`
          ${this.getUnpackedSamplerFromInput(n,e,b).routineBody}
          float ${n}(int row, int col) {
            return ${n}(${He(g,u)});
          }
        `;return new v(x,["coordinates.sampleTexture"])}let c=a[1],d=a[0];if(d===1){let m=`
          float ${n}(int row, int col) {
            int offset_${e} = coordsToOffset(TexCoords, ${c}, ${d});
            float index = dot(vec3(row, col, offset_${e}), vec3(${i[1]}, 1, 1));
            vec2 uv = vec2(0.5, (index + 0.5) / ${c}.0);
            return sampleTexture(${e}, uv);
          }
        `;return new v(m,["coordinates.sampleTexture","coordinates.coordsToOffset"])}if(c===1){let m=`
          float ${n}(int row, int col) {
            int offset_${e} = coordsToOffset(TexCoords, ${c}, ${d});
            float index = dot(vec3(row, col, offset_${e}), vec3(${i[1]}, 1, 1));
            vec2 uv = vec2((index + 0.5) / ${d}.0, 0.5);
            return sampleTexture(${e}, uv);
          }
        `;return new v(m,["coordinates.sampleTexture","coordinates.coordsToOffset"])}let p=`
        float ${n}(int row, int col) {
          int index = col * ${i[1]} + row;
          vec2 uv = uvFromFlat(${c}, ${d}, index);
          return sampleTexture(${e}, uv);
        }
      `;return new v(p,["coordinates.uvFromFlat","coordinates.sampleTexture","coordinates.coordsToOffset"])}getUnpackedSampler3D(n,e,o){let i=o.unpackedShape,a=i[1]*i[2],s=i[2],{newShape:u,keptDims:l}=Ze(i),c=u;if(c.length<i.length){let b=We(i,c),g=["batch","col","row"],x=JSON.parse(JSON.stringify(o));x.unpackedShape=b;let w=this.getUnpackedSamplerFromInput(n,e,x),I=l.reverse(),D=`
          ${w.routineBody}
          float ${n}(int batch, int row, int col) {
            return ${n}(${He(g,I)});
          }
        `;return new v(D,w.dependencies)}let d=o.width,p=o.height,m=`
          float ${n}(int depth, int row, int col) {
            // Explicitly use integer operations as dot() only works on floats.
            int index = depth * ${a} + col * ${s} + row;
            vec2 uv = uvFromFlat(${d}, ${p}, index);
            return sampleTexture(${e}, uv);
          }
      `;return new v(m,["coordinates.uvFromFlat","coordinates.sampleTexture","coordinates.coordsToOffset"])}getUnpackedSampler4D(n,e,o){let i=o.unpackedShape,a=i[3],s=i[2]*a,u=i[1]*s,l=o.width,c=o.height,d=`
        float ${n}(int row, int col, int depth, int depth2) {
          int index = row * ${u} + col * ${s} +
              depth2 * ${a} + depth;
          vec2 uv = uvFromFlat(${l}, ${c}, index);
          return sampleTexture(${e}, uv);
        }
      `;return new v(d,["coordinates.uvFromFlat","coordinates.sampleTexture"])}getUnpackedSampler5D(n,e,o){let i=o.unpackedShape,a=i[4],s=i[3]*a,u=i[2]*s,l=i[1]*u,{newShape:c,keptDims:d}=Ze(i);if(c.length<i.length){let g=We(i,c),x=["row","col","depth","depth2","depth3"],w=JSON.parse(JSON.stringify(o));w.unpackedShape=g;let I=`
          ${this.getUnpackedSamplerFromInput(n,e,w).routineBody}
          float ${n}(int row, int col, int depth, int depth2, int depth3) {
            return ${n}(${He(x,d)});
          }
        `;return new v(I,["coordinates.sampleTexture","coordinates.uvFromFlat"])}let p=o.width,m=o.height,b=`
        float ${n}(int row, int col, int depth, int depth2, int depth3) {
          int index = row * ${l} + col * ${u} + depth * ${s} +
          depth3 * ${a} + depth2;
          vec2 uv = uvFromFlat(${p}, ${m}, index);
          return sampleTexture(${e}, uv);
        }
      `;return new v(b,["coordinates.sampleTexture","coordinates.uvFromFlat"])}getUnpackedSampler6D(n,e,o){let i=o.unpackedShape,a=i[5],s=i[4]*a,u=i[3]*s,l=i[2]*u,c=i[1]*l,{newShape:d,keptDims:p}=Ze(i);if(d.length<i.length){let x=We(i,d),w=["row","col","depth","depth2","depth3","depth4"],I=JSON.parse(JSON.stringify(o));I.unpackedShape=x;let D=`
            ${this.getUnpackedSamplerFromInput(n,e,I).routineBody}
            float ${n}(int row, int col, int depth,
              int depth2, int depth3, int depth4) {
              return ${n}(${He(w,p)});
            }
          `;return new v(D,["coordinates.sampleTexture","coordinates.uvFromFlat"])}let m=o.width,b=o.height,g=`
          float ${n}(int row, int col, int depth,
            int depth2, int depth3, int depth4) {
            int index = row * ${c} + col * ${l} + depth * ${u} +
            depth2 * ${s} + depth3 * ${a} + depth4;
            vec2 uv = uvFromFlat(${m}, ${b}, index);
            return sampleTexture(${e}, uv);
          }
        `;return new v(g,["coordinates.uvFromFlat","coordinates.sampleTexture","coordinates.coordsToOffset"])}toVec(){let n=this.context.outputTextureLayout,e=n.shape.length,o=n.strides,i=n.width,a=n.height,s=[];for(let l=0;l<e-1;++l)s.push(`
        c[${l}] = offset / ${o[l]};`),s.push(`
        offset -= c[${l}] * ${o[l]};`);s.push(`
        c[${e-1}] = offset;`);let u=`
      void toVec(vec2 texCoords, out int c[${e}]) {
        int offset = coordsToOffset(texCoords, ${i}, ${a});
        ${s.join("")}
      }
      void toVec(int offset, out int c[${e}]) {
        ${s.join("")}
      }
    `;return{toVec:new v(u,["coordinates.coordsToOffset"])}}valueFrom(){let n={};return this.context.programInfo.inputNames.forEach((e,o)=>{let i=this.context.inputTextureLayouts[o],s=(i.unpackedShape.length>0?i.unpackedShape:i.shape).length,u=`_${e}`;n[u]=new v(this.getValueFromSingle(e,s,i.width,i.height,!1),[`shapeUtils.indicesToOffset${u}`,"coordinates.offsetToCoords","fragcolor.getColorAsFloat"]),u=u+"_T",n[u]=new v(this.getValueFromSingle(e,s,i.width,i.height,!0),[`shapeUtils.indicesToOffset${u}`,"coordinates.offsetToCoords","fragcolor.getColorAsFloat"])}),n}getValueFromSingle(n,e,o,i,a){let s=`_${n}`;a&&(s=s+"_T");let u=A(this.context.glContext.version);return`
        float ${s}(int m[${e}]) {
          int offset = indicesToOffset${s}(m);
          vec2 coords = offsetToCoords(offset, ${o}, ${i});
          float value = getColorAsFloat(${u.texture2D}(${n}, coords));
          return value;
        }
        `}getPackedValueFrom(n,e,o,i,a){let s=`_${n}_Pack`;a&&(s=s+"_T");let u=A(this.context.glContext.version);return`
        vec4 ${s}(int m[${e}]) {
          int offset = indicesToOffset_${n}(m);
          vec2 coords = offsetToCoords(offset, ${o}, ${i});
          return ${u.texture2D}(${n}, coords);
        }
        `}}});var hr,Uf=y(()=>{"use strict";he();hr=class r extends Tt{constructor(t){super(t)}getFunctions(){return{...this.encodeFloat32(),...this.decodeFloat32()}}getCustomTypes(){return{}}encodeFloat32(){return{encode:new v(`highp vec4 encode(highp float f) {
        return vec4(f, 0.0, 0.0, 0.0);
      }
        `)}}decodeFloat32(){return{decode:new v(`highp float decode(highp vec4 rgba) {
        return rgba.r;
      }
        `)}}encodeUint8(){let t=r.isLittleEndian()?"rgba.rgba=rgba.abgr;":"";return{encode:new v(`
      highp vec4 encode(highp float f) {
        highp float F = abs(f);
        highp float Sign = step(0.0,-f);
        highp float Exponent = floor(log2(F));
        highp float Mantissa = (exp2(- Exponent) * F);
        Exponent = floor(log2(F) + 127.0) + floor(log2(Mantissa));
        highp vec4 rgba;
        rgba[0] = 128.0 * Sign  + floor(Exponent*exp2(-1.0));
        rgba[1] = 128.0 * mod(Exponent,2.0) + mod(floor(Mantissa*128.0),128.0);
        rgba[2] = floor(mod(floor(Mantissa*exp2(23.0 -8.0)),exp2(8.0)));
        rgba[3] = floor(exp2(23.0)*mod(Mantissa,exp2(-15.0)));
        ${t}
        rgba = rgba / 255.0; // values need to be normalized to [0,1]
        return rgba;
    }
        `)}}decodeUint8(){let t=r.isLittleEndian()?"rgba.rgba=rgba.abgr;":"";return{decode:new v(`
        highp float decode(highp vec4 rgba) {
          rgba = rgba * 255.0; // values need to be de-normalized from [0,1] to [0,255]
          ${t}
          highp float Sign = 1.0 - step(128.0,rgba[0])*2.0;
          highp float Exponent = 2.0 * mod(rgba[0],128.0) + step(128.0,rgba[1]) - 127.0;
          highp float Mantissa = mod(rgba[1],128.0)*65536.0 + rgba[2]*256.0 +rgba[3] + float(0x800000);
          highp float Result =  Sign * exp2(Exponent) * (Mantissa * exp2(-23.0 ));
          return Result;
      }
        `)}}static isLittleEndian(){let t=new ArrayBuffer(4),n=new Uint32Array(t),e=new Uint8Array(t);if(n[0]=3735928559,e[0]===239)return!0;if(e[0]===222)return!1;throw new Error("unknown endianness")}}});var mr,jf=y(()=>{"use strict";he();q();mr=class extends Tt{constructor(t){super(t)}getFunctions(){return{...this.setFragColor(),...this.getColorAsFloat()}}getCustomTypes(){return{}}setFragColor(){let t=A(this.context.glContext.version);return{setFragColor:new v(`
        void setFragColor(float value) {
            ${t.output} = encode(value);
        }
        `,["encoding.encode"])}}getColorAsFloat(){return{getColorAsFloat:new v(`
        float getColorAsFloat(vec4 color) {
            return decode(color);
        }
        `,["encoding.decode"])}}}});var br,Wf=y(()=>{"use strict";he();br=class r extends Tt{constructor(t){super(t)}getFunctions(){return{...this.bcastIndex(),...this.bcastMatmulIndex(),...this.offsetToIndices(),...this.indicesToOffset(),...this.incrementIndices()}}getCustomTypes(){return{}}bcastIndex(){let t=this.context.outputTextureLayout.shape.length,n={};return this.context.programInfo.inputNames.forEach((e,o)=>{let i=this.context.inputTextureLayouts[o].unpackedShape;if(i.length<=t){let a=i.length,s=t-a,u=`bcastIndices_${e}`,l="";for(let d=0;d<a;++d)l+=`
          realIndices[${d}] = int( mod(float(bcastedIndices[${s+d}]), ${i[d]}.0) );
          `;let c=`
        void ${u} (int bcastedIndices[${t}], out int realIndices[${a}]) {
          ${l}
        }
        `;n[u]=new v(c)}}),n}bcastMatmulIndex(){let t=this.context.outputTextureLayout.shape.length,n={};return this.context.programInfo.inputNames.forEach((e,o)=>{let i=this.context.inputTextureLayouts[o].shape;if(!(i.length<2||i.length>t)){let a=i.length,s=t-a,u=`bcastMatmulIndices_${e}`,l="";for(let d=0;d<a-2;++d)l+=`
          realIndices[${d}] = int( mod(float(bcastedIndices[${s+d}]), ${i[d]}.0) );
          `;let c=`
        void ${u}(int bcastedIndices[${t}], out int realIndices[${a}]) {
          ${l}
          realIndices[${a-1}] = bcastedIndices[${t-1}];
          realIndices[${a-2}] = bcastedIndices[${t-2}];
        }
        `;n[u]=new v(c)}}),n}indicesToOffset(){let t={};return this.context.programInfo.inputNames.forEach((n,e)=>{let o=this.context.inputTextureLayouts[e].shape,i=this.context.inputTextureLayouts[e].strides,a=o.length,s=`indicesToOffset_${n}`;t[s]=new v(r.indexToOffsetSingle(s,a,i)),s=`indicesToOffset_${n}_T`,t[s]=new v(r.indexToOffsetSingle(s,a,i.slice().reverse()))}),t}static indexToOffsetSingle(t,n,e){let o="";for(let i=n-1;i>=0;--i)o+=`
        offset += indices[${i}] * ${e[i]};
        `;return`
      int ${t}(int indices[${n}]) {
        int offset = 0;
        ${o}
        return offset;
      }
      `}offsetToIndices(){let t={};return this.context.programInfo.inputNames.forEach((n,e)=>{let o=this.context.inputTextureLayouts[e].shape,i=this.context.inputTextureLayouts[e].strides,a=o.length,s=`offsetToIndices_${n}`;t[s]=new v(r.offsetToIndicesSingle(s,a,i)),s=`offsetToIndices_${n}_T`,t[s]=new v(r.offsetToIndicesSingle(s,a,i.slice().reverse()))}),t}static offsetToIndicesSingle(t,n,e){let o=[];for(let i=0;i<n-1;++i)o.push(`
      indices[${i}] = offset / ${e[i]};`),o.push(`
        offset -= indices[${i}] * ${e[i]};`);return o.push(`
      indices[${n-1}] = offset;`),`
      void ${t}(int offset, out int indices[${n}]) {
        ${o.join("")}
      }
      `}incrementIndices(){let t={};return this.context.programInfo.inputNames.forEach((n,e)=>{let o=this.context.inputTextureLayouts[e].shape,i=o.length,a=`incrementIndices_${n}`,s="";for(let l=0;l<i;++l)s+=`
        shape[${l}] = ${o[l]};`;let u=`
        void ${a}(int axis, out int indices[${i}]) {
          int shape[${i}];
          ${s};
          for(int i = ${i} -1 ; i >= 0; --i) {
            if(i > axis) continue;
            indices[i] += 1;
            if(indices[i] < shape[i]) {
              break;
            }
            indices[i] = 0;
          }
        }
        `;t[a]=new v(u)}),t}}});var gr,Hf=y(()=>{"use strict";he();gr=class extends Tt{constructor(t){super(t)}getCustomTypes(){return{}}getFunctions(){return{...this.binaryVecFunctions(),...this.copyVec(),...this.setVecItem(),...this.getVecItem()}}binaryVecFunctions(){let n=this.context.outputTextureLayout.shape.length,e={add:"+=",sub:"-=",mul:"*=",div:"/="},o={};for(let i in e){let a=`${i}Vec`,s="";for(let l=0;l<n;++l)s+=`
          dest[${l}] ${e[i]} src[${l}];
          `;let u=`
        void ${a}(int src[${n}], out int dest[${n}]) {
          ${s}
        }
        `;o[a]=new v(u)}return o}copyVec(){let n=this.context.outputTextureLayout.shape.length,e="";for(let i=0;i<n;++i)e+=`
        dest[${i}] = src[${i}];
        `;let o=`
      void copyVec(int src[${n}], out int dest[${n}]) {
        ${e}
      }
      `;return{copyVec:new v(o)}}setVecItem(){let n=this.context.outputTextureLayout.shape.length,e=`
        if(index < 0)
            index =${n} + index;
        if (index == 0)
            m[0] = value;
        `;for(let i=1;i<n-1;++i)e+=`
        else if (index == ${i})
            m[${i}] = value;
            `;e+=`
        else
            m[${n-1}] = value;
        `;let o=`
      void setVecItem(out int m[${n}], int index, int value) {
        ${e}
      }
        `;return{setVecItem:new v(o)}}getVecItem(){let n=this.context.outputTextureLayout.shape.length,e=`
        if(index < 0)
            index = ${n} + index;
        if (index == 0)
            return m[0];
      `;for(let i=1;i<n-1;++i)e+=`
        else if (index == ${i})
            return m[${i}];
      `;e+=`
        else
            return m[${n-1}];
        `;let o=`
      int getVecItem(int m[${n}], int index) {
        ${e}
      }
    `;return{getVecItem:new v(o)}}}});var Zi,qf=y(()=>{"use strict";zf();Uf();jf();Wf();Hf();Zi={encoding:hr,fragcolor:mr,vec:gr,shapeUtils:br,coordinates:pr}});var yr,Kf=y(()=>{"use strict";he();Vf();qf();q();yr=class{constructor(t,n,e,o){this.libs={};this.glslLibRoutineDependencyGraph={};this.context=new nr(t,n,e,o),Object.keys(Zi).forEach(a=>{let s=new Zi[a](this.context);this.libs[a]=s});let i=this.glslLibRoutineDependencyGraph;for(let a in this.libs){let u=this.libs[a].getFunctions();for(let l in u){let c=a+"."+l,d;i[c]?(d=i[c],d.routineBody=u[l].routineBody):(d=new hn(c,u[l].routineBody),i[c]=d);let p=u[l].dependencies;if(p)for(let m=0;m<p.length;++m)if(i[p[m]])d.addDependency(i[p[m]]);else{let b=new hn(p[m]);i[p[m]]=b,d.addDependency(b)}}}}preprocess(){let t=this.context.programInfo,n=t.shaderSource;return this.context.programInfo.hasMain||(n=`${n}
      ${Su(this.context.glContext.version,this.context.outputTextureLayout.shape.length)}`),n=Mf(n),`${Pu(this.context.glContext.version)}
    ${this.getUniforms(t.inputNames,t.variables)}
    ${this.getImports(n)}
    ${n}`}getImports(t){let n=this.selectGlslLibRoutinesToBeIncluded(t);if(n.length===0)return"";let e="";for(let o=0;o<n.length;++o)if(n[o].routineBody)e+=n[o].routineBody+`
`;else throw new Error(`Missing body for the Glsl Library routine: ${n[o].name}`);return e}selectGlslLibRoutinesToBeIncluded(t){let n=[];return Object.keys(this.glslLibRoutineDependencyGraph).forEach(e=>{let o=e.split(".")[1];t.indexOf(o)!==-1&&n.push(this.glslLibRoutineDependencyGraph[e])}),rr.returnOrderedNodes(n)}getUniforms(t,n){let e=[];if(t)for(let o of t)e.push(`uniform sampler2D ${o};`);if(n)for(let o of n)e.push(`uniform ${o.type} ${o.name}${o.arrayLength?`[${o.arrayLength}]`:""};`);return e.join(`
`)}}});var Tr,Xf=y(()=>{"use strict";me();pt();Kf();q();Tr=class{constructor(t,n,e){this.profiler=t;this.glContext=n;this.textureLayoutStrategy=e;this.repo=new Map,this.attributesBound=!1}getArtifact(t){return this.repo.get(t)}setArtifact(t,n){this.repo.set(t,n)}run(t,n,e){this.profiler.event("op",`ProgramManager.run ${t.programInfo.name??"unknown kernel"}`,()=>{let o=this.glContext.gl,i=t.program;o.useProgram(i);try{this.bindOutput(e),this.attributesBound||this.bindAttributes(t.attribLocations),this.bindUniforms(t.uniformLocations,t.programInfo.variables??[],n)}catch(a){throw B.error("ProgramManager",t.programInfo.shaderSource),a}this.profiler.event("backend","GlContext.draw()",()=>{this.glContext.draw()})},this.glContext)}dispose(){this.vertexShader&&this.glContext.deleteShader(this.vertexShader),this.repo.forEach(t=>this.glContext.deleteProgram(t.program))}build(t,n,e){return this.profiler.event("backend","ProgramManager.build",()=>{let o=new yr(this.glContext,t,n,e),i=o.preprocess(),a=this.compile(i);return{programInfo:t,program:a,uniformLocations:this.getUniformLocations(a,o.context.programInfo.inputNames,o.context.programInfo.variables),attribLocations:this.getAttribLocations(a)}})}compile(t){if(!this.vertexShader){B.verbose("ProrgramManager","Compiling and caching Vertex shader for the first time");let o=Ou(this.glContext.version);this.vertexShader=this.glContext.compileShader(o,this.glContext.gl.VERTEX_SHADER)}K.debug&&B.verbose("ProrgramManager",`FragShader:
${t}
`);let n=this.glContext.compileShader(t,this.glContext.gl.FRAGMENT_SHADER),e=this.glContext.createProgram(this.vertexShader,n);return this.glContext.deleteShader(n),e}bindOutput(t){let n=t.width,e=t.height;B.verbose("ProrgramManager",`Binding output texture to Framebuffer: w/h=${n}/${e}, shape=${t.shape}, type=${t.tensor.type}`),this.glContext.attachFramebuffer(t.texture,n,e)}bindAttributes(t){let n=t.position,e=t.textureCoord;this.glContext.setVertexAttributes(n,e),this.attributesBound=!0}bindUniforms(t,n,e){let o=this.glContext.gl,i=0;for(let{name:a,type:s,location:u,arrayLength:l}of t){let c=n.find(d=>d.name===a)?.data;if(s!=="sampler2D"&&!c)throw new Error(`variable '${a}' does not have data defined in program info`);switch(s){case"sampler2D":this.bindTexture(e[i],u,i),i++;break;case"float":l?o.uniform1fv(u,c):o.uniform1f(u,c);break;case"int":l?o.uniform1iv(u,c):o.uniform1i(u,c);break;default:throw new Error(`Uniform not implemented: ${s}`)}}}bindTexture(t,n,e){this.glContext.bindTextureToUniform(t.texture,e,n)}getAttribLocations(t){return{position:this.getAttribLocation(t,"position"),textureCoord:this.getAttribLocation(t,"textureCoord")}}getUniformLocations(t,n,e){let o=[];if(n)for(let i of n)o.push({name:i,type:"sampler2D",location:this.getUniformLocation(t,i)});if(e)for(let i of e)o.push({...i,location:this.getUniformLocation(t,i.name)});return o}getUniformLocation(t,n){let o=this.glContext.gl.getUniformLocation(t,n);if(o===null)throw new Error(`Uniform ${n} not found.`);return o}getAttribLocation(t,n){return this.glContext.gl.getAttribLocation(t,n)}}});var _r,Zf=y(()=>{"use strict";pt();dn();_r=class{constructor(t,n,e,o){this.glContext=t;this.layoutStrategy=n;this.profiler=e;this.config=o;this.pendingRead=new Map;o.reuseTextures&&(this.inUseTextures=new Map,this.idleTextures=new Map,this.textureLookup=new Map)}createTextureFromLayout(t,n,e,o){let i=this.toEncoderType(t),a=this.glContext.getEncoder(i,n.channels||1,o);if(n.isPacked&&o===1)throw new Error("not implemented");let s=n.width,u=n.height,l,c;if(this.config.reuseTextures){l=`${s}x${u}_${a.format}_${a.internalFormat}_${a.textureType}`,c=this.inUseTextures.get(l),c||(c=[],this.inUseTextures.set(l,c));let p=this.idleTextures.get(l);if(p&&p.length>0){let m=p.pop();return c.push(m),o===1&&this.glContext.updateTexture(m,s,u,a,this.toTextureData(t,e)),m}}B.verbose("TextureManager",`Creating new texture of size ${n.width}x${n.height}`);let d=this.glContext.allocateTexture(s,u,a,this.toTextureData(t,e));return this.config.reuseTextures&&(c.push(d),this.textureLookup.set(d,l)),d}readTexture(t,n,e){return e||(e=1),this.profiler.event("backend","TextureManager.readTexture",()=>{let o=t.shape.reduce((a,s)=>a*s)*e,i=this.glContext.readTexture(t.texture,t.width,t.height,o,this.toEncoderType(n),e);return this.toTensorData(n,i)})}async readTextureAsync(t,n,e){let o=t.tensor.dataId;if(e||(e=1),this.pendingRead.has(o)){let i=this.pendingRead.get(o);return new Promise(a=>i?.push(a))}return this.profiler.event("backend","TextureManager.readTextureAsync",async()=>{this.pendingRead.set(o,[]);let i=t.shape.reduce((l,c)=>l*c)*e;await this.glContext.createAndWaitForFence();let a=this.glContext.readTexture(t.texture,t.width,t.height,i,this.toEncoderType(n),e),s=this.toTensorData(n,a),u=this.pendingRead.get(o);return this.pendingRead.delete(o),u?.forEach(l=>l(s)),s})}readUint8TextureAsFloat(t){return this.profiler.event("backend","TextureManager.readUint8TextureAsFloat",()=>{let n=t.shape.reduce((o,i)=>o*i),e=this.glContext.readTexture(t.texture,t.width,t.height,n*4,"byte",4);return new Float32Array(e.buffer,e.byteOffset,n)})}releaseTexture(t,n){let e;if(this.config.reuseTextures&&(e=this.textureLookup.get(t.texture),e)){n&&this.textureLookup.delete(e);let o=this.inUseTextures.get(e);if(o){let i=o.indexOf(t.texture);if(i!==-1){o.splice(i,1);let a=this.idleTextures.get(e);a||(a=[],this.idleTextures.set(e,a)),a.push(t.texture)}}}(!e||n)&&(B.verbose("TextureManager",`Deleting texture of size ${t.width}x${t.height}`),this.glContext.deleteTexture(t.texture))}toTensorData(t,n){switch(t){case"int16":return n instanceof Int16Array?n:Int16Array.from(n);case"int32":return n instanceof Int32Array?n:Int32Array.from(n);case"int8":return n instanceof Int8Array?n:Int8Array.from(n);case"uint16":return n instanceof Uint16Array?n:Uint16Array.from(n);case"uint32":return n instanceof Uint32Array?n:Uint32Array.from(n);case"uint8":case"bool":return n instanceof Uint8Array?n:Uint8Array.from(n);case"float32":return n instanceof Float32Array?n:Float32Array.from(n);case"float64":return n instanceof Float64Array?n:Float64Array.from(n);default:throw new Error(`TensorData type ${t} is not supported`)}}toTextureData(t,n){if(n)return n instanceof Float32Array?n:new Float32Array(n)}toEncoderType(t){return"float"}clearActiveTextures(){this.glContext.clearActiveTextures()}}});var xr,Jf=y(()=>{"use strict";pt();Ma();zu();Gf();Xf();Xi();Zf();xr=class{constructor(t,n){this.backend=t;this.context=n;this.layoutStrategy=new dr(t.glContext.maxTextureSize),this.programManager=new Tr(this.context.profiler,t.glContext,this.layoutStrategy),this.textureManager=new _r(t.glContext,this.layoutStrategy,this.context.profiler,{reuseTextures:t.textureCacheMode==="full"}),this.packedTextureDataCache=new Map,this.unpackedTextureDataCache=new Map,this.pack=t.pack,this.pack2unpackMap=new Map,this.unpack2packMap=new Map}createInferenceHandler(){return new er(this)}onGraphInitialized(t){let n=t.getValues().filter(e=>e.from===-1&&e.tensor).map(e=>e.tensor.dataId);this.initializers=new Set(n)}isInitializer(t){return this.initializers?this.initializers.has(t):!1}addInitializer(t){this.initializers.add(t)}getTextureData(t,n){return n?this.packedTextureDataCache.get(t):this.unpackedTextureDataCache.get(t)}setTextureData(t,n,e=!1){B.verbose("WebGLSessionHandler","Storing Texture data in cache"),e?this.packedTextureDataCache.set(t,n):this.unpackedTextureDataCache.set(t,n)}dispose(){this.programManager.dispose(),this.textureManager.clearActiveTextures(),this.packedTextureDataCache.forEach(t=>this.textureManager.releaseTexture(t,!0)),this.packedTextureDataCache=new Map,this.unpackedTextureDataCache.forEach(t=>this.textureManager.releaseTexture(t,!0)),this.unpackedTextureDataCache=new Map}resolve(t,n,e){let o=ka(t,n,Rf);return{impl:o.opImpl,context:o.opInit?o.opInit(t,e):t}}}});function uT(r){let t=0;for(;t<r.length&&r[t]();++t);return t-1}var gn,Yf=y(()=>{"use strict";me();dn();dn();ue();gn=class{constructor(t,n){this.frameBufferBound=!1;this.itemsToPoll=[];this.gl=t,this.version=n,this.getExtensions(),this.vertexbuffer=this.createVertexbuffer(),this.framebuffer=this.createFramebuffer(),this.queryVitalParameters()}allocateTexture(t,n,e,o){let i=this.gl,a=i.createTexture();i.bindTexture(i.TEXTURE_2D,a),i.texParameteri(i.TEXTURE_2D,i.TEXTURE_MIN_FILTER,i.NEAREST),i.texParameteri(i.TEXTURE_2D,i.TEXTURE_MAG_FILTER,i.NEAREST),i.texParameteri(i.TEXTURE_2D,i.TEXTURE_WRAP_S,i.CLAMP_TO_EDGE),i.texParameteri(i.TEXTURE_2D,i.TEXTURE_WRAP_T,i.CLAMP_TO_EDGE);let s=o?e.encode(o,t*n):null;return i.texImage2D(i.TEXTURE_2D,0,e.internalFormat,t,n,0,e.format,e.textureType,s),this.checkError(),a}updateTexture(t,n,e,o,i){let a=this.gl;a.bindTexture(a.TEXTURE_2D,t);let s=o.encode(i,n*e);a.texSubImage2D(a.TEXTURE_2D,0,0,0,n,e,o.format,o.textureType,s),this.checkError()}attachFramebuffer(t,n,e){let o=this.gl;o.bindTexture(o.TEXTURE_2D,t),o.bindFramebuffer(o.FRAMEBUFFER,this.framebuffer),o.framebufferTexture2D(o.FRAMEBUFFER,o.COLOR_ATTACHMENT0,o.TEXTURE_2D,t,0),this.checkError(),o.viewport(0,0,n,e),o.scissor(0,0,n,e)}readTexture(t,n,e,o,i,a){let s=this.gl;a||(a=1),this.frameBufferBound||this.attachFramebuffer(t,n,e);let u=this.getEncoder(i,a),l=u.allocate(n*e);return s.bindTexture(s.TEXTURE_2D,t),s.framebufferTexture2D(s.FRAMEBUFFER,s.COLOR_ATTACHMENT0,s.TEXTURE_2D,t,0),s.readPixels(0,0,n,e,s.RGBA,u.textureType,l),this.checkError(),u.decode(l,o)}isFramebufferReady(){return!0}getActiveTexture(){let t=this.gl;return`TEXTURE${t.getParameter(this.gl.ACTIVE_TEXTURE)-t.TEXTURE0}`}getTextureBinding(){return this.gl.getParameter(this.gl.TEXTURE_BINDING_2D)}getFramebufferBinding(){return this.gl.getParameter(this.gl.FRAMEBUFFER_BINDING)}setVertexAttributes(t,n){let e=this.gl;e.vertexAttribPointer(t,3,e.FLOAT,!1,20,0),e.enableVertexAttribArray(t),n!==-1&&(e.vertexAttribPointer(n,2,e.FLOAT,!1,20,12),e.enableVertexAttribArray(n)),this.checkError()}createProgram(t,n){let e=this.gl,o=e.createProgram();return e.attachShader(o,t),e.attachShader(o,n),e.linkProgram(o),o}compileShader(t,n){let e=this.gl,o=e.createShader(n);if(!o)throw new Error(`createShader() returned null with type ${n}`);if(e.shaderSource(o,t),e.compileShader(o),e.getShaderParameter(o,e.COMPILE_STATUS)===!1)throw new Error(`Failed to compile shader: ${e.getShaderInfoLog(o)}
Shader source:
${t}`);return o}deleteShader(t){this.gl.deleteShader(t)}bindTextureToUniform(t,n,e){let o=this.gl;o.activeTexture(o.TEXTURE0+n),this.checkError(),o.bindTexture(o.TEXTURE_2D,t),this.checkError(),o.uniform1i(e,n),this.checkError()}draw(){this.gl.drawArrays(this.gl.TRIANGLE_STRIP,0,4),this.checkError()}checkError(){if(K.debug){let t=this.gl,n=t.getError(),e="";switch(n){case t.NO_ERROR:return;case t.INVALID_ENUM:e="INVALID_ENUM";break;case t.INVALID_VALUE:e="INVALID_VALUE";break;case t.INVALID_OPERATION:e="INVALID_OPERATION";break;case t.INVALID_FRAMEBUFFER_OPERATION:e="INVALID_FRAMEBUFFER_OPERATION";break;case t.OUT_OF_MEMORY:e="OUT_OF_MEMORY";break;case t.CONTEXT_LOST_WEBGL:e="CONTEXT_LOST_WEBGL";break;default:e=`Unknown WebGL Error: ${n.toString(16)}`}throw new Error(e)}}deleteTexture(t){this.gl.deleteTexture(t)}deleteProgram(t){this.gl.deleteProgram(t)}getEncoder(t,n,e=0){if(this.version===2)return new Qn(this.gl,n);switch(t){case"float":return e===1||this.isRenderFloat32Supported?new fn(this.gl,n):new fn(this.gl,n,this.textureHalfFloatExtension.HALF_FLOAT_OES);case"int":throw new Error("not implemented");case"byte":return new tr(this.gl,n);default:throw new Error(`Invalid dataType: ${t}`)}}clearActiveTextures(){let t=this.gl;for(let n=0;n<this.maxTextureImageUnits;++n)t.activeTexture(t.TEXTURE0+n),t.bindTexture(t.TEXTURE_2D,null)}dispose(){if(this.disposed)return;let t=this.gl;t.bindFramebuffer(t.FRAMEBUFFER,null),t.deleteFramebuffer(this.framebuffer),t.bindBuffer(t.ARRAY_BUFFER,null),t.deleteBuffer(this.vertexbuffer),t.bindBuffer(t.ELEMENT_ARRAY_BUFFER,null),t.finish(),this.disposed=!0}createDefaultGeometry(){return new Float32Array([-1,1,0,0,1,-1,-1,0,0,0,1,1,0,1,1,1,-1,0,1,0])}createVertexbuffer(){let t=this.gl,n=t.createBuffer();if(!n)throw new Error("createBuffer() returned null");let e=this.createDefaultGeometry();return t.bindBuffer(t.ARRAY_BUFFER,n),t.bufferData(t.ARRAY_BUFFER,e,t.STATIC_DRAW),this.checkError(),n}createFramebuffer(){let t=this.gl.createFramebuffer();if(!t)throw new Error("createFramebuffer returned null");return t}queryVitalParameters(){let t=this.gl;if(this.isFloatTextureAttachableToFrameBuffer=this.checkFloatTextureAttachableToFrameBuffer(),this.isRenderFloat32Supported=this.checkRenderFloat32(),this.isFloat32DownloadSupported=this.checkFloat32Download(),this.version===1&&!this.textureHalfFloatExtension&&!this.isRenderFloat32Supported)throw new Error("both float32 and float16 TextureType are not supported");this.isBlendSupported=!this.isRenderFloat32Supported||this.checkFloat32Blend(),this.maxTextureSize=t.getParameter(t.MAX_TEXTURE_SIZE),this.maxTextureImageUnits=t.getParameter(t.MAX_TEXTURE_IMAGE_UNITS),this.version}getExtensions(){this.version===2?(this.colorBufferFloatExtension=this.gl.getExtension("EXT_color_buffer_float"),this.disjointTimerQueryWebgl2Extension=this.gl.getExtension("EXT_disjoint_timer_query_webgl2")):(this.textureFloatExtension=this.gl.getExtension("OES_texture_float"),this.textureHalfFloatExtension=this.gl.getExtension("OES_texture_half_float"))}checkFloatTextureAttachableToFrameBuffer(){let t=this.gl,n=t.createTexture();t.bindTexture(t.TEXTURE_2D,n);let e=this.version===2?t.RGBA32F:t.RGBA;t.texImage2D(t.TEXTURE_2D,0,e,1,1,0,t.RGBA,t.FLOAT,null);let o=t.createFramebuffer();t.bindFramebuffer(t.FRAMEBUFFER,o),t.framebufferTexture2D(t.FRAMEBUFFER,t.COLOR_ATTACHMENT0,t.TEXTURE_2D,n,0);let i=t.checkFramebufferStatus(t.FRAMEBUFFER)===t.FRAMEBUFFER_COMPLETE;return t.bindTexture(t.TEXTURE_2D,null),t.bindFramebuffer(t.FRAMEBUFFER,null),t.deleteTexture(n),t.deleteFramebuffer(o),i}checkRenderFloat32(){if(this.version===2){if(!this.colorBufferFloatExtension)return!1}else if(!this.textureFloatExtension)return!1;return this.isFloatTextureAttachableToFrameBuffer}checkFloat32Download(){if(this.version===2){if(!this.colorBufferFloatExtension)return!1}else if(!this.textureFloatExtension||!this.gl.getExtension("WEBGL_color_buffer_float"))return!1;return this.isFloatTextureAttachableToFrameBuffer}checkFloat32Blend(){let t=this.gl,n,e,o,i,a;try{n=t.createTexture(),e=t.createFramebuffer(),t.bindTexture(t.TEXTURE_2D,n);let s=this.version===2?t.RGBA32F:t.RGBA;return t.texImage2D(t.TEXTURE_2D,0,s,1,1,0,t.RGBA,t.FLOAT,null),t.bindFramebuffer(t.FRAMEBUFFER,e),t.framebufferTexture2D(t.FRAMEBUFFER,t.COLOR_ATTACHMENT0,t.TEXTURE_2D,n,0),t.enable(t.BLEND),o=t.createShader(t.VERTEX_SHADER),!o||(t.shaderSource(o,"void main(){}"),t.compileShader(o),i=t.createShader(t.FRAGMENT_SHADER),!i)||(t.shaderSource(i,"precision highp float;void main(){gl_FragColor=vec4(0.5);}"),t.compileShader(i),a=t.createProgram(),!a)?!1:(t.attachShader(a,o),t.attachShader(a,i),t.linkProgram(a),t.useProgram(a),t.drawArrays(t.POINTS,0,1),t.getError()===t.NO_ERROR)}finally{t.disable(t.BLEND),a&&t.deleteProgram(a),o&&t.deleteShader(o),i&&t.deleteShader(i),e&&(t.bindFramebuffer(t.FRAMEBUFFER,null),t.deleteFramebuffer(e)),n&&(t.bindTexture(t.TEXTURE_2D,null),t.deleteTexture(n))}}beginTimer(){if(this.version===2&&this.disjointTimerQueryWebgl2Extension){let t=this.gl,n=this.disjointTimerQueryWebgl2Extension,e=t.createQuery();return t.beginQuery(n.TIME_ELAPSED_EXT,e),e}else throw new Error("WebGL1 profiling currently not supported.")}endTimer(){if(this.version===2&&this.disjointTimerQueryWebgl2Extension){let t=this.gl,n=this.disjointTimerQueryWebgl2Extension;t.endQuery(n.TIME_ELAPSED_EXT);return}else throw new Error("WebGL1 profiling currently not supported")}isTimerResultAvailable(t){let n=!1,e=!1;if(this.version===2&&this.disjointTimerQueryWebgl2Extension){let o=this.gl,i=this.disjointTimerQueryWebgl2Extension;n=o.getQueryParameter(t,o.QUERY_RESULT_AVAILABLE),e=o.getParameter(i.GPU_DISJOINT_EXT)}else throw new Error("WebGL1 profiling currently not supported");return n&&!e}getTimerResult(t){let n=0;if(this.version===2){let e=this.gl;n=e.getQueryParameter(t,e.QUERY_RESULT),e.deleteQuery(t)}else throw new Error("WebGL1 profiling currently not supported");return n/1e6}async waitForQueryAndGetTime(t){return await xi(()=>this.isTimerResultAvailable(t)),this.getTimerResult(t)}async createAndWaitForFence(){let t=this.createFence(this.gl);return this.pollFence(t)}createFence(t){let n,e=t,o=e.fenceSync(e.SYNC_GPU_COMMANDS_COMPLETE,0);return t.flush(),o===null?n=()=>!0:n=()=>{let i=e.clientWaitSync(o,0,0);return i===e.ALREADY_SIGNALED||i===e.CONDITION_SATISFIED},{query:o,isFencePassed:n}}async pollFence(t){return new Promise(n=>{this.addItemToPoll(()=>t.isFencePassed(),()=>n())})}pollItems(){let t=uT(this.itemsToPoll.map(n=>n.isDoneFn));for(let n=0;n<=t;++n){let{resolveFn:e}=this.itemsToPoll[n];e()}this.itemsToPoll=this.itemsToPoll.slice(t+1)}async addItemToPoll(t,n){this.itemsToPoll.push({isDoneFn:t,resolveFn:n}),!(this.itemsToPoll.length>1)&&await xi(()=>(this.pollItems(),this.itemsToPoll.length===0))}}});function Ji(r){let t;if((!r||r==="webgl2")&&"webgl2"in Je?t=Je.webgl2:(!r||r==="webgl")&&"webgl"in Je&&(t=Je.webgl),!t)try{let e=cT();t=Qf(e,r)}catch{let o=lT();t=Qf(o,r)}r=r||t.version===1?"webgl":"webgl2";let n=t.gl;return Je[r]=t,n.isContextLost()?(delete Je[r],Ji(r)):(n.disable(n.DEPTH_TEST),n.disable(n.STENCIL_TEST),n.disable(n.BLEND),n.disable(n.DITHER),n.disable(n.POLYGON_OFFSET_FILL),n.disable(n.SAMPLE_COVERAGE),n.enable(n.SCISSOR_TEST),n.enable(n.CULL_FACE),n.cullFace(n.BACK),t)}function Qf(r,t){let n={alpha:!1,depth:!1,antialias:!1,stencil:!1,preserveDrawingBuffer:!1,premultipliedAlpha:!1,failIfMajorPerformanceCaveat:!1},e,o=n;if((!t||t==="webgl2")&&(e=r.getContext("webgl2",o),e))try{return new gn(e,2)}catch(i){B.warning("GlContextFactory",`failed to create WebGLContext using contextId 'webgl2'. Error: ${i}`)}if((!t||t==="webgl")&&(e=r.getContext("webgl",o)||r.getContext("experimental-webgl",o),e))try{return new gn(e,1)}catch(i){B.warning("GlContextFactory",`failed to create WebGLContext using contextId 'webgl' or 'experimental-webgl'. Error: ${i}`)}throw new Error("WebGL is not supported")}function lT(){if(typeof document>"u")throw new TypeError("failed to create canvas: document is not supported");let r=document.createElement("canvas");return r.width=1,r.height=1,r}function cT(){if(typeof OffscreenCanvas>"u")throw new TypeError("failed to create offscreen canvas: OffscreenCanvas is not supported");return new OffscreenCanvas(1,1)}var Je,td=y(()=>{"use strict";pt();Yf();Je={}});var vr,ed=y(()=>{"use strict";me();pt();Jf();td();vr=class{get contextId(){return K.webgl.contextId}set contextId(t){K.webgl.contextId=t}get matmulMaxBatchSize(){return K.webgl.matmulMaxBatchSize}set matmulMaxBatchSize(t){K.webgl.matmulMaxBatchSize=t}get textureCacheMode(){return K.webgl.textureCacheMode}set textureCacheMode(t){K.webgl.textureCacheMode=t}get pack(){return K.webgl.pack}set pack(t){K.webgl.pack=t}get async(){return K.webgl.async}set async(t){K.webgl.async=t}initialize(){try{return this.glContext=Ji(this.contextId),typeof this.matmulMaxBatchSize!="number"&&(this.matmulMaxBatchSize=16),typeof this.textureCacheMode!="string"&&(this.textureCacheMode="full"),typeof this.pack!="boolean"&&(this.pack=!1),typeof this.async!="boolean"&&(this.async=!1),B.setWithEnv(K),K.webgl.context||Object.defineProperty(K.webgl,"context",{value:this.glContext.gl}),B.verbose("WebGLBackend",`Created WebGLContext: ${typeof this.glContext} with matmulMaxBatchSize: ${this.matmulMaxBatchSize}; textureCacheMode: ${this.textureCacheMode}; pack: ${this.pack}; async: ${this.async}.`),!0}catch(t){return B.warning("WebGLBackend",`Unable to initialize WebGLBackend. ${t}`),!1}}createSessionHandler(t){return new xr(this,t)}dispose(){this.glContext.dispose()}}});async function Yi(r){if(r){let t=typeof r=="string"?[r]:r;for(let n of t){let e=nd.get(n);if(e)return e;let o=await dT(n);if(o)return o}}else return Yi(["webgl"]);throw new Error("no available backend to use")}async function dT(r){let t=fT;if(typeof t[r]<"u"&&pT(t[r])){let n=t[r],e=n.initialize();if(typeof e=="object"&&"then"in e&&(e=await e),e)return nd.set(r,n),n}}function pT(r){let t=r;return"initialize"in t&&typeof t.initialize=="function"&&"createSessionHandler"in t&&typeof t.createSessionHandler=="function"&&"dispose"in t&&typeof t.dispose=="function"}var nd,fT,rd=y(()=>{"use strict";ed();nd=new Map,fT={webgl:new vr}});var Qi,wr,od=y(()=>{"use strict";pt();Qi=class{constructor(t,n){this.op=t;this.node=n}},wr=class{constructor(t,n,e){this.graph=t;this.profiler=e;this.initialize(n)}initialize(t){this.profiler.event("session","ExecutionPlan.initialize",()=>{let n=this.graph.getNodes();if(n.length!==t.length)throw new Error("The size of nodes and OPs do not match.");this._ops=t.map((e,o)=>new Qi(e,n[o])),this.reset(),this._starter=[],this._ops.forEach((e,o)=>{let i=!0;for(let a of e.node.inputs)if(!this._values[a]&&this.graph.getInputIndices().indexOf(a)===-1){i=!1;break}i&&this._starter.push(o)})})}reset(){this._values=this.graph.getValues().map(t=>t.tensor)}async execute(t,n){return this.profiler.event("session","ExecutionPlan.execute",async()=>{this.reset();let e=t.createInferenceHandler(),o=this.graph.getInputIndices();if(n.length!==o.length)throw new Error(`number of input tensors don't match the number of inputs to the model: actual: ${n.length} expected: ${o.length}`);n.forEach((c,d)=>{let p=o[d];this._values[p]=c});let i=this._starter.slice(0),a=this.graph.getValues(),s=this.graph.getNodes(),u=0;for(;u<i.length;){let c=i[u++],d=this._ops[c],p=d.node.inputs.map(x=>this._values[x]);if(p.indexOf(void 0)!==-1)throw new Error(`unresolved input detected: op: ${d.node}`);let m=p;B.verbose("ExecPlan",`Running op:${d.node.name} (${m.map((x,w)=>`'${d.node.inputs[w]}': ${x.type}[${x.dims.join(",")}]`).join(", ")})`);let b=await this.profiler.event("node",d.node.name,async()=>d.op.impl(e,m,d.op.context));if(b.length!==d.node.outputs.length)throw new Error("the size of output does not match model definition.");b.forEach((x,w)=>{let I=d.node.outputs[w];if(this._values[I])throw new Error(`output [${I}] already has value: op:${d.node.name}`);this._values[I]=x});let g=new Set;b.forEach((x,w)=>{let I=d.node.outputs[w];for(let D of a[I].to){let z=s[D],F=!0;for(let at of z.inputs)if(!this._values[at]){F=!1;break}F&&g.add(D)}}),i.push(...g)}let l=[];for(let c=0;c<this.graph.getOutputIndices().length;c++){let d=this.graph.getOutputIndices()[c],p=this._values[d];if(p===void 0)throw new Error(`required output [${d}] does not have value`);d===0?await p.getData():p.data,l.push(p)}return B.verbose("ExecPlan","disposing of inferenceHandler"),e.dispose(),l})}}});var N,yn,id=y(()=>{"use strict";on();N=E(Ue());Ce();V();yn=class r{constructor(t){if(this._attributes=new Map,t!=null){for(let n of t)n instanceof N.onnx.AttributeProto?this._attributes.set(n.name,[r.getValue(n),r.getType(n)]):n instanceof Wn.Attribute&&this._attributes.set(n.name(),[r.getValue(n),r.getType(n)]);if(this._attributes.size<t.length)throw new Error("duplicated attribute names")}}set(t,n,e){this._attributes.set(t,[e,n])}delete(t){this._attributes.delete(t)}getFloat(t,n){return this.get(t,"float",n)}getInt(t,n){return this.get(t,"int",n)}getString(t,n){return this.get(t,"string",n)}getTensor(t,n){return this.get(t,"tensor",n)}getFloats(t,n){return this.get(t,"floats",n)}getInts(t,n){return this.get(t,"ints",n)}getStrings(t,n){return this.get(t,"strings",n)}getTensors(t,n){return this.get(t,"tensors",n)}get(t,n,e){let o=this._attributes.get(t);if(o===void 0){if(e!==void 0)return e;throw new Error(`required attribute not found: ${t}`)}if(o[1]!==n)throw new Error(`type mismatch: expected ${n} but got ${o[1]}`);return o[0]}static getType(t){let n=t instanceof N.onnx.AttributeProto?t.type:t.type();switch(n){case N.onnx.AttributeProto.AttributeType.FLOAT:return"float";case N.onnx.AttributeProto.AttributeType.INT:return"int";case N.onnx.AttributeProto.AttributeType.STRING:return"string";case N.onnx.AttributeProto.AttributeType.TENSOR:return"tensor";case N.onnx.AttributeProto.AttributeType.FLOATS:return"floats";case N.onnx.AttributeProto.AttributeType.INTS:return"ints";case N.onnx.AttributeProto.AttributeType.STRINGS:return"strings";case N.onnx.AttributeProto.AttributeType.TENSORS:return"tensors";default:throw new Error(`attribute type is not supported yet: ${N.onnx.AttributeProto.AttributeType[n]}`)}}static getValue(t){let n=t instanceof N.onnx.AttributeProto?t.type:t.type();if(n===N.onnx.AttributeProto.AttributeType.GRAPH||n===N.onnx.AttributeProto.AttributeType.GRAPHS)throw new Error("graph attribute is not supported yet");let e=this.getValueNoCheck(t);if(n===N.onnx.AttributeProto.AttributeType.INT&&ct.isLong(e))return ct.longToNumber(e);if(n===N.onnx.AttributeProto.AttributeType.INTS){let o=e,i=new Array(o.length);for(let a=0;a<o.length;a++){let s=o[a];i[a]=ct.longToNumber(s)}return i}if(n===N.onnx.AttributeProto.AttributeType.TENSOR)return t instanceof N.onnx.AttributeProto?Y.fromProto(e):Y.fromOrtTensor(e);if(n===N.onnx.AttributeProto.AttributeType.TENSORS){if(t instanceof N.onnx.AttributeProto)return e.map(i=>Y.fromProto(i));if(t instanceof Wn.Attribute)return e.map(i=>Y.fromOrtTensor(i))}return n===N.onnx.AttributeProto.AttributeType.STRING&&t instanceof N.onnx.AttributeProto?cn(e):n===N.onnx.AttributeProto.AttributeType.STRINGS&&t instanceof N.onnx.AttributeProto?e.map(cn):e}static getValueNoCheck(t){return t instanceof N.onnx.AttributeProto?this.getValueNoCheckFromOnnxFormat(t):this.getValueNoCheckFromOrtFormat(t)}static getValueNoCheckFromOnnxFormat(t){switch(t.type){case N.onnx.AttributeProto.AttributeType.FLOAT:return t.f;case N.onnx.AttributeProto.AttributeType.INT:return t.i;case N.onnx.AttributeProto.AttributeType.STRING:return t.s;case N.onnx.AttributeProto.AttributeType.TENSOR:return t.t;case N.onnx.AttributeProto.AttributeType.GRAPH:return t.g;case N.onnx.AttributeProto.AttributeType.FLOATS:return t.floats;case N.onnx.AttributeProto.AttributeType.INTS:return t.ints;case N.onnx.AttributeProto.AttributeType.STRINGS:return t.strings;case N.onnx.AttributeProto.AttributeType.TENSORS:return t.tensors;case N.onnx.AttributeProto.AttributeType.GRAPHS:return t.graphs;default:throw new Error(`unsupported attribute type: ${N.onnx.AttributeProto.AttributeType[t.type]}`)}}static getValueNoCheckFromOrtFormat(t){switch(t.type()){case gt.AttributeType.FLOAT:return t.f();case gt.AttributeType.INT:return t.i();case gt.AttributeType.STRING:return t.s();case gt.AttributeType.TENSOR:return t.t();case gt.AttributeType.GRAPH:return t.g();case gt.AttributeType.FLOATS:return t.floatsArray();case gt.AttributeType.INTS:{let n=[];for(let e=0;e<t.intsLength();e++)n.push(t.ints(e));return n}case gt.AttributeType.STRINGS:{let n=[];for(let e=0;e<t.stringsLength();e++)n.push(t.strings(e));return n}case gt.AttributeType.TENSORS:{let n=[];for(let e=0;e<t.tensorsLength();e++)n.push(t.tensors(e));return n}default:throw new Error(`unsupported attribute type: ${gt.AttributeType[t.type()]}`)}}}});var ea,na,fe,Ir,ta,ad=y(()=>{"use strict";id();on();ea=E(Ue());Ce();V();na={from:(r,t)=>new ta(r,t)},fe=class{constructor(t){this._from=void 0,this._to=[],this.tensor=void 0,this.type=void 0,t&&(this.type=nt.tensorValueTypeFromProto(t.type.tensorType))}get from(){return this._from}get to(){return this._to}},Ir=class{constructor(t,n){t instanceof ea.onnx.NodeProto?(this.name=t.name,this.opType=t.opType,this.attributes=new yn(t.attribute)):t instanceof ri.Node&&(this.name=n??t.name(),this.opType=t.opType(),this.attributes=new yn(nt.tensorAttributesFromORTFormat(t))),this.inputs=[],this.outputs=[],this.executeNode=!0}},ta=class{constructor(t,n){if(!t)throw new TypeError("graph is empty");this.buildGraph(t),this.transformGraph(n),this.checkIsAcyclic()}getInputIndices(){return this._allInputIndices}getInputNames(){return this._allInputNames}getOutputIndices(){return this._allOutputIndices}getOutputNames(){return this._allOutputNames}getValues(){return this._allData}getNodes(){return this._nodes}buildGraph(t){if(t instanceof ea.onnx.GraphProto)this.buildGraphFromOnnxFormat(t);else if(t instanceof ei.Graph)this.buildGraphFromOrtFormat(t);else throw new TypeError("Graph type is not supported.")}buildGraphFromOnnxFormat(t){let n=new Map;this._allData=[],this._allInputIndices=[],this._allInputNames=[],this._allOutputIndices=[],this._allOutputNames=[],this._nodes=[];let e=new Map;if(!t.input)throw new Error("missing information in graph: input");let o=[];for(let i of t.input){if(n.has(i.name))throw new Error(`duplicated input name: ${i.name}`);let a=this._allData.push(new fe(i))-1;n.set(i.name,a),o.push(i.name)}if(!t.initializer)throw new Error("missing information in graph: initializer");for(let i of t.initializer){let a=n.get(i.name);if(a===void 0){let s=new fe;s.type={shape:{dims:nt.tensorDimsFromProto(i.dims)},tensorType:nt.tensorDataTypeFromProto(i.dataType)},a=this._allData.push(s)-1,n.set(i.name,a)}this._allData[a]._from=-1,this._allData[a].tensor=Y.fromProto(i)}for(let i=0;i<this._allData.length;i++)this._allData[i].tensor||(this._allInputIndices.push(i),this._allInputNames.push(o[i]));if(!t.output)throw new Error("missing information in graph: output");for(let i of t.output){if(n.has(i.name))throw new Error(`duplicated output name: ${i.name}`);let a=this._allData.push(new fe(i))-1;n.set(i.name,a),this._allOutputIndices.push(a),this._allOutputNames.push(i.name)}if(!t.node)throw new Error("missing information in graph: node");for(let i of t.node){if(!i.name)for(let s=0;;s++){let u=`unnamed_${i.opType}_${s}`;if(!e.has(u)){i.name=u;break}}if(e.has(i.name))throw new Error(`duplicated node name: ${i.name}`);let a=this._nodes.push(new Ir(i))-1;e.set(i.name,a)}for(let i=0;i<this._nodes.length;i++){let a=this._nodes[i],s=t.node[i];if(!s.output)throw new Error(`missing output for node: ${s.name}`);for(let u of s.output){let l=n.get(u);if(typeof l>"u"&&(l=this._allData.push(new fe)-1,n.set(u,l)),a.outputs.push(l),this._allData[l]._from!==void 0)throw new Error(`multiple nodes output to one data value: ${l}`);if(this._allData[l]._from=i,s.opType==="Constant"){if(!s.attribute||s.attribute.length!==1||!s.attribute[0].t)throw new Error("missing attributes or missing tensor value in attributes for this Constant operator");if(!s.output||s.output.length!==1)throw new Error("missing output or incorrect number of outputs for this Constant operator");a.outputs.pop(),a.executeNode=!1,this._allData[l]._from=-1,this._allData[l].tensor=Y.fromProto(s.attribute[0].t)}}}for(let i=0;i<this._nodes.length;i++){let a=this._nodes[i],s=t.node[i];if(!s.input)throw new Error(`missing input for node: ${s.name}`);for(let u of s.input){let l=n.get(u);if(typeof l>"u"){if(u===""&&(s.input.length===3||s.input.length===4)&&s.opType==="Resize")continue;throw new Error(`unrecognized input '${u}' for node: ${s.name}`)}a.inputs.push(l),this._allData[l]._to.push(i)}}return!0}buildGraphFromOrtFormat(t){let n=new Map;this._allData=[],this._allInputIndices=[],this._allInputNames=[],this._allOutputIndices=[],this._allOutputNames=[],this._nodes=[];let e=new Map,o=[];for(let i=0;i<t.inputsLength();i++){let a=t.inputs(i);if(n.has(a))throw new Error(`duplicated input name: ${a}`);for(let s=0;s<t.nodeArgsLength();s++)if(t.nodeArgs(s)?.name()===a){let u=new fe;if(t.nodeArgs(s)?.type()?.valueType()!==ii.TypeInfoValue.tensor_type)throw new Error("Unexpected value type for the nodeArg.");let c=t.nodeArgs(s).type().value(new oi.TensorTypeAndShape),d=nt.tensorDataTypeFromProto(c.elemType()),p=c.shape(),m=[];for(let g=0;g<p.dimLength();g++)m.push(ct.longToNumber(p.dim(g).value().dimValue()));u.type={shape:{dims:m},tensorType:d};let b=this._allData.push(u)-1;n.set(a,b),o.push(a)}}for(let i=0;i<t.initializersLength();i++){let a=t.initializers(i),s=n.get(a.name());if(s===void 0){let u=new fe,l=nt.tensorDimsFromORTFormat(a),c=nt.tensorDataTypeFromProto(a.dataType());u.type={shape:{dims:l},tensorType:c},s=this._allData.push(u)-1,n.set(a.name(),s)}this._allData[s]._from=-1,this._allData[s].tensor=Y.fromOrtTensor(a)}for(let i=0;i<this._allData.length;i++)this._allData[i].tensor||(this._allInputIndices.push(i),this._allInputNames.push(o[i]));for(let i=0;i<t.outputsLength();i++){let a=t.outputs(i);if(n.has(a))throw new Error(`duplicated output name: ${a}`);let s=this._allData.push(new fe)-1;n.set(a,s),this._allOutputIndices.push(s),this._allOutputNames.push(a)}if(!t.nodes)throw new Error("missing information in graph: node");for(let i=0;i<t.nodesLength();i++){let a=t.nodes(i),s=a.name();if(!s)for(let l=0;s=`unnamed_${a.opType()}_${l}`,!!e.has(s);l++);if(e.has(s))throw new Error(`duplicated node name: ${s}`);let u=this._nodes.push(new Ir(a,s))-1;e.set(s,u)}for(let i=0;i<this._nodes.length;i++){let a=this._nodes[i],s=t.nodes(i);if(s==null)throw new Error(`No node exists at index ${i}`);if(s?.outputsLength()===0)throw new Error(`missing output for node: ${s.name}`);for(let u=0;u<s?.outputsLength();u++){let l=s?.outputs(u),c=n.get(l);if(typeof c>"u"&&(c=this._allData.push(new fe)-1,n.set(l,c)),a.outputs.push(c),this._allData[c]._from!==void 0)throw new Error(`multiple nodes output to one data value: ${c}`);if(this._allData[c]._from=i,s.opType()==="Constant"){if(s.attributesLength()!==1||!s.attributes(0).t())throw new Error("missing attributes or missing tensor value in attributes for this Constant operator");if(s.outputsLength()!==1)throw new Error("missing output or incorrect number of outputs for this Constant operator");a.outputs.pop(),a.executeNode=!1,this._allData[c]._from=-1,this._allData[c].tensor=Y.fromOrtTensor(s.attributes(0).t())}}}for(let i=0;i<this._nodes.length;i++){let a=this._nodes[i],s=t.nodes(i);if(s.inputsLength()===0)throw new Error(`missing input for node: ${s.name}`);for(let u=0;u<s.inputsLength();u++){let l=s.inputs(u),c=n.get(l);if(typeof c>"u")throw new Error(`unrecognized input '${l}' for node: ${s.name()}`);a.inputs.push(c),this._allData[c]._to.push(i)}}}checkIsAcyclic(){let t=new Set;this._allInputIndices.forEach(o=>{this._allData[o]._to.forEach(a=>{t.add(a)})});let n=Array.from(t),e=new Array(this._nodes.length).fill("white");for(;n.length>0;){let o=n.pop();e[o]==="gray"?e[o]="black":(n.push(o),e[o]="gray",this._nodes[o].outputs.forEach(i=>{let a=this._allData[i];if(typeof a.tensor<"u")throw new Error("node outputs should not be initialized");if(a._from!==o)throw new Error("from property of the Value object doesn't match index of Node being processed");a._to.forEach(s=>{if(e[s]==="gray")throw new Error("model graph is cyclic");e[s]==="white"&&n.push(s)})}))}}transformGraph(t){this.removeAllIdentityNodes(),this.removeAllDropoutNodes(),this.fuseConvActivationNodes(),t&&t.transformGraph(this),this.finalizeGraph()}finalizeGraph(){let t=0,n=new Array(this._nodes.length,0),e=0;for(let o=0;o<this._nodes.length;o++)n[o]=e,this._nodes[o].executeNode?(e!==o&&(this._nodes[e]=this._nodes[o]),e++):this._nodes[o].outputs.forEach(i=>{this._allData[i]._from=-2});this._nodes.splice(e,this._nodes.length-e);for(let o=0;o<this._allData.length;o++){let i=this._allData[o];i._from!==void 0&&i._from!==-1&&i._from!==-2&&(i._from=n[i._from]);for(let a=0;a<i._to.length;a++)if(i._to[a]>=0)i._to[a]=n[i._to[a]];else throw new Error("Trying to update a removed node")}t=0;for(let o=0;o<this._allData.length;o++){if(this._allData[o].from===-2&&this._allOutputIndices.indexOf(o+t)===-1){t++,this._allData.splice(o,1),o--;continue}if(t>0){let i=-1;this._allData[o].from!==void 0&&this._allData[o].from!==-1?(i=this._nodes[this._allData[o].from].outputs.indexOf(o+t),i!==-1&&(this._nodes[this._allData[o].from].outputs[i]=o)):(i=this._allInputIndices.indexOf(o+t),i!==-1&&(this._allInputIndices[i]=o)),this._allData[o].to.forEach(a=>{i=this._nodes[a].inputs.indexOf(o+t),i!==-1&&(this._nodes[a].inputs[i]=o)}),this._allData[o].to.length===0&&(i=this._allOutputIndices.indexOf(o+t),i!==-1&&(this._allOutputIndices[i]=o))}}}deleteNode(t){let n=this._nodes[t];if(n.outputs.length>1){for(let s=1;s<n.outputs.length;s++)if(this._allData[n.outputs[s]].to.length>0)throw new Error("Node deletion with more than one output connected to other nodes is not supported. ")}n.executeNode=!1;let e=n.inputs[0],o=n.outputs[0],i=this._allData[o].to;for(let s=0;s<n.inputs.length;s++){let u=this._allData[n.inputs[s]].to.indexOf(t);if(u===-1)throw new Error("The Value object doesn't have the current Node in it's 'to' property ");this._allData[n.inputs[s]].to.splice(u,1)}this._allData[o]._to=[];let a=this._allOutputIndices.indexOf(o);if(a!==-1&&(this._allOutputIndices[a]=e),i&&i.length>0)for(let s of i){let u=this._nodes[s].inputs.indexOf(o);if(u===-1)throw new Error("The Node object doesn't have the output Value in it's 'inputs' property ");this._nodes[s].inputs[u]=e,this._allData[e].to.push(s)}}removeAllDropoutNodes(){let t=0;for(let n of this._nodes){if(n.opType==="Dropout"){if(n.inputs.length!==1)throw new Error("Dropout nodes should only contain one input. ");if(n.outputs.length!==1&&n.outputs.length!==2)throw new Error("Dropout nodes should contain either 1 or 2 output(s)");if(n.outputs.length===2&&this._allData[n.outputs[1]]._to.length!==0)throw new Error("Dropout nodes's second output should not be referenced by other nodes");this.deleteNode(t)}t++}}removeAllIdentityNodes(){let t=0;for(let n of this._nodes)n.opType==="Identity"&&this.deleteNode(t),t++}isActivation(t){switch(t.opType){case"Relu":case"Sigmoid":case"Clip":return!0;default:return!1}}fuseConvActivationNodes(){for(let t of this._nodes)if(t.opType==="Conv"){let n=this._allData[t.outputs[0]]._to;if(n.length===1&&this.isActivation(this._nodes[n[0]])){let e=this._nodes[n[0]];if(e.opType==="Clip")if(e.inputs.length===1)try{t.attributes.set("activation_params","floats",[e.attributes.getFloat("min"),e.attributes.getFloat("max")])}catch{t.attributes.set("activation_params","floats",[Ne,Fe])}else if(e.inputs.length>=3&&this._allData[e.inputs[1]].tensor!==void 0&&this._allData[e.inputs[2]].tensor!==void 0)t.attributes.set("activation_params","floats",[this._allData[e.inputs[1]].tensor.floatData[0],this._allData[e.inputs[2]].tensor.floatData[0]]);else continue;t.attributes.set("activation","string",e.opType),this.deleteNode(n[0])}}}}});var sd,ud,Or,ld=y(()=>{"use strict";sd=E(M());ad();on();ud=E(Ue());V();Or=class{constructor(){}load(t,n,e){let o;if(!e)try{this.loadFromOnnxFormat(t,n);return}catch(i){if(e!==void 0)throw i;o=i}try{this.loadFromOrtFormat(t,n)}catch(i){throw e!==void 0?i:new Error(`Failed to load model as ONNX format: ${o}
as ORT format: ${i}`)}}loadFromOnnxFormat(t,n){let e=ud.onnx.ModelProto.decode(t);if(ct.longToNumber(e.irVersion)<3)throw new Error("only support ONNX model with IR_VERSION>=3");this._opsets=e.opsetImport.map(i=>({domain:i.domain,version:ct.longToNumber(i.version)})),this._graph=na.from(e.graph,n)}loadFromOrtFormat(t,n){let e=new sd.ByteBuffer(t),o=ni.InferenceSession.getRootAsInferenceSession(e).model();if(ct.longToNumber(o.irVersion())<3)throw new Error("only support ONNX model with IR_VERSION>=3");this._opsets=[];for(let a=0;a<o.opsetImportLength();a++){let s=o.opsetImport(a);this._opsets.push({domain:s?.domain(),version:ct.longToNumber(s.version())})}this._graph=na.from(o.graph(),n)}get graph(){return this._graph}get opsets(){return this._opsets}}});var Pr,cd=y(()=>{"use strict";rd();od();pt();ld();Pr=class{constructor(t={}){this._initialized=!1,this.backendHint=t.backendHint,this.profiler=Ln.create(t.profiler),this.context={profiler:this.profiler,graphInputTypes:[],graphInputDims:[]}}get inputNames(){return this._model.graph.getInputNames()}get outputNames(){return this._model.graph.getOutputNames()}startProfiling(){this.profiler.start()}endProfiling(){this.profiler.stop()}async loadModel(t,n,e){await this.profiler.event("session","Session.loadModel",async()=>{let o=await Yi(this.backendHint);if(this.sessionHandler=o.createSessionHandler(this.context),this._model=new Or,typeof t=="string"){let i=t.endsWith(".ort");{let s=await(await fetch(t)).arrayBuffer();this.initialize(new Uint8Array(s),i)}}else if(ArrayBuffer.isView(t))this.initialize(t);else{let i=new Uint8Array(t,n||0,e||t.byteLength);this.initialize(i)}})}initialize(t,n){if(this._initialized)throw new Error("already initialized");this.profiler.event("session","Session.initialize",()=>{let e=this.sessionHandler.transformGraph?this.sessionHandler:void 0;this._model.load(t,e,n),this.sessionHandler.onGraphInitialized&&this.sessionHandler.onGraphInitialized(this._model.graph),this.initializeOps(this._model.graph),this._executionPlan=new wr(this._model.graph,this._ops,this.profiler)}),this._initialized=!0}async run(t){if(!this._initialized)throw new Error("session not initialized yet");return this.profiler.event("session","Session.run",async()=>{let n=this.normalizeAndValidateInputs(t),e=await this._executionPlan.execute(this.sessionHandler,n);return this.createOutput(e)})}normalizeAndValidateInputs(t){let n=this._model.graph.getInputNames();if(Array.isArray(t)){if(t.length!==n.length)throw new Error(`incorrect input array length: expected ${n.length} but got ${t.length}`)}else{if(t.size!==n.length)throw new Error(`incorrect input map size: expected ${n.length} but got ${t.size}`);let e=new Array(t.size),o=0;for(let i=0;i<n.length;++i){let a=t.get(n[i]);if(!a)throw new Error(`missing input tensor for: '${name}'`);e[o++]=a}t=e}if(!this.context.graphInputTypes||this.context.graphInputTypes.length===0||!this.context.graphInputDims||this.context.graphInputDims.length===0){let e=this._model.graph.getInputIndices(),o=this._model.graph.getValues(),i=new Array(e.length);for(let a=0;a<e.length;++a){let s=o[e[a]];i[a]=s.type.shape.dims,this.context.graphInputTypes.push(s.type.tensorType),this.context.graphInputDims.push(t[a].dims)}this.validateInputTensorDims(i,t,!0)}else this.validateInputTensorDims(this.context.graphInputDims,t,!1);return this.validateInputTensorTypes(this.context.graphInputTypes,t),t}validateInputTensorTypes(t,n){for(let e=0;e<n.length;e++){let o=t[e],i=n[e].type;if(o!==i)throw new Error(`input tensor[${e}] check failed: expected type '${o}' but got ${i}`)}}validateInputTensorDims(t,n,e){for(let o=0;o<n.length;o++){let i=t[o],a=n[o].dims;if(!this.compareTensorDims(i,a,e))throw new Error(`input tensor[${o}] check failed: expected shape '[${i.join(",")}]' but got [${a.join(",")}]`)}}compareTensorDims(t,n,e){if(t.length!==n.length)return!1;for(let o=0;o<t.length;++o)if(t[o]!==n[o]&&(!e||t[o]!==0))return!1;return!0}createOutput(t){let n=this._model.graph.getOutputNames();if(t.length!==n.length)throw new Error("expected number of outputs do not match number of generated outputs");let e=new Map;for(let o=0;o<n.length;++o)e.set(n[o],t[o]);return e}initializeOps(t){let n=t.getNodes();this._ops=new Array(n.length);for(let e=0;e<n.length;e++)this._ops[e]=this.sessionHandler.resolve(n[e],this._model.opsets,t)}}});var Sr,fd=y(()=>{"use strict";me();Ce();Sr=class{constructor(t){this.session=t;this.inputNames=this.session.inputNames,this.outputNames=this.session.outputNames}async dispose(){}async run(t,n,e){let o=new Map;for(let s in t)if(Object.hasOwnProperty.call(t,s)){let u=t[s];o.set(s,new Y(u.dims,u.type,void 0,void 0,u.data))}let i=await this.session.run(o),a={};return i.forEach((s,u)=>{a[u]=new de(s.type,s.data,s.dims)}),a}startProfiling(){this.session.startProfiling()}endProfiling(){this.session.endProfiling()}}});var dd={};oa(dd,{onnxjsBackend:()=>hT});var ra,hT,pd=y(()=>{"use strict";cd();fd();ra=class{async init(){}async createInferenceSessionHandler(t,n){let e=new Pr(n);return typeof t=="string"?await e.loadModel(t):await e.loadModel(t),new Sr(e)}},hT=new ra});me();me();me();var Ca="1.21.0-dev.20250206-d981b153d3";var IS=Fr;{let r=(pd(),Td(dd)).onnxjsBackend;vn("webgl",r,-10)}Object.defineProperty(K.versions,"web",{value:Ca,enumerable:!0});export{xd as InferenceSession,Sa as TRACE,In as TRACE_FUNC_BEGIN,On as TRACE_FUNC_END,de as Tensor,IS as default,K as env,vn as registerBackend};
/*! Bundled license information:

long/index.js:
  (**
   * @license
   * Copyright 2009 The Closure Library Authors
   * Copyright 2020 Daniel Wirtz / The long.js Authors.
   *
   * Licensed under the Apache License, Version 2.0 (the "License");
   * you may not use this file except in compliance with the License.
   * You may obtain a copy of the License at
   *
   *     http://www.apache.org/licenses/LICENSE-2.0
   *
   * Unless required by applicable law or agreed to in writing, software
   * distributed under the License is distributed on an "AS IS" BASIS,
   * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   * See the License for the specific language governing permissions and
   * limitations under the License.
   *
   * SPDX-License-Identifier: Apache-2.0
   *)
*/
//# sourceMappingURL=ort.webgl.min.mjs.map
