import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>rk<PERSON>, Zap, <PERSON>, Settings } from 'lucide-react';
import ImageUploader from './components/ImageUploader';
import ImageProcessor from './components/ImageProcessor';
import SimpleBackgroundRemover from './components/SimpleBackgroundRemover';

function App() {
  const [selectedImage, setSelectedImage] = useState(null);
  const [processorMode, setProcessorMode] = useState('ai'); // 'ai' o 'simple'

  const handleImageSelect = (image) => {
    setSelectedImage(image);
  };

  const handleClearImage = () => {
    setSelectedImage(null);
  };

  const handleReset = () => {
    setSelectedImage(null);
  };

  return (
    <div className="min-h-screen py-8 px-4">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="text-center mb-12 animate-fadeInUp">
          <div className="flex justify-center mb-4">
            <div className="p-4 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-full">
              <Scissors className="w-12 h-12 text-white" />
            </div>
          </div>
          
          <h1 className="text-4xl md:text-5xl font-bold text-gray-800 mb-4">
            Eliminador de Fondos
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-500 to-indigo-600">
              {' '}con IA
            </span>
          </h1>
          
          <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
            Elimina el fondo de tus imágenes con <strong>MediaPipe AI</strong> y algoritmos
            avanzados de procesamiento. Resultados profesionales en segundos.
          </p>
          
          {/* Características */}
          <div className="grid md:grid-cols-3 gap-6 mb-8">
            <div className="flex items-center justify-center space-x-3 p-4 glass-effect rounded-lg">
              <Sparkles className="w-6 h-6 text-blue-500" />
              <span className="font-medium text-gray-700">MediaPipe AI</span>
            </div>
            <div className="flex items-center justify-center space-x-3 p-4 glass-effect rounded-lg">
              <Zap className="w-6 h-6 text-green-500" />
              <span className="font-medium text-gray-700">Algoritmos Avanzados</span>
            </div>
            <div className="flex items-center justify-center space-x-3 p-4 glass-effect rounded-lg">
              <Shield className="w-6 h-6 text-purple-500" />
              <span className="font-medium text-gray-700">Procesamiento Local</span>
            </div>
          </div>
        </div>

        {/* Selector de modo */}
        {selectedImage && (
          <div className="glass-effect rounded-xl p-4 mb-8 animate-fadeInUp">
            <div className="flex items-center justify-center space-x-4">
              <Settings className="w-5 h-5 text-gray-600" />
              <span className="font-medium text-gray-700">Modo de procesamiento:</span>
              <div className="flex space-x-2">
                <button
                  onClick={() => setProcessorMode('ai')}
                  className={`px-4 py-2 rounded-lg text-sm font-medium transition-all ${
                    processorMode === 'ai'
                      ? 'bg-blue-500 text-white shadow-md'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                >
                  IA Avanzada
                </button>
                <button
                  onClick={() => setProcessorMode('simple')}
                  className={`px-4 py-2 rounded-lg text-sm font-medium transition-all ${
                    processorMode === 'simple'
                      ? 'bg-blue-500 text-white shadow-md'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                >
                  Algoritmos Avanzados
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Contenido principal */}
        <div className="space-y-8">
          {/* Subir imagen */}
          <ImageUploader
            onImageSelect={handleImageSelect}
            selectedImage={selectedImage}
            onClear={handleClearImage}
          />

          {/* Procesar imagen */}
          {processorMode === 'ai' ? (
            <ImageProcessor
              selectedImage={selectedImage}
              onReset={handleReset}
            />
          ) : (
            <SimpleBackgroundRemover
              selectedImage={selectedImage}
              onReset={handleReset}
            />
          )}
        </div>

        {/* Footer */}
        <footer className="mt-16 text-center text-gray-500 text-sm">
          <div className="glass-effect rounded-lg p-6">
            <p className="mb-2">
              🚀 Potenciado por <strong>MediaPipe AI</strong> de Google y algoritmos avanzados de procesamiento
            </p>
            <p>
              Todas las imágenes se procesan localmente en tu navegador con tecnología de vanguardia.
              Privacidad y velocidad garantizadas.
            </p>
          </div>
        </footer>
      </div>
    </div>
  );
}

export default App;
