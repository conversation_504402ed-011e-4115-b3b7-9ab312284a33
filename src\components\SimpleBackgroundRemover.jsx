import React, { useState, useCallback, useRef } from 'react';
import { Wand2, Loader2, Download, RotateCcw, Palette } from 'lucide-react';
import { downloadImage, blobToImageUrl } from '../utils/imageUtils';

const SimpleBackgroundRemover = ({ selectedImage, onReset }) => {
  const [isProcessing, setIsProcessing] = useState(false);
  const [processedImage, setProcessedImage] = useState(null);
  const [error, setError] = useState('');
  const [backgroundColor, setBackgroundColor] = useState('#ffffff');
  const canvasRef = useRef(null);

  // Función simple para crear una imagen con fondo de color sólido
  const processImageSimple = useCallback(async () => {
    if (!selectedImage) return;

    setIsProcessing(true);
    setError('');

    try {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();

      await new Promise((resolve, reject) => {
        img.onload = resolve;
        img.onerror = reject;
        img.src = URL.createObjectURL(selectedImage);
      });

      // Configurar canvas
      canvas.width = img.width;
      canvas.height = img.height;

      // Dibujar fondo de color
      ctx.fillStyle = backgroundColor;
      ctx.fillRect(0, 0, canvas.width, canvas.height);

      // Dibujar imagen original
      ctx.drawImage(img, 0, 0);

      // Convertir a blob
      canvas.toBlob((blob) => {
        setProcessedImage(blob);
        setIsProcessing(false);
      }, 'image/png', 0.9);

    } catch (err) {
      console.error('Error procesando imagen:', err);
      setError('Error al procesar la imagen. Inténtalo de nuevo.');
      setIsProcessing(false);
    }
  }, [selectedImage, backgroundColor]);

  // Función para crear imagen con fondo transparente (simulado)
  const createTransparentBackground = useCallback(async () => {
    if (!selectedImage) return;

    setIsProcessing(true);
    setError('');

    try {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();

      await new Promise((resolve, reject) => {
        img.onload = resolve;
        img.onerror = reject;
        img.src = URL.createObjectURL(selectedImage);
      });

      // Configurar canvas
      canvas.width = img.width;
      canvas.height = img.height;

      // Dibujar imagen original
      ctx.drawImage(img, 0, 0);

      // Aplicar un efecto simple de "eliminación de fondo"
      // Esto es una simulación - en una app real usarías IA
      const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
      const data = imageData.data;

      // Detectar color de fondo (esquinas) y hacerlo transparente
      const cornerColors = [
        [data[0], data[1], data[2]], // esquina superior izquierda
        [data[(canvas.width - 1) * 4], data[(canvas.width - 1) * 4 + 1], data[(canvas.width - 1) * 4 + 2]], // esquina superior derecha
      ];

      const tolerance = 30;

      for (let i = 0; i < data.length; i += 4) {
        const r = data[i];
        const g = data[i + 1];
        const b = data[i + 2];

        // Verificar si el pixel es similar al color de fondo
        for (const [cr, cg, cb] of cornerColors) {
          if (Math.abs(r - cr) < tolerance && Math.abs(g - cg) < tolerance && Math.abs(b - cb) < tolerance) {
            data[i + 3] = 0; // Hacer transparente
            break;
          }
        }
      }

      ctx.putImageData(imageData, 0, 0);

      // Convertir a blob
      canvas.toBlob((blob) => {
        setProcessedImage(blob);
        setIsProcessing(false);
      }, 'image/png', 0.9);

    } catch (err) {
      console.error('Error procesando imagen:', err);
      setError('Error al procesar la imagen. Inténtalo de nuevo.');
      setIsProcessing(false);
    }
  }, [selectedImage]);

  const handleDownload = useCallback(() => {
    if (processedImage) {
      const filename = `${selectedImage.name.split('.')[0]}-procesada.png`;
      downloadImage(processedImage, filename);
    }
  }, [processedImage, selectedImage]);

  const handleReset = useCallback(() => {
    setProcessedImage(null);
    setError('');
    onReset();
  }, [onReset]);

  if (!selectedImage) {
    return null;
  }

  return (
    <div className="space-y-6">
      {/* Botones de acción */}
      <div className="glass-effect rounded-xl p-6 animate-fadeInUp">
        <div className="space-y-4">
          {/* Selector de color de fondo */}
          <div className="flex items-center justify-center space-x-4">
            <label className="flex items-center space-x-2">
              <Palette className="w-5 h-5 text-gray-600" />
              <span className="text-sm font-medium text-gray-700">Color de fondo:</span>
              <input
                type="color"
                value={backgroundColor}
                onChange={(e) => setBackgroundColor(e.target.value)}
                className="w-8 h-8 rounded border border-gray-300 cursor-pointer"
              />
            </label>
          </div>

          {/* Botones de procesamiento */}
          <div className="flex flex-wrap gap-4 justify-center">
            {!processedImage && (
              <>
                <button
                  onClick={createTransparentBackground}
                  disabled={isProcessing}
                  className="btn-primary disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
                >
                  {isProcessing ? (
                    <>
                      <Loader2 className="w-5 h-5 animate-spin" />
                      <span>Procesando...</span>
                    </>
                  ) : (
                    <>
                      <Wand2 className="w-5 h-5" />
                      <span>Fondo transparente</span>
                    </>
                  )}
                </button>
                
                <button
                  onClick={processImageSimple}
                  disabled={isProcessing}
                  className="btn-secondary disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
                >
                  <Palette className="w-5 h-5" />
                  <span>Cambiar fondo</span>
                </button>
              </>
            )}
            
            {processedImage && (
              <>
                <button
                  onClick={handleDownload}
                  className="btn-primary flex items-center space-x-2"
                >
                  <Download className="w-5 h-5" />
                  <span>Descargar</span>
                </button>
                
                <button
                  onClick={handleReset}
                  className="btn-secondary flex items-center space-x-2"
                >
                  <RotateCcw className="w-5 h-5" />
                  <span>Nueva imagen</span>
                </button>
              </>
            )}
          </div>
        </div>
        
        {/* Error */}
        {error && (
          <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-red-700 text-sm">{error}</p>
          </div>
        )}
      </div>

      {/* Comparación de imágenes */}
      {processedImage && (
        <div className="glass-effect rounded-xl p-6 animate-fadeInUp">
          <h3 className="text-lg font-semibold text-gray-800 mb-4 text-center">
            Resultado
          </h3>
          
          <div className="grid md:grid-cols-2 gap-6">
            {/* Imagen original */}
            <div>
              <h4 className="text-sm font-medium text-gray-600 mb-2">Original</h4>
              <div className="relative">
                <img
                  src={URL.createObjectURL(selectedImage)}
                  alt="Imagen original"
                  className="w-full h-64 object-contain rounded-lg bg-gray-50 border"
                />
              </div>
            </div>
            
            {/* Imagen procesada */}
            <div>
              <h4 className="text-sm font-medium text-gray-600 mb-2">Procesada</h4>
              <div className="relative">
                <img
                  src={blobToImageUrl(processedImage)}
                  alt="Imagen procesada"
                  className="w-full h-64 object-contain rounded-lg border"
                  style={{
                    backgroundImage: `
                      linear-gradient(45deg, #f0f0f0 25%, transparent 25%),
                      linear-gradient(-45deg, #f0f0f0 25%, transparent 25%),
                      linear-gradient(45deg, transparent 75%, #f0f0f0 75%),
                      linear-gradient(-45deg, transparent 75%, #f0f0f0 75%)
                    `,
                    backgroundSize: '20px 20px',
                    backgroundPosition: '0 0, 0 10px, 10px -10px, -10px 0px'
                  }}
                />
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SimpleBackgroundRemover;
