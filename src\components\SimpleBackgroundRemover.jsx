import React, { useState, useCallback, useRef } from 'react';
import { Wand2, Loader2, Download, RotateCcw, Palette, Sciss<PERSON>, Zap } from 'lucide-react';
import { downloadImage, blobToImageUrl } from '../utils/imageUtils';
import {
  grabCutBackgroundRemoval,
  colorSegmentationRemoval,
  edgeBasedRemoval
} from '../utils/advancedImageProcessing';

const SimpleBackgroundRemover = ({ selectedImage, onReset }) => {
  const [isProcessing, setIsProcessing] = useState(false);
  const [processedImage, setProcessedImage] = useState(null);
  const [error, setError] = useState('');
  const [backgroundColor, setBackgroundColor] = useState('#ffffff');
  const [algorithm, setAlgorithm] = useState('grabcut');
  const [progress, setProgress] = useState(0);
  const canvasRef = useRef(null);

  // Función avanzada para eliminar fondo
  const processAdvancedRemoval = useCallback(async () => {
    if (!selectedImage) return;

    setIsProcessing(true);
    setError('');
    setProgress(0);

    try {
      setProgress(20);

      let processedBlob;
      const options = {
        tolerance: 30,
        iterations: 3,
        smoothing: true
      };

      setProgress(40);

      switch (algorithm) {
        case 'grabcut':
          processedBlob = await grabCutBackgroundRemoval(selectedImage, {
            ...options,
            edgeThreshold: 25
          });
          break;
        case 'color':
          processedBlob = await colorSegmentationRemoval(selectedImage, {
            ...options,
            samples: 'auto',
            feathering: 2
          });
          break;
        case 'edge':
          processedBlob = await edgeBasedRemoval(selectedImage, {
            threshold: 40,
            morphology: true,
            fillHoles: true
          });
          break;
        default:
          throw new Error('Algoritmo no válido');
      }

      setProgress(90);

      if (!processedBlob) {
        throw new Error('No se pudo procesar la imagen');
      }

      setProgress(100);
      setProcessedImage(processedBlob);

    } catch (err) {
      console.error('Error procesando imagen:', err);
      setError(`Error al procesar la imagen: ${err.message}`);
    } finally {
      setIsProcessing(false);
      setTimeout(() => setProgress(0), 1000);
    }
  }, [selectedImage, algorithm]);

  // Función para cambiar fondo por color sólido
  const changeBackgroundColor = useCallback(async () => {
    if (!selectedImage || !processedImage) {
      setError('Primero elimina el fondo, luego cambia el color.');
      return;
    }

    setIsProcessing(true);
    setError('');

    try {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();

      await new Promise((resolve, reject) => {
        img.onload = resolve;
        img.onerror = reject;
        img.src = blobToImageUrl(processedImage);
      });

      // Configurar canvas
      canvas.width = img.width;
      canvas.height = img.height;

      // Dibujar fondo de color
      ctx.fillStyle = backgroundColor;
      ctx.fillRect(0, 0, canvas.width, canvas.height);

      // Dibujar imagen sin fondo encima
      ctx.drawImage(img, 0, 0);

      // Convertir a blob
      canvas.toBlob((blob) => {
        setProcessedImage(blob);
        setIsProcessing(false);
      }, 'image/png', 0.9);

    } catch (err) {
      console.error('Error cambiando fondo:', err);
      setError('Error al cambiar el fondo. Inténtalo de nuevo.');
      setIsProcessing(false);
    }
  }, [selectedImage, processedImage, backgroundColor]);

  const handleDownload = useCallback(() => {
    if (processedImage) {
      const filename = `${selectedImage.name.split('.')[0]}-procesada.png`;
      downloadImage(processedImage, filename);
    }
  }, [processedImage, selectedImage]);

  const handleReset = useCallback(() => {
    setProcessedImage(null);
    setError('');
    onReset();
  }, [onReset]);

  if (!selectedImage) {
    return null;
  }

  return (
    <div className="space-y-6">
      {/* Botones de acción */}
      <div className="glass-effect rounded-xl p-6 animate-fadeInUp">
        <div className="space-y-6">
          {/* Selector de algoritmo */}
          <div className="text-center">
            <h4 className="text-sm font-medium text-gray-700 mb-3">Algoritmo de eliminación:</h4>
            <div className="flex flex-wrap gap-2 justify-center">
              <button
                onClick={() => setAlgorithm('grabcut')}
                className={`px-3 py-2 rounded-lg text-sm font-medium transition-all ${
                  algorithm === 'grabcut'
                    ? 'bg-blue-500 text-white shadow-md'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                <Scissors className="w-4 h-4 inline mr-1" />
                GrabCut
              </button>
              <button
                onClick={() => setAlgorithm('color')}
                className={`px-3 py-2 rounded-lg text-sm font-medium transition-all ${
                  algorithm === 'color'
                    ? 'bg-blue-500 text-white shadow-md'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                <Palette className="w-4 h-4 inline mr-1" />
                Por Color
              </button>
              <button
                onClick={() => setAlgorithm('edge')}
                className={`px-3 py-2 rounded-lg text-sm font-medium transition-all ${
                  algorithm === 'edge'
                    ? 'bg-blue-500 text-white shadow-md'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                <Zap className="w-4 h-4 inline mr-1" />
                Por Bordes
              </button>
            </div>
          </div>

          {/* Botones de procesamiento */}
          <div className="flex flex-wrap gap-4 justify-center">
            {!processedImage && (
              <button
                onClick={processAdvancedRemoval}
                disabled={isProcessing}
                className="btn-primary disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
              >
                {isProcessing ? (
                  <>
                    <Loader2 className="w-5 h-5 animate-spin" />
                    <span>Procesando...</span>
                  </>
                ) : (
                  <>
                    <Wand2 className="w-5 h-5" />
                    <span>Eliminar fondo</span>
                  </>
                )}
              </button>
            )}

            {processedImage && (
              <>
                <button
                  onClick={handleDownload}
                  className="btn-primary flex items-center space-x-2"
                >
                  <Download className="w-5 h-5" />
                  <span>Descargar</span>
                </button>

                <button
                  onClick={handleReset}
                  className="btn-secondary flex items-center space-x-2"
                >
                  <RotateCcw className="w-5 h-5" />
                  <span>Nueva imagen</span>
                </button>
              </>
            )}
          </div>

          {/* Selector de color de fondo (solo si hay imagen procesada) */}
          {processedImage && (
            <div className="border-t pt-4">
              <div className="flex items-center justify-center space-x-4 mb-3">
                <label className="flex items-center space-x-2">
                  <Palette className="w-5 h-5 text-gray-600" />
                  <span className="text-sm font-medium text-gray-700">Cambiar fondo:</span>
                  <input
                    type="color"
                    value={backgroundColor}
                    onChange={(e) => setBackgroundColor(e.target.value)}
                    className="w-8 h-8 rounded border border-gray-300 cursor-pointer"
                  />
                </label>
              </div>
              <div className="text-center">
                <button
                  onClick={changeBackgroundColor}
                  disabled={isProcessing}
                  className="btn-secondary disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2 mx-auto"
                >
                  <Palette className="w-4 h-4" />
                  <span>Aplicar color</span>
                </button>
              </div>
            </div>
          )}

          {/* Barra de progreso */}
          {isProcessing && progress > 0 && (
            <div className="mt-4">
              <div className="flex justify-between text-sm text-gray-600 mb-2">
                <span>Procesando con algoritmo avanzado...</span>
                <span>{progress}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="bg-gradient-to-r from-green-500 to-blue-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${progress}%` }}
                ></div>
              </div>
            </div>
          )}
        </div>
        
        {/* Error */}
        {error && (
          <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-red-700 text-sm">{error}</p>
          </div>
        )}
      </div>

      {/* Comparación de imágenes */}
      {processedImage && (
        <div className="glass-effect rounded-xl p-6 animate-fadeInUp">
          <h3 className="text-lg font-semibold text-gray-800 mb-4 text-center">
            Resultado
          </h3>
          
          <div className="grid md:grid-cols-2 gap-6">
            {/* Imagen original */}
            <div>
              <h4 className="text-sm font-medium text-gray-600 mb-2">Original</h4>
              <div className="relative">
                <img
                  src={URL.createObjectURL(selectedImage)}
                  alt="Imagen original"
                  className="w-full h-64 object-contain rounded-lg bg-gray-50 border"
                />
              </div>
            </div>
            
            {/* Imagen procesada */}
            <div>
              <h4 className="text-sm font-medium text-gray-600 mb-2">Procesada</h4>
              <div className="relative">
                <img
                  src={blobToImageUrl(processedImage)}
                  alt="Imagen procesada"
                  className="w-full h-64 object-contain rounded-lg border"
                  style={{
                    backgroundImage: `
                      linear-gradient(45deg, #f0f0f0 25%, transparent 25%),
                      linear-gradient(-45deg, #f0f0f0 25%, transparent 25%),
                      linear-gradient(45deg, transparent 75%, #f0f0f0 75%),
                      linear-gradient(-45deg, transparent 75%, #f0f0f0 75%)
                    `,
                    backgroundSize: '20px 20px',
                    backgroundPosition: '0 0, 0 10px, 10px -10px, -10px 0px'
                  }}
                />
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SimpleBackgroundRemover;
