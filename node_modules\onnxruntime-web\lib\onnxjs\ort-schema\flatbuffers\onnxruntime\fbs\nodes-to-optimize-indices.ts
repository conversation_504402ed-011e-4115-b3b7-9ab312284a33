// automatically generated by the FlatBuffers compiler, do not modify

/* eslint-disable @typescript-eslint/no-unused-vars, @typescript-eslint/no-explicit-any, @typescript-eslint/no-non-null-assertion */

import * as flatbuffers from 'flatbuffers';

/**
 * nodes to consider for a runtime optimization
 * see corresponding type in onnxruntime/core/graph/runtime_optimization_record.h
 */
export class NodesToOptimizeIndices {
  bb: flatbuffers.ByteBuffer | null = null;
  bb_pos = 0;
  __init(i: number, bb: flatbuffers.ByteBuffer): NodesToOptimizeIndices {
    this.bb_pos = i;
    this.bb = bb;
    return this;
  }

  static getRootAsNodesToOptimizeIndices(
    bb: flatbuffers.ByteBuffer,
    obj?: NodesToOptimizeIndices,
  ): NodesToOptimizeIndices {
    return (obj || new NodesToOptimizeIndices()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
  }

  static getSizePrefixedRootAsNodesToOptimizeIndices(
    bb: flatbuffers.ByteBuffer,
    obj?: NodesToOptimizeIndices,
  ): NodesToOptimizeIndices {
    bb.setPosition(bb.position() + flatbuffers.SIZE_PREFIX_LENGTH);
    return (obj || new NodesToOptimizeIndices()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
  }

  nodeIndices(index: number): number | null {
    const offset = this.bb!.__offset(this.bb_pos, 4);
    return offset ? this.bb!.readUint32(this.bb!.__vector(this.bb_pos + offset) + index * 4) : 0;
  }

  nodeIndicesLength(): number {
    const offset = this.bb!.__offset(this.bb_pos, 4);
    return offset ? this.bb!.__vector_len(this.bb_pos + offset) : 0;
  }

  nodeIndicesArray(): Uint32Array | null {
    const offset = this.bb!.__offset(this.bb_pos, 4);
    return offset
      ? new Uint32Array(
          this.bb!.bytes().buffer,
          this.bb!.bytes().byteOffset + this.bb!.__vector(this.bb_pos + offset),
          this.bb!.__vector_len(this.bb_pos + offset),
        )
      : null;
  }

  numInputs(): number {
    const offset = this.bb!.__offset(this.bb_pos, 6);
    return offset ? this.bb!.readUint32(this.bb_pos + offset) : 0;
  }

  numOutputs(): number {
    const offset = this.bb!.__offset(this.bb_pos, 8);
    return offset ? this.bb!.readUint32(this.bb_pos + offset) : 0;
  }

  hasVariadicInput(): boolean {
    const offset = this.bb!.__offset(this.bb_pos, 10);
    return offset ? !!this.bb!.readInt8(this.bb_pos + offset) : false;
  }

  hasVariadicOutput(): boolean {
    const offset = this.bb!.__offset(this.bb_pos, 12);
    return offset ? !!this.bb!.readInt8(this.bb_pos + offset) : false;
  }

  numVariadicInputs(): number {
    const offset = this.bb!.__offset(this.bb_pos, 14);
    return offset ? this.bb!.readUint32(this.bb_pos + offset) : 0;
  }

  numVariadicOutputs(): number {
    const offset = this.bb!.__offset(this.bb_pos, 16);
    return offset ? this.bb!.readUint32(this.bb_pos + offset) : 0;
  }

  static startNodesToOptimizeIndices(builder: flatbuffers.Builder) {
    builder.startObject(7);
  }

  static addNodeIndices(builder: flatbuffers.Builder, nodeIndicesOffset: flatbuffers.Offset) {
    builder.addFieldOffset(0, nodeIndicesOffset, 0);
  }

  static createNodeIndicesVector(builder: flatbuffers.Builder, data: number[] | Uint32Array): flatbuffers.Offset;
  /**
   * @deprecated This Uint8Array overload will be removed in the future.
   */
  static createNodeIndicesVector(builder: flatbuffers.Builder, data: number[] | Uint8Array): flatbuffers.Offset;
  static createNodeIndicesVector(
    builder: flatbuffers.Builder,
    data: number[] | Uint32Array | Uint8Array,
  ): flatbuffers.Offset {
    builder.startVector(4, data.length, 4);
    for (let i = data.length - 1; i >= 0; i--) {
      builder.addInt32(data[i]!);
    }
    return builder.endVector();
  }

  static startNodeIndicesVector(builder: flatbuffers.Builder, numElems: number) {
    builder.startVector(4, numElems, 4);
  }

  static addNumInputs(builder: flatbuffers.Builder, numInputs: number) {
    builder.addFieldInt32(1, numInputs, 0);
  }

  static addNumOutputs(builder: flatbuffers.Builder, numOutputs: number) {
    builder.addFieldInt32(2, numOutputs, 0);
  }

  static addHasVariadicInput(builder: flatbuffers.Builder, hasVariadicInput: boolean) {
    builder.addFieldInt8(3, +hasVariadicInput, +false);
  }

  static addHasVariadicOutput(builder: flatbuffers.Builder, hasVariadicOutput: boolean) {
    builder.addFieldInt8(4, +hasVariadicOutput, +false);
  }

  static addNumVariadicInputs(builder: flatbuffers.Builder, numVariadicInputs: number) {
    builder.addFieldInt32(5, numVariadicInputs, 0);
  }

  static addNumVariadicOutputs(builder: flatbuffers.Builder, numVariadicOutputs: number) {
    builder.addFieldInt32(6, numVariadicOutputs, 0);
  }

  static endNodesToOptimizeIndices(builder: flatbuffers.Builder): flatbuffers.Offset {
    const offset = builder.endObject();
    return offset;
  }

  static createNodesToOptimizeIndices(
    builder: flatbuffers.Builder,
    nodeIndicesOffset: flatbuffers.Offset,
    numInputs: number,
    numOutputs: number,
    hasVariadicInput: boolean,
    hasVariadicOutput: boolean,
    numVariadicInputs: number,
    numVariadicOutputs: number,
  ): flatbuffers.Offset {
    NodesToOptimizeIndices.startNodesToOptimizeIndices(builder);
    NodesToOptimizeIndices.addNodeIndices(builder, nodeIndicesOffset);
    NodesToOptimizeIndices.addNumInputs(builder, numInputs);
    NodesToOptimizeIndices.addNumOutputs(builder, numOutputs);
    NodesToOptimizeIndices.addHasVariadicInput(builder, hasVariadicInput);
    NodesToOptimizeIndices.addHasVariadicOutput(builder, hasVariadicOutput);
    NodesToOptimizeIndices.addNumVariadicInputs(builder, numVariadicInputs);
    NodesToOptimizeIndices.addNumVariadicOutputs(builder, numVariadicOutputs);
    return NodesToOptimizeIndices.endNodesToOptimizeIndices(builder);
  }
}
