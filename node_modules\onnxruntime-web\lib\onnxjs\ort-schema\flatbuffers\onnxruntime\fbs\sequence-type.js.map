{"version": 3, "file": "sequence-type.js", "sourceRoot": "", "sources": ["sequence-type.ts"], "names": [], "mappings": ";AAAA,qEAAqE;;;;;;;;;;;;;;;;;;;;;;;;;;AAErE,oIAAoI;AAEpI,yDAA2C;AAE3C,qEAA8D;AAE9D,MAAa,YAAY;IAAzB;QACE,OAAE,GAAkC,IAAI,CAAC;QACzC,WAAM,GAAG,CAAC,CAAC;IAuCb,CAAC;IAtCC,MAAM,CAAC,CAAS,EAAE,EAA0B;QAC1C,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;QAChB,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;QACb,OAAO,IAAI,CAAC;IACd,CAAC;IAED,MAAM,CAAC,qBAAqB,CAAC,EAA0B,EAAE,GAAkB;QACzE,OAAO,CAAC,GAAG,IAAI,IAAI,YAAY,EAAE,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,SAAS,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE,EAAE,EAAE,CAAC,CAAC;IAC7F,CAAC;IAED,MAAM,CAAC,iCAAiC,CAAC,EAA0B,EAAE,GAAkB;QACrF,EAAE,CAAC,WAAW,CAAC,EAAE,CAAC,QAAQ,EAAE,GAAG,WAAW,CAAC,kBAAkB,CAAC,CAAC;QAC/D,OAAO,CAAC,GAAG,IAAI,IAAI,YAAY,EAAE,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,SAAS,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE,EAAE,EAAE,CAAC,CAAC;IAC7F,CAAC;IAED,QAAQ,CAAC,GAAc;QACrB,MAAM,MAAM,GAAG,IAAI,CAAC,EAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QACjD,OAAO,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,IAAI,uBAAQ,EAAE,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,EAAG,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,EAAE,IAAI,CAAC,EAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IAC7G,CAAC;IAED,MAAM,CAAC,iBAAiB,CAAC,OAA4B;QACnD,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;IACzB,CAAC;IAED,MAAM,CAAC,WAAW,CAAC,OAA4B,EAAE,cAAkC;QACjF,OAAO,CAAC,cAAc,CAAC,CAAC,EAAE,cAAc,EAAE,CAAC,CAAC,CAAC;IAC/C,CAAC;IAED,MAAM,CAAC,eAAe,CAAC,OAA4B;QACjD,MAAM,MAAM,GAAG,OAAO,CAAC,SAAS,EAAE,CAAC;QACnC,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,MAAM,CAAC,kBAAkB,CAAC,OAA4B,EAAE,cAAkC;QACxF,YAAY,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;QACxC,YAAY,CAAC,WAAW,CAAC,OAAO,EAAE,cAAc,CAAC,CAAC;QAClD,OAAO,YAAY,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;IAC/C,CAAC;CACF;AAzCD,oCAyCC"}