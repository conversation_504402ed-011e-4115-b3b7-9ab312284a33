// automatically generated by the FlatBuffers compiler, do not modify

/* eslint-disable @typescript-eslint/no-unused-vars, @typescript-eslint/no-explicit-any, @typescript-eslint/no-non-null-assertion */

import * as flatbuffers from 'flatbuffers';

import { ArgType } from '../../onnxruntime/fbs/arg-type.js';

export class ArgTypeAndIndex {
  bb: flatbuffers.ByteBuffer | null = null;
  bb_pos = 0;
  __init(i: number, bb: flatbuffers.ByteBuffer): ArgTypeAndIndex {
    this.bb_pos = i;
    this.bb = bb;
    return this;
  }

  static getRootAsArgTypeAndIndex(bb: flatbuffers.ByteBuffer, obj?: ArgTypeAndIndex): ArgTypeAndIndex {
    return (obj || new ArgTypeAndIndex()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
  }

  static getSizePrefixedRootAsArgTypeAndIndex(bb: flatbuffers.ByteBuffer, obj?: ArgTypeAndIndex): ArgTypeAndIndex {
    bb.setPosition(bb.position() + flatbuffers.SIZE_PREFIX_LENGTH);
    return (obj || new ArgTypeAndIndex()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
  }

  argType(): ArgType {
    const offset = this.bb!.__offset(this.bb_pos, 4);
    return offset ? this.bb!.readInt8(this.bb_pos + offset) : ArgType.INPUT;
  }

  index(): number {
    const offset = this.bb!.__offset(this.bb_pos, 6);
    return offset ? this.bb!.readUint32(this.bb_pos + offset) : 0;
  }

  static startArgTypeAndIndex(builder: flatbuffers.Builder) {
    builder.startObject(2);
  }

  static addArgType(builder: flatbuffers.Builder, argType: ArgType) {
    builder.addFieldInt8(0, argType, ArgType.INPUT);
  }

  static addIndex(builder: flatbuffers.Builder, index: number) {
    builder.addFieldInt32(1, index, 0);
  }

  static endArgTypeAndIndex(builder: flatbuffers.Builder): flatbuffers.Offset {
    const offset = builder.endObject();
    return offset;
  }

  static createArgTypeAndIndex(builder: flatbuffers.Builder, argType: ArgType, index: number): flatbuffers.Offset {
    ArgTypeAndIndex.startArgTypeAndIndex(builder);
    ArgTypeAndIndex.addArgType(builder, argType);
    ArgTypeAndIndex.addIndex(builder, index);
    return ArgTypeAndIndex.endArgTypeAndIndex(builder);
  }
}
