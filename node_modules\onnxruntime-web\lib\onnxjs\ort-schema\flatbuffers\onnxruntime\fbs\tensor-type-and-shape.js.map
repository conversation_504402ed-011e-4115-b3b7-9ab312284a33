{"version": 3, "file": "tensor-type-and-shape.js", "sourceRoot": "", "sources": ["tensor-type-and-shape.ts"], "names": [], "mappings": ";AAAA,qEAAqE;;;;;;;;;;;;;;;;;;;;;;;;;;AAErE,oIAAoI;AAEpI,yDAA2C;AAE3C,6DAAuD;AACvD,mFAA2E;AAE3E,MAAa,kBAAkB;IAA/B;QACE,OAAE,GAAkC,IAAI,CAAC;QACzC,WAAM,GAAG,CAAC,CAAC;IA6Cb,CAAC;IA5CC,MAAM,CAAC,CAAS,EAAE,EAA0B;QAC1C,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;QAChB,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;QACb,OAAO,IAAI,CAAC;IACd,CAAC;IAED,MAAM,CAAC,2BAA2B,CAAC,EAA0B,EAAE,GAAwB;QACrF,OAAO,CAAC,GAAG,IAAI,IAAI,kBAAkB,EAAE,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,SAAS,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE,EAAE,EAAE,CAAC,CAAC;IACnG,CAAC;IAED,MAAM,CAAC,uCAAuC,CAC5C,EAA0B,EAC1B,GAAwB;QAExB,EAAE,CAAC,WAAW,CAAC,EAAE,CAAC,QAAQ,EAAE,GAAG,WAAW,CAAC,kBAAkB,CAAC,CAAC;QAC/D,OAAO,CAAC,GAAG,IAAI,IAAI,kBAAkB,EAAE,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,SAAS,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE,EAAE,EAAE,CAAC,CAAC;IACnG,CAAC;IAED,QAAQ;QACN,MAAM,MAAM,GAAG,IAAI,CAAC,EAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QACjD,OAAO,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,EAAG,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,oCAAc,CAAC,SAAS,CAAC;IACtF,CAAC;IAED,KAAK,CAAC,GAAW;QACf,MAAM,MAAM,GAAG,IAAI,CAAC,EAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QACjD,OAAO,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,IAAI,gBAAK,EAAE,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,EAAG,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,EAAE,IAAI,CAAC,EAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IAC1G,CAAC;IAED,MAAM,CAAC,uBAAuB,CAAC,OAA4B;QACzD,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;IACzB,CAAC;IAED,MAAM,CAAC,WAAW,CAAC,OAA4B,EAAE,QAAwB;QACvE,OAAO,CAAC,aAAa,CAAC,CAAC,EAAE,QAAQ,EAAE,oCAAc,CAAC,SAAS,CAAC,CAAC;IAC/D,CAAC;IAED,MAAM,CAAC,QAAQ,CAAC,OAA4B,EAAE,WAA+B;QAC3E,OAAO,CAAC,cAAc,CAAC,CAAC,EAAE,WAAW,EAAE,CAAC,CAAC,CAAC;IAC5C,CAAC;IAED,MAAM,CAAC,qBAAqB,CAAC,OAA4B;QACvD,MAAM,MAAM,GAAG,OAAO,CAAC,SAAS,EAAE,CAAC;QACnC,OAAO,MAAM,CAAC;IAChB,CAAC;CACF;AA/CD,gDA+CC"}