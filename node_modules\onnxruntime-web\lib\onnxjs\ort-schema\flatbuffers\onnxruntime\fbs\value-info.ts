// automatically generated by the FlatBuffers compiler, do not modify

/* eslint-disable @typescript-eslint/no-unused-vars, @typescript-eslint/no-explicit-any, @typescript-eslint/no-non-null-assertion */

import * as flatbuffers from 'flatbuffers';

import { TypeInfo } from '../../onnxruntime/fbs/type-info.js';

export class ValueInfo {
  bb: flatbuffers.ByteBuffer | null = null;
  bb_pos = 0;
  __init(i: number, bb: flatbuffers.ByteBuffer): ValueInfo {
    this.bb_pos = i;
    this.bb = bb;
    return this;
  }

  static getRootAsValueInfo(bb: flatbuffers.ByteBuffer, obj?: ValueInfo): ValueInfo {
    return (obj || new ValueInfo()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
  }

  static getSizePrefixedRootAsValueInfo(bb: flatbuffers.ByteBuffer, obj?: ValueInfo): ValueInfo {
    bb.setPosition(bb.position() + flatbuffers.SIZE_PREFIX_LENGTH);
    return (obj || new ValueInfo()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
  }

  name(): string | null;
  name(optionalEncoding: flatbuffers.Encoding): string | Uint8Array | null;
  name(optionalEncoding?: any): string | Uint8Array | null {
    const offset = this.bb!.__offset(this.bb_pos, 4);
    return offset ? this.bb!.__string(this.bb_pos + offset, optionalEncoding) : null;
  }

  docString(): string | null;
  docString(optionalEncoding: flatbuffers.Encoding): string | Uint8Array | null;
  docString(optionalEncoding?: any): string | Uint8Array | null {
    const offset = this.bb!.__offset(this.bb_pos, 6);
    return offset ? this.bb!.__string(this.bb_pos + offset, optionalEncoding) : null;
  }

  type(obj?: TypeInfo): TypeInfo | null {
    const offset = this.bb!.__offset(this.bb_pos, 8);
    return offset ? (obj || new TypeInfo()).__init(this.bb!.__indirect(this.bb_pos + offset), this.bb!) : null;
  }

  static startValueInfo(builder: flatbuffers.Builder) {
    builder.startObject(3);
  }

  static addName(builder: flatbuffers.Builder, nameOffset: flatbuffers.Offset) {
    builder.addFieldOffset(0, nameOffset, 0);
  }

  static addDocString(builder: flatbuffers.Builder, docStringOffset: flatbuffers.Offset) {
    builder.addFieldOffset(1, docStringOffset, 0);
  }

  static addType(builder: flatbuffers.Builder, typeOffset: flatbuffers.Offset) {
    builder.addFieldOffset(2, typeOffset, 0);
  }

  static endValueInfo(builder: flatbuffers.Builder): flatbuffers.Offset {
    const offset = builder.endObject();
    return offset;
  }
}
